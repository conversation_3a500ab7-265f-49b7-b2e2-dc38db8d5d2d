using System;
using System.Collections.Generic;

namespace CasualGame.lib_ChuagnYi.NeeG.Pool
{
    public static class HashSetPool<T>
    {
        private static readonly object              @lock = new object();
        private static readonly Stack<HashSet<T>>   free  = new Stack<HashSet<T>>();
        private static readonly HashSet<HashSet<T>> busy  = new HashSet<HashSet<T>>();

        public static HashSet<T> New()
        {
            lock (@lock)
            {
                if (free.Count == 0) free.Push(new HashSet<T>());

                var HashSet = free.Pop();

                busy.Add(HashSet);

                return HashSet;
            }
        }

        public static void Free(HashSet<T> HashSet)
        {
            lock (@lock)
            {
                if (!busy.Remove(HashSet))
                    throw new ArgumentException("The polyh set to free is not in use by the pool.", nameof(HashSet));

                HashSet.Clear();

                free.Push(HashSet);
            }
        }
    }

    public static class XHashSetPool
    {
        public static HashSet<T> ToHashSetPooled<T>(this IEnumerable<T> source)
        {
            var HashSet = HashSetPool<T>.New();

            foreach (var item in source) HashSet.Add(item);

            return HashSet;
        }

        public static void Free<T>(this HashSet<T> HashSet)
        {
            HashSetPool<T>.Free(HashSet);
        }
    }
}