using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    // /// <summary>
    // /// 实现Idelegate的类，应在生命周期结束时候调用UnSubALl来自动取消注册
    // /// </summary>
    // public interface IDelegate { }

    /// <summary>
    ///     Nee框架下的MonoBehaviour
    /// </summary>
    public class NeeMono : MonoBehaviour, IEventSuber
    {
#if UNITY_EDITOR
        public string Description;
#endif
        [HideInInspector] public bool _eventSuber = false;

        public bool isEventSuber
        {
            get { return _eventSuber; }
            set { _eventSuber = value; }
        }

        public int GetSuberId()
        {
            return this.GetInstanceID();
        }

        protected virtual void OnDestroy()
        {
            if (isEventSuber && NeeGame.Instance)
                NeeGame.GetMgr<DelegateMgr>()?.UnSubAll(this);
        }

        [Button]
        public virtual void SyncName()
        {
            gameObject.name = GetType().Name;
        }
    }

    public class NeeMonoAsync : Nee<PERSON>ono, INeeAsync
    {
        [SerializeField] public bool isAsyncInited { get; set; }

        public virtual void SetAysncInitBool(bool bo)
        {
            isAsyncInited = bo;
            if (bo)
            {
                onAsyncInited?.Invoke();
                onAsyncInited = null;
            }
        }

        public virtual void RegisterOnAsyncInited(Action a)
        {
            onAsyncInited += a;
            if (isAsyncInited)
            {
                onAsyncInited?.Invoke();
                onAsyncInited = null;
            }
        }

        protected event Action onAsyncInited;
    }
}