using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;
using Object = System.Object;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public abstract class NeeMonoSingleton : NeeMono
    {
    }

    /// <summary>
    ///     需要面板或管理生命周期的Mgr
    /// </summary>
    /// <typeparam name="T"></typeparam>
    [ScrptsOrder(EScriptsOrder.MonoSingleton)]
    public class NeeMonoSingleton<T> : NeeMonoSingleton, INeeAsync, INeeSingleton where T : NeeMono, INeeSingleton
    {
        private static T _instance;

        private static object _lock = new object();

        private static bool applicationIsQuitting;

        protected static T _Instance
        {
            get
            {
                if (applicationIsQuitting) return null;

                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = (T) NeeGame.Instance.GetComponentInChildren(typeof(T));

                        if (NeeGame.Instance.GetComponentsInChildren(typeof(T)).Length > 1) return _instance;

                        if (_instance == null)
                        {
                            var singleton = new GameObject();
                            _instance      = singleton.AddComponent<T>();
                            singleton.name = "(singleton) " + typeof(T);

                            DontDestroyOnLoad(singleton);
                        }
                    }

                    return _instance;
                }
            }
        }

        protected override void OnDestroy()
        {
            NeeGame.GetMgr<DelegateMgr>()?.UnSubAll(this);
            applicationIsQuitting = true;
        }

        // protected virtual void Awake() {
        //Game.AddMgr<T>(this as T);
        // Application.quitting += OnQuitting;
        // }

        protected virtual void OnApplicationQuit()
        {
            _instance             = null;
            _lock                 = new object();
            applicationIsQuitting = false;
            // Application.quitting -= OnQuitting;
        }

        [Button]
        public override void SyncName()
        {
            gameObject.name = GetType().Name;
        }

        public List<Object> refers;

        #region Async

        public virtual bool  needAysncInit => false;
        public         bool  isAsyncInited { get; set; }
        private event Action onAsyncInited;

        public void SetAysncInitBool(bool bo)
        {
            isAsyncInited = bo;
            if (needAysncInit && NeeGame.Instance)
                NeeGame.SetInitBool(this, bo);

            if (bo)
            {
                onAsyncInited?.Invoke();
                onAsyncInited = null;
            }
        }

        public void RegisterOnAsyncInited(Action a)
        {
            onAsyncInited += a;
            if (needAysncInit == false || isAsyncInited)
            {
                onAsyncInited?.Invoke();
                onAsyncInited = null;
            }
        }

        #endregion
    }
}