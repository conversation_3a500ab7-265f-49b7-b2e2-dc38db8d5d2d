using UnityEngine;
using UnityEditor;
#if UNITY_2022_1_OR_NEWER
using UnityEditor.SceneManagement;
#else
using UnityEditor.Experimental.SceneManagement;
#endif
using System;
using System.IO;

[System.Serializable]

public class BuildMaincityMapFormat : EditorWindow
{

    [MenuItem("JK Tools/BuildMaincityMap Format")]
    public static void ShowWindow()
    {
        GetWindow<BuildMaincityMapFormat>("BuildMaincityMap Format");
    }

    private void OnGUI()
    {
        if (GUILayout.Button("Format"))
        {
            format();
        }
    }

    private void _format(Transform decorates, StreamWriter writer, ref int index, int bg=0)
    {
        foreach(Transform decorate in decorates)
        {
            Debug.Log($"{decorate.name} {PrefabUtility.IsPartOfPrefabInstance(decorate.gameObject)}");
            if(PrefabUtility.IsPartOfPrefabInstance(decorate.gameObject))
            {
                if(decorate.gameObject.activeSelf)
                {
                    string name = decorate.name;
                    float _x = decorate.localPosition.x - 0.5f;
                    float _y = decorate.localPosition.z - 0.5f;
                    float x = (int)_x;
                    float y = (int)_y;
                    int _angel = Mathf.RoundToInt(decorate.localEulerAngles.y);
                    string angel = _angel == 0 ? "" : $"{_angel}";
                    string BeautifyRes = AssetDatabase.GetAssetPath(PrefabUtility.GetCorrespondingObjectFromSource(decorate.gameObject)).ToLower().Replace("assets/","");
                    float ResScale = Mathf.Round(decorate.localScale.x * 100f) / 100f;
                    string offset = x == _x && y == _y ? "" : $"{Mathf.Round((_x-x)*100f)/100f}#0#{Mathf.Round((_y-y)*100f)/100f}";
                    string layer = bg == 0 ? "" : $"{bg}";

                    Debug.Log($"{name} x={x} y={y} BeautifyRes={BeautifyRes} ResScale={ResScale} offset={offset}");
                    
                    if(int.TryParse(name, out int mapID))
                    {
                        writer.WriteLine($"{mapID},3,,,{x},{y},,{angel},,,,,{BeautifyRes},{ResScale},{offset},,,,,{layer}");
                    }
                    else
                    {
                        writer.WriteLine($"{index},3,,,{x},{y},,{angel},,,,,{BeautifyRes},{ResScale},{offset},,,,,{layer}");
                        index += 1;
                    }
                }
            }
            else
            {
                _format(decorate, writer, ref index, bg);
            }
        }
    }

    public void format()
    {
        var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
        if (prefabStage == null)
        {
            EditorUtility.DisplayDialog("错误", "请在预制体编辑模式下运行此工具。", "确定");
            return;
        }

        GameObject prefabInstance = prefabStage.prefabContentsRoot;

        Transform panel = prefabInstance.transform.Find("panel");

        // 打开保存文件面板，允许用户选择保存路径
        string filePath = EditorUtility.SaveFilePanel("Save CSV File", "", "BuildMaincityMap.csv", "csv");

        // 创建一个 CSV 文件并写入数据
        using (StreamWriter writer = new StreamWriter(filePath))
        {
            // 写入表头
            writer.WriteLine("{name}MapID,type,name,buildingID,x,y,towards,angel,broken,FixCondition,FixDes,Hiddenbuildings,BeautifyRes,ResScale,offset,RegionArea,RegionEvent,bubbleAppearID,bubbleID,layer");
            writer.WriteLine("{type}int32,int32,string,int32,int32,int32,int32,int32,int32,string,int32,int32,string,float,float,int32,int32,int32,int32,int32");
            writer.WriteLine("{desc}单位ID,单位类型（1建筑 2事件 3地图装饰 4新手事件）,单位备注,id,x坐标,y坐标,建筑朝向,事件模型角度（类型2：上180下0左90右270）,是否为破损状态,修复条件（条件类型1 建筑id#建筑等级  条件类型2 关卡id  条件类型3 小游戏关卡id  条件类型4 礼包id）,修复描述,建筑隐藏（针对扩建区解锁的建筑，地图创建时要隐藏）,装饰资源路径,资源缩放,资源偏移,装饰区域（随区域扩建清除）,单位显示（完成事件隐藏1#事件类型（1普通事件2新手事件）#事件ID；完成事件显示2#事件类型（1普通事件2新手事件）#事件ID）,完成事件后地图单位出气泡：事件类型（普通事件1/新手事件2）#事件id,完成事件后地图单位出气泡（langID#头像；不配则不显示）,地表单位层级（1地表2泥巴路3城建地表4道路#子层级，不配则走通用逻辑）");
            writer.WriteLine("{filter}sc,sc,,sc,sc,sc,c,c,sc,sc,c,s,c,c,c,c,c,c,c,c");
            writer.WriteLine("{array},,,sc,,,,,,,,,,,c,,c,c,c,c");
            writer.WriteLine("{arraytype},,,int32,,,,,,,,,,,float,,int32,int32,int32,int32");
            writer.WriteLine("{index}1,,,,,,,,,,,,,,,,,,,");

            int index = 10001;
            // 写入数据行
            Transform entity2 = panel.Find("entity2");
            _format(entity2, writer, ref index);

            Transform bg2 = panel.Find("bg2");
            _format(bg2, writer, ref index, 2);

            Transform bg4 = panel.Find("bg4");
            _format(bg4, writer, ref index, 4);
        }

        Debug.Log("CSV 文件创建成功: " + filePath);
    }
}
