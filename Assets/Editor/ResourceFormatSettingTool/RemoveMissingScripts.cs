using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using SuperTools;
using System.IO;
using System.Text;

public class RemoveMissingScripts : EditorWindow
{
    private string folderPath = "Assets";
    private List<string> modifiedPrefabs = new List<string>();
    private string recordsFolder;

    [SuperTMenuItem(EMenuType.Main, "SToolCollection/Tools/UI/检查预制体脚本丢失")]
    public static void ShowWindow()
    {
        GetWindow<RemoveMissingScripts>("Remove Missing Scripts");
    }

    private void OnEnable()
    {
        recordsFolder = Path.Combine(Application.dataPath, "../Temp/RemoveMissingScriptsRecords");
        if (!Directory.Exists(recordsFolder))
        {
            Directory.CreateDirectory(recordsFolder);
        }
    }

    private void OnGUI()
    {
        GUILayout.Label("Select Folder to Process", EditorStyles.boldLabel);
        folderPath = EditorGUILayout.TextField("Folder Path", folderPath);

        if (GUILayout.Button("Remove Missing Scripts"))
        {
            RemoveMissingScriptsInFolder(folderPath);
        }

        if (GUILayout.Button("Open Records Folder"))
        {
            OpenRecordsFolder();
        }
    }

    private void RemoveMissingScriptsInFolder(string path)
    {
        modifiedPrefabs.Clear();

        var prefabGuids = AssetDatabase.FindAssets("t:Prefab", new[] { path });
        foreach (var prefabGuid in prefabGuids)
        {
            var prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);

            var instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;

            if (RemoveMissingScriptsFromGameObject(instance))
            {
                PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                Debug.LogWarning($"[RemoveMissingScripts] Successfully removed missing scripts from {prefabPath}");
                modifiedPrefabs.Add(prefabPath);
            }
            else
            {
                Debug.Log($"[RemoveMissingScripts] No missing scripts found in {prefabPath}");
            }

            DestroyImmediate(instance);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        SaveRecordFile();
    }

    private bool RemoveMissingScriptsFromGameObject(GameObject gameObject)
    {
        bool hasRemoved = false;

        var components = gameObject.GetComponents<Component>();

        foreach (var comp in components)
        {
            if (comp == null)
            {
                GameObjectUtility.RemoveMonoBehavioursWithMissingScript(gameObject);
                hasRemoved = true;
                break;
            }
        }

        foreach (Transform child in gameObject.transform)
        {
            if (RemoveMissingScriptsFromGameObject(child.gameObject))
            {
                hasRemoved = true;
            }
        }

        return hasRemoved;
    }

    private void SaveRecordFile()
    {
        if (modifiedPrefabs.Count == 0)
        {
            Debug.Log("[RemoveMissingScripts] No prefabs were modified, so no record file generated.");
            return;
        }

        string timestamp = System.DateTime.Now.ToString("yyyyMMdd_HHmmss");
        string filePath = Path.Combine(recordsFolder, $"RemovedMissingScripts_{timestamp}.txt");

        StringBuilder sb = new StringBuilder();
        sb.AppendLine("Removed missing scripts from following prefabs:");
        sb.AppendLine($"Operation Time: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine("------------------------------------------------------");

        foreach (var prefabPath in modifiedPrefabs)
        {
            sb.AppendLine(prefabPath);
        }

        try
        {
            File.WriteAllText(filePath, sb.ToString(), Encoding.UTF8);
            Debug.Log($"[RemoveMissingScripts] Record file saved at: {filePath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError("[RemoveMissingScripts] Failed to save record file: " + e.Message);
        }
    }

    private void OpenRecordsFolder()
    {
        if (Directory.Exists(recordsFolder))
        {
            EditorUtility.RevealInFinder(recordsFolder);
        }
        else
        {
            Debug.LogWarning("[RemoveMissingScripts] Records folder does not exist.");
        }
    }
}

