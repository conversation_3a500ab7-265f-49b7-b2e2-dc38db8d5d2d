using UnityEditor;
using UnityEngine;
using System.IO;
using Common_Util;
using SuperTools;
using TMPro;
using UnityEngine.UI;
#if UNITY_2022_1_OR_NEWER
using UnityEditor.SceneManagement;
#else
using UnityEditor.Experimental.SceneManagement;
#endif

public class SpriteConverter : EditorWindow
{
    [SuperTMenuItem(EMenuType.Main, "Assets/设置资源/重设图片packingTag")]
    public static void ResetPackingTag()
    {
        foreach (var selectedObject in Selection.objects)
        {
            string path = AssetDatabase.GetAssetPath(selectedObject);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                // 清空Packing Tag
                importer.spritePackingTag = string.Empty;
                // 仅保存更改，不影响其他属性
                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();
            }
        }
    }
    
    [SuperTMenuItem(EMenuType.Main, "Assets/设置资源/设置图片为Sprite类型")]
    private static void ConvertImagesToSprites()
    {
        Object[] selectedObjects = Selection.objects;
        if (selectedObjects == null || selectedObjects.Length == 0)
        {
            return;
        }

        foreach (var obj in selectedObjects)
        {
            // 获取所选项的路径
            string selectedPath = AssetDatabase.GetAssetPath(obj);
            ConvertImagesToSpritesByAssetPath(selectedPath, false);
        }

        AssetDatabase.Refresh();
    }

    private static void ConvertImagesToSpritesByAssetPath(string selectedPath, bool refreshDataBase = true)
    {
        // 如果选中的项是文件夹，获取该文件夹的路径
        if (Directory.Exists(selectedPath))
        {
            ConvertFolderImagesToSprites(selectedPath, refreshDataBase);
        }
        // 如果选中的项是文件，直接转换该文件
        else if (File.Exists(selectedPath))
        {
            ConvertFileToSprite(selectedPath);
        }
        else
        {
            Debug.LogWarning("Please select a folder or an image file.");
        }

        if (!refreshDataBase)
        {
            AssetDatabase.Refresh();
        }
    }


    private static void ConvertFolderImagesToSprites(string folderPath, bool refreshDataBase = true)
    {
        // 在文件夹内递归查找PNG、JPG等图片文件
        string[] files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            ConvertFileToSprite(file);
        }

        if (refreshDataBase)
        {
            AssetDatabase.Refresh();
        }

        Debug.Log("All conversions in the folder completed!");
    }

    private static void ConvertFileToSprite(string filePath)
    {
        string extension = Path.GetExtension(filePath).ToLower();
        if (extension == ".png" || extension == ".jpg" || extension == ".jpeg" || extension == ".tga")
        {
            string assetPath = filePath.Replace(Application.dataPath, "Assets");

            // 获取TextureImporter并设置为Sprite
            TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
            if (textureImporter != null)
            {
                if (textureImporter.textureType == TextureImporterType.Default)
                {
                    textureImporter.textureType = TextureImporterType.Sprite;
                    textureImporter.SaveAndReimport();
                    Debug.Log($"Converted {assetPath} to Sprite.");
                }
                else
                {
                    Debug.Log($"Skipped {assetPath}: already of type {textureImporter.textureType}.");
                }
            }
            else
            {
                Debug.LogWarning($"Could not get TextureImporter for {assetPath}");
            }
        }
        else
        {
            Debug.Log($"Skipping non-image file: {filePath}");
        }
    }

    [SuperTMenuItem(EMenuType.Main, "Assets/设置资源/清空预制体文本")]
    private static void ClearText()
    {
        // 获取选中的对象
        Object selectedObject = Selection.activeObject;

        if (selectedObject == null)
        {
            Debug.LogWarning("请先选择一个预制体或文件夹");
            return;
        }

        // 检查选中的对象是预制体资产还是文件夹
        string assetPath = AssetDatabase.GetAssetPath(selectedObject);

        if (Directory.Exists(assetPath))
        {
            // 如果是文件夹，递归查找文件夹中的所有预制体
            string[] prefabFiles = Directory.GetFiles(assetPath, "*.prefab", SearchOption.AllDirectories);
            foreach (string prefabFile in prefabFiles)
            {
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabFile);
                if (prefab != null)
                {
                    ClearTextInPrefabAsset(prefab);
                }
            }

            Debug.Log($"已清空文件夹 '{assetPath}' 下所有预制体的文本。");
        }
        else
        {
            // 如果是单个预制体，处理该预制体
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            if (prefab != null)
            {
                ClearTextInPrefabAsset(prefab);
                Debug.Log($"已清空预制体 '{prefab.name}' 的文本。");
            }
            else
            {
                Debug.LogWarning("选择的对象不是正确的预制体或文件夹。");
            }
        }
    }

    private static void ClearTextInPrefabAsset(GameObject prefab)
    {
        // 获取所有 Text 组件并清空文本内容
        Text[] textComponents = prefab.GetComponentsInChildren<Text>(true);
        foreach (Text text in textComponents)
        {
            text.text = string.Empty;
        }

        TextMeshPro[] textMeshProComponents = prefab.GetComponentsInChildren<TextMeshPro>(true);
        foreach (TextMeshPro text in textMeshProComponents)
        {
            text.text = string.Empty;
        }

        TextMeshProUGUI[] textMeshUGUIProComponents = prefab.GetComponentsInChildren<TextMeshProUGUI>(true);
        foreach (TextMeshProUGUI text in textMeshUGUIProComponents)
        {
            text.text = string.Empty;
        }

        Outline[] outlines = prefab.GetComponentsInChildren<Outline>(true);
        foreach (Outline outline in outlines)
        {
            if (outline.effectDistance.x < 1.5f)
            {
                int distance = 2;
                outline.effectDistance = Vector2.one * distance * 3 / 4;
            }
        }

        // 保存预制体资产
        if (prefab != null)
        {
            // 保存更改
            PrefabUtility.SavePrefabAsset(prefab);
            Debug.Log($"已保存预制体 '{prefab.name}' 的更改。");
        }
    }

    [SuperTMenuItem(SuperTools.EMenuType.Main, "GameObject/蓝湖/图片设为透明 %#&1", false, 1)]
    public static void SetImageDefult()
    {
        bool isDirty = false;
        // 获取当前所有选中的对象
        GameObject[] selectedObjects = Selection.gameObjects;
        foreach (var obj in selectedObjects)
        {
            bool dirty = SetImageDefult(obj);
            if (dirty)
            {
                EditorUtility.SetDirty(obj);
                isDirty = true;
            }
        }
        if (isDirty)
        {
            // 如果是场景对象，标记场景脏状态
            UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(
                UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene());
        }
    }

    public static bool SetImageDefult(GameObject selectedObject)
    {
        string imagePath = "Assets/UI/GWUICommon/UIDefault.png";
        bool isDirty = false;
        if (selectedObject == null)
        {
            Debug.LogWarning("请先选择一个带有Image组件的游戏对象。");
            return isDirty;
        }

        // 获取该对象的Image组件
        Image imageComponent = selectedObject.GetComponent<Image>();

        if (imageComponent != null)
        {
            // 加载Sprite
            Sprite newSprite = AssetDatabase.LoadAssetAtPath<Sprite>(imagePath);
            if (newSprite != null)
            {
                // 设置Image组件的sprite
                imageComponent.sprite = newSprite;
                isDirty = true;
                Debug.Log($"已将 Image 组件的图片更改为: {imagePath}");
            }
        }

        RawImage rawImageComponent = selectedObject.GetComponent<RawImage>();
        if (rawImageComponent != null)
        {
            Texture newTexture = AssetDatabase.LoadAssetAtPath<Texture>(imagePath); // 也可以直接加载Texture
            if (newTexture != null)
            {
                rawImageComponent.texture = newTexture;
                isDirty = true;
                Debug.Log($"已将 RawImage 组件的图片更改为: {imagePath}");
            }
        }

        SpriteRenderer spriteRendererComponent = selectedObject.GetComponent<SpriteRenderer>();
        if (spriteRendererComponent != null)
        {
            Sprite newSprite = AssetDatabase.LoadAssetAtPath<Sprite>(imagePath);
            if (newSprite != null)
            {
                // 设置Image组件的sprite
                spriteRendererComponent.sprite = newSprite;
                isDirty = true;
                Debug.Log($"已将 Image 组件的图片更改为: {imagePath}");
            }
        }
        return isDirty;
    }

    // 添加上下文菜单项目
    [SuperTMenuItem(SuperTools.EMenuType.Main,
        ("CONTEXT/Image/Set Image From Path %#&1"))] // %s means Ctrl + S or Cmd + S on Mac
    private static void SetImageFromContext(MenuCommand command)
    {
        SetImageDefult();
    }

    [SuperTMenuItem(SuperTools.EMenuType.Main, "GameObject/蓝湖/沙漠风暴_地图还原")]
    public static void ResetStormMapBg()
    {
        var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
        if (prefabStage != null && prefabStage.prefabContentsRoot!=null)
        {
            Transform bg0 = prefabStage.prefabContentsRoot.transform.Find("bg0");
            if (bg0)
            {
                string imagePath = "Assets/UI/GWStormBattleMap/";
                for (int i = 0; i < bg0.childCount-1; i++)
                {
                    Transform selectedObject = bg0.GetChild(i);
                   
                    SpriteRenderer spriteRendererComponent = selectedObject.GetComponent<SpriteRenderer>();
                    if (spriteRendererComponent != null)
                    {
                        string tempStr = imagePath + i + ".png";
                        Sprite newSprite = AssetDatabase.LoadAssetAtPath<Sprite>(tempStr);
                        if (newSprite != null)
                        {
                            // 设置Image组件的sprite
                            spriteRendererComponent.sprite = newSprite;
                            EditorUtility.SetDirty(selectedObject);
                        }
                    }
                }
            }
        }
    }
}