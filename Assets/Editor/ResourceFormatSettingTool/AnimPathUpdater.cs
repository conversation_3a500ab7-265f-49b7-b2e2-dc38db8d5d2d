#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using SuperTools;

public class AnimPathUpdater : EditorWindow
{
    private AnimationClip animClip;
    private Vector2 scrollPos;
    private List<string> pathList = new List<string>();
    private List<NameRule> nameRules = new List<NameRule>();
    private GameObject targetPrefab;
    [SuperTMenuItem(EMenuType.Main,"SToolCollection/Tools/UI/动画工具/智能路径修复器")]
    static void ShowWindow()
    {
        GetWindow<AnimPathUpdater>("路径修复器 v4.0");
    }

    void OnGUI()
    {
        // 动画文件选择
        animClip = (AnimationClip)EditorGUILayout.ObjectField("动画文件", animClip, typeof(AnimationClip), false);

        // 动态规则配置
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("名称替换规则：");
        for (int i = 0; i < nameRules.Count; i++)
        {
            using (new EditorGUILayout.HorizontalScope())
            {
                nameRules[i].oldName = EditorGUILayout.TextField("原名称", nameRules[i].oldName);
                nameRules[i].newName = EditorGUILayout.TextField("→ 新名称", nameRules[i].newName);
                
                if (GUILayout.Button("×", GUILayout.Width(20)))
                {
                    nameRules.RemoveAt(i);
                    break;
                }
            }
        }

        if (GUILayout.Button("＋ 添加新规则"))
        {
            nameRules.Add(new NameRule());
        }

        // 可选预制体
        EditorGUILayout.Space();
        targetPrefab = (GameObject)EditorGUILayout.ObjectField("[可选] 参照预制体", targetPrefab, typeof(GameObject), false);
        EditorGUILayout.HelpBox("提供预制体可进行路径有效性验证", MessageType.Info);

        // 操作按钮
        EditorGUILayout.Space();
        using (new EditorGUILayout.HorizontalScope())
        {
            if (GUILayout.Button("开始修复", GUILayout.Height(35)))
            {
                UpdatePaths();
            }
            if (GUILayout.Button("显示路径", GUILayout.Height(35)))
            {
                LoadPaths();
            }
        }

        // 路径列表
        if (pathList.Count > 0)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"发现 {pathList.Count} 条路径：");
            scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
            foreach (var path in pathList)
            {
                var displayPath = ApplyRules(path);
                var isValid = targetPrefab ? IsValidPath(displayPath) : true;
                
                var style = new GUIStyle(EditorStyles.label) 
                {
                    normal = { textColor = isValid ? Color.white : Color.yellow }
                };
                
                EditorGUILayout.LabelField($"{path} → {displayPath}", style);
            }
            EditorGUILayout.EndScrollView();
        }
    }

    bool IsValidPath(string path)
    {
        var parts = path.Split('|');
        return targetPrefab.transform.Find(parts[0]) != null;
    }

    void LoadPaths()
    {
        pathList.Clear();
        if (!animClip) return;

        foreach (var binding in AnimationUtility.GetCurveBindings(animClip))
        {
            pathList.Add($"{binding.path}|{binding.propertyName}");
        }
    }

    void UpdatePaths()
    {
        if (!animClip) return;

        Undo.RecordObject(animClip, "Update Animation Paths");
        var bindings = AnimationUtility.GetCurveBindings(animClip);

        foreach (var binding in bindings)
        {
            var newPath = ApplyRules(binding.path);
            UpdateBinding(binding, newPath);
        }

        AssetDatabase.SaveAssets();
        Debug.Log($"路径更新完成！受影响数：{bindings.Length}");
    }

    string ApplyRules(string original)
    {
        foreach (var rule in nameRules.Where(r => !string.IsNullOrEmpty(r.oldName)))
        {
            original = original.Replace(rule.oldName, rule.newName);
        }
        return original;
    }

    void UpdateBinding(EditorCurveBinding oldBinding, string newPath)
    {
        var curve = AnimationUtility.GetEditorCurve(animClip, oldBinding);
        AnimationUtility.SetEditorCurve(animClip, oldBinding, null);

        var newBinding = new EditorCurveBinding
        {
            path = newPath,
            propertyName = oldBinding.propertyName,
            type = oldBinding.type
        };

        AnimationUtility.SetEditorCurve(animClip, newBinding, curve);
    }

    [System.Serializable]
    class NameRule
    {
        public string oldName = "";
        public string newName = "";
    }
}
#endif
