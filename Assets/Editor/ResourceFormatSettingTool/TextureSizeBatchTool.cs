using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using SuperTools; // 项目自定义的特性命名空间

public class TextureBatchTool : EditorWindow
{
    private const string EXPORT_FOLDER_NAME = "UITextureUnusedReports";
    private const string FOLDER_WHITELIST_FILE = "FolderWhitelist.txt";
    private const string FontsFolder = "Assets/UI/Fonts/";
    private const string UI_Texture_Folder = "Assets/UI";

    private string csvPath = "";
    private List<string> texturePaths = new List<string>();

    private Vector2 scrollPosFolderWhitelist = Vector2.zero;
    private Vector2 scrollPosTextureList = Vector2.zero;

    private int previewSize = 64;
    private Dictionary<string, int> originalSizes = new Dictionary<string, int>();

    private List<string> folderWhitelist = new List<string>(); // 文件夹白名单

    private readonly static string[] ValidExtensions = new[] { ".png", ".jpg", ".jpeg", ".tga", ".psd", ".gif", ".bmp" };

    [SuperTMenuItem(EMenuType.Main, "SToolCollection/Tools/UI/未引用图片压缩为32")]
    public static void OpenWindow()
    {
        War.AutoSetAssetBundlePostprocessor.TempCloseCheck = true;
        GetWindow<TextureBatchTool>("Texture Batch Manage");
    }

    private void OnDestroy()
    {
        War.AutoSetAssetBundlePostprocessor.TempCloseCheck = false;
    }

    private void OnEnable()
    {
        LoadFolderWhitelist();
    }

    private void OnGUI()
    {
        GUILayout.Label("批量操作图片（设置32x32 / 恢复 / 删除），支持文件夹白名单", EditorStyles.boldLabel);

        if (GUILayout.Button("选择无用图片CSV"))
        {
            string path = EditorUtility.OpenFilePanel("选择CSV", Application.dataPath, "csv");
            if (!string.IsNullOrEmpty(path))
            {
                csvPath = path;
                LoadTexturePaths();
            }
        }

        GUILayout.Label($"当前CSV路径: {csvPath}", EditorStyles.wordWrappedLabel);

        if (texturePaths.Count > 0)
        {
            GUILayout.Label($"共加载 {texturePaths.Count} 张图片", EditorStyles.helpBox);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("批量设置为32x32"))
                BatchResizeTo32();
            if (GUILayout.Button("批量恢复原始尺寸"))
                BatchRestoreSize();
            if (GUILayout.Button("一键删除CSV中的所有图片"))
                BatchDeleteAll();
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            if (GUILayout.Button($"删除 {UI_Texture_Folder} 下所有32x32的图片（排除白名单文件夹）"))
                DeleteAllSetAs32InUIFolder();

            GUILayout.Space(10);
            GUILayout.Label("文件夹白名单", EditorStyles.boldLabel);

            if (GUILayout.Button("添加文件夹至白名单"))
            {
                string selectedFolder = EditorUtility.OpenFolderPanel("选择文件夹添加到白名单", Application.dataPath, "");
                if (!string.IsNullOrEmpty(selectedFolder))
                {
                    string projPath = Application.dataPath.Replace("\\", "/");
                    selectedFolder = selectedFolder.Replace("\\", "/");
                    if (selectedFolder.StartsWith(projPath))
                    {
                        string relativePath = selectedFolder.Substring(projPath.Length);
                        if (!relativePath.StartsWith("Assets/"))
                            relativePath = "Assets" + relativePath;

                        if (!folderWhitelist.Contains(relativePath))
                        {
                            folderWhitelist.Add(relativePath);
                            SaveFolderWhitelist();
                        }
                    }
                    else
                    {
                        Debug.LogWarning("请选择项目内部目录");
                    }
                }
            }

            scrollPosFolderWhitelist = GUILayout.BeginScrollView(scrollPosFolderWhitelist, GUILayout.Height(120));
            for (int i = 0; i < folderWhitelist.Count; i++)
            {
                GUILayout.BeginHorizontal();
                GUILayout.Label(folderWhitelist[i], GUILayout.ExpandWidth(true));
                if (GUILayout.Button("移除", GUILayout.Width(60)))
                {
                    folderWhitelist.RemoveAt(i);
                    SaveFolderWhitelist();
                    break;
                }
                GUILayout.EndHorizontal();
            }
            GUILayout.EndScrollView();

            GUILayout.Space(10);

            GUILayout.Label("加载的图片路径列表：", EditorStyles.boldLabel);
            scrollPosTextureList = GUILayout.BeginScrollView(scrollPosTextureList, GUILayout.Height(120));
            foreach (var path in texturePaths)
                GUILayout.Label(path);
            GUILayout.EndScrollView();
        }
        else
        {
            GUILayout.Label("请先选择并加载CSV文件", EditorStyles.helpBox);
        }
    }

    private void LoadFolderWhitelist()
    {
        folderWhitelist.Clear();
        string whitelistPath = Path.Combine(Application.dataPath.Replace("/Assets", ""), EXPORT_FOLDER_NAME, FOLDER_WHITELIST_FILE);
        if (File.Exists(whitelistPath))
        {
            var lines = File.ReadAllLines(whitelistPath);
            foreach (var line in lines)
            {
                string t = line.Trim();
                if (!string.IsNullOrEmpty(t))
                    folderWhitelist.Add(t.Replace("\\", "/"));
            }
        }
    }

    private void SaveFolderWhitelist()
    {
        string whitelistPath = Path.Combine(Application.dataPath.Replace("/Assets", ""), EXPORT_FOLDER_NAME, FOLDER_WHITELIST_FILE);
        try
        {
            File.WriteAllLines(whitelistPath, folderWhitelist);
        }
        catch (Exception e)
        {
            Debug.LogError("保存白名单失败: " + e.Message);
        }
    }

    private bool IsPathInWhitelist(string path)
    {
        return folderWhitelist.Any(folder => path.StartsWith(folder, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsValidTexture(string path)
    {
        var ext = Path.GetExtension(path).ToLower();
        if (!ValidExtensions.Contains(ext))
            return false;

        if (path.StartsWith(FontsFolder, StringComparison.OrdinalIgnoreCase))
            return false;

        if (IsPathInWhitelist(path))
            return false;

        return true;
    }

    private void LoadTexturePaths()
    {
        texturePaths.Clear();
        originalSizes.Clear();

        var lines = File.ReadAllLines(csvPath);
        for (int i = 1; i < lines.Length; i++)
        {
            string line = lines[i].Trim().Replace("\\,", ",");
            if (!string.IsNullOrEmpty(line) && IsValidTexture(line))
                texturePaths.Add(line);
        }

        foreach (var path in texturePaths)
        {
            var ti = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ti != null)
                originalSizes[path] = ti.maxTextureSize;
        }

        Debug.Log($"已缓存 {texturePaths.Count} 张图片原始尺寸，排除白名单与Fonts文件夹。");
    }

    private void BatchResizeTo32()
    {
        int success = 0, fail = 0;
        List<string> failedPaths = new List<string>();

        foreach (var path in texturePaths)
        {
            var ti = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ti == null)
            {
                Debug.LogWarning($"未找到导入器或非图片文件: {path}");
                failedPaths.Add(path);
                fail++;
                continue;
            }

            if (ti.maxTextureSize == 32)
            {
                Debug.Log($"跳过已是32x32的图片: {path}");
                continue;
            }

            if (!originalSizes.ContainsKey(path))
                originalSizes[path] = ti.maxTextureSize;

            ti.maxTextureSize = 32;
            ti.textureCompression = TextureImporterCompression.Uncompressed;
            ti.mipmapEnabled = false;

            ti.SetPlatformTextureSettings(new TextureImporterPlatformSettings() { name = "Android", overridden = true, maxTextureSize = 32, format = TextureImporterFormat.ETC2_RGBA8 });
            ti.SetPlatformTextureSettings(new TextureImporterPlatformSettings() { name = "iPhone", overridden = true, maxTextureSize = 32, format = TextureImporterFormat.PVRTC_RGBA4 });

            ti.SaveAndReimport();
            success++;
        }

        string msg = $"设置为32x32完成，成功:{success}，失败:{fail}";
        if (failedPaths.Count > 0)
            msg += "\n失败列表:\n" + string.Join("\n", failedPaths);

        Debug.Log(msg);
        EditorUtility.DisplayDialog("完成", msg, "确定");
    }

    private void BatchRestoreSize()
    {
        int success = 0, fail = 0;
        List<string> failedPaths = new List<string>();

        foreach (var path in texturePaths)
        {
            var ti = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ti == null)
            {
                Debug.LogWarning($"未找到导入器或非图片文件: {path}");
                failedPaths.Add(path);
                fail++;
                continue;
            }

            if (originalSizes.TryGetValue(path, out int size))
            {
                ti.maxTextureSize = size;
                ti.ClearPlatformTextureSettings("Android");
                ti.ClearPlatformTextureSettings("iPhone");
                ti.SaveAndReimport();
                success++;
            }
            else
            {
                Debug.LogWarning($"无原始尺寸缓存，跳过: {path}");
                failedPaths.Add(path);
                fail++;
            }
        }

        string msg = $"恢复原始尺寸完成，成功:{success}，失败:{fail}";
        if (failedPaths.Count > 0)
            msg += "\n失败列表:\n" + string.Join("\n", failedPaths);

        Debug.Log(msg);
        EditorUtility.DisplayDialog("完成", msg, "确定");
    }

    private void BatchDeleteAll()
    {
        if (!EditorUtility.DisplayDialog("确认删除", $"即将删除CSV中 {texturePaths.Count} 个文件，且不可恢复，确定吗？", "确定", "取消"))
            return;

        int success = 0, fail = 0;
        List<string> failedPaths = new List<string>();

        foreach (var path in texturePaths)
        {
            if (!IsValidTexture(path))
                continue;

            bool res = AssetDatabase.DeleteAsset(path);
            if (res) success++; else { failedPaths.Add(path); fail++; }
        }
        AssetDatabase.Refresh();

        string msg = $"删除完成，成功:{success}，失败:{fail}";
        if (failedPaths.Count > 0)
            msg += "\n失败列表:\n" + string.Join("\n", failedPaths);

        Debug.Log(msg);
        EditorUtility.DisplayDialog("删除完成", msg, "确定");
    }

    private void DeleteAllSetAs32InUIFolder()
    {
        string[] allGuids = AssetDatabase.FindAssets("t:Texture2D", new[] { UI_Texture_Folder });
        List<string> toDelete = new List<string>();

        foreach (var guid in allGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (!IsValidTexture(path))
                continue;
            var ti = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ti != null && ti.maxTextureSize == 32)
                toDelete.Add(path);
        }

        if (toDelete.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", $"在{UI_Texture_Folder}目录下未检测到设置为32x32的图片。", "确定");
            return;
        }

        if (!EditorUtility.DisplayDialog("确认删除", $"即将删除{toDelete.Count}张32x32图片，且不可恢复，确定吗？", "确定", "取消"))
            return;

        int success = 0, fail = 0;
        List<string> failedPaths = new List<string>();

        foreach (var path in toDelete)
        {
            bool res = AssetDatabase.DeleteAsset(path);
            if (res) success++; else { failedPaths.Add(path); fail++; }
        }
        AssetDatabase.Refresh();

        string msg = $"删除完成，成功:{success}，失败:{fail}";
        if (failedPaths.Count > 0)
            msg += "\n失败列表:\n" + string.Join("\n", failedPaths);

        Debug.Log(msg);
        EditorUtility.DisplayDialog("删除完成", msg, "确定");
    }
}

