using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using System.Text;
using System.Collections.Generic;
using SuperTools;

public class TextCheckerEditor : EditorWindow
{
    private string folderPath = "";
    private string exportPath = "TextCheckResult.csv";

    [SuperTMenuItem(EMenuType.Main, "SToolCollection/Tools/UI/文本高度检测")]
    public static void ShowWindow()
    {
        GetWindow<TextCheckerEditor>("Text Height Checker");
    }

    private void OnGUI()
    {
        GUILayout.Label("选择要检查的文件夹（Assets路径）", EditorStyles.boldLabel);

        if (GUILayout.Button("选择文件夹"))
        {
            string selectedFolder = EditorUtility.OpenFolderPanel("选择文件夹", Application.dataPath, "");
            if (!string.IsNullOrEmpty(selectedFolder))
            {
                if (selectedFolder.StartsWith(Application.dataPath))
                {
                    folderPath = "Assets" + selectedFolder.Substring(Application.dataPath.Length);
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", "请选择Assets文件夹内的路径", "确定");
                }
            }
        }

        GUILayout.Label("当前路径：" + folderPath);

        GUILayout.Space(10);

        exportPath = EditorGUILayout.TextField("导出CSV路径（相对Assets/）", exportPath);

        if (GUILayout.Button("开始检测并导出CSV"))
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                EditorUtility.DisplayDialog("提示", "请选择一个文件夹路径", "确定");
                return;
            }

            CheckTextsAndExportCSV(folderPath, exportPath);
        }
    }

    private void CheckTextsAndExportCSV(string folder, string exportFile)
    {
        string[] guids = AssetDatabase.FindAssets("t:Prefab", new[] { folder });
        List<string[]> records = new List<string[]>();
        records.Add(new string[] { "预制体路径", "游戏对象名称", "Text组件高度", "字体大小", "bestFit" });

        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (prefab == null) continue;

            Text[] texts = prefab.GetComponentsInChildren<Text>(true);
            foreach (var text in texts)
            {
                float height = text.rectTransform.rect.height;
                int fontSize = text.fontSize;
                bool bestFit = text.resizeTextForBestFit;

                if (Mathf.Approximately(height, fontSize) && !bestFit)
                {
                    records.Add(new string[] { path, text.gameObject.name, height.ToString(), fontSize.ToString(), bestFit.ToString() });
                }
            }
        }

        string fullExportPath = Path.Combine(Application.dataPath, exportFile);
        StringBuilder sb = new StringBuilder();
        foreach (var rec in records)
        {
            sb.AppendLine(string.Join(",", rec));
        }

        try
        {
            File.WriteAllText(fullExportPath, sb.ToString(), Encoding.UTF8);
            EditorUtility.RevealInFinder(fullExportPath);
            EditorUtility.DisplayDialog("完成", $"检测完成，结果导出到:\n{fullExportPath}", "确定");
        }
        catch (System.Exception e)
        {
            EditorUtility.DisplayDialog("错误", "写文件失败：" + e.Message, "确定");
        }
    }
}

