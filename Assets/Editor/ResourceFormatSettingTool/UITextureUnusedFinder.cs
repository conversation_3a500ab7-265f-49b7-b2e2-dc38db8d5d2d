using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using SuperTools; // 项目自定义菜单等特性命名空间

public class UITextureUnusedFinder : EditorWindow
{
    private const string EXPORT_FOLDER_NAME = "UITextureUnusedReports";
    private const string WHITELIST_CSV_NAME = "UnusedUITextures_Whitelist.csv";
    private const string SCAN_FOLDERS_FILE = "ScanFolders.txt";
    private const string UI_TEXTURE_FOLDER = "Assets/UI";
    private const string SPRITE_ATLAS_FOLDER = "Assets/UI/0SpriteAtlas"; // 图集路径

    private string exportFolderFullPath;

    private List<string> scanFolders = new List<string>(); // 预制体等资源扫描目录列表
    private HashSet<string> whitelistPaths = new HashSet<string>();
    private List<string> unreferencedImagePaths = new List<string>(); // 未被引用的图片

    private List<string> loadedCsvTexturePaths = new List<string>();
    private Vector2 scrollPosScan = Vector2.zero;
    private Vector2 scrollPosCsv = Vector2.zero;
    private int previewSize = 64;

    private enum Tab
    {
        ScanAndExport,
        ViewCSV,
        ManageFolders
    }
    private Tab currentTab = Tab.ScanAndExport;

    [SuperTMenuItem(EMenuType.Main, "SToolCollection/Tools/UI/检查图片未引用")]
    public static void OpenWindow()
    {
        var window = GetWindow<UITextureUnusedFinder>("Unused UI Textures");
        window.minSize = new Vector2(800, 600);
        window.InitExportFolder();
        window.LoadWhitelist();
        window.LoadScanFolders();
    }

    private void InitExportFolder()
    {
        string projectRoot = Application.dataPath.Replace("/Assets", "");
        exportFolderFullPath = Path.Combine(projectRoot, EXPORT_FOLDER_NAME);
        if (!Directory.Exists(exportFolderFullPath))
            Directory.CreateDirectory(exportFolderFullPath);
    }

    private void OnGUI()
    {
        GUILayout.Space(8);
        GUILayout.BeginHorizontal();
        if (GUILayout.Toggle(currentTab == Tab.ScanAndExport, "Scan & Export", "LargeButton")) currentTab = Tab.ScanAndExport;
        if (GUILayout.Toggle(currentTab == Tab.ViewCSV, "View CSV & Preview", "LargeButton")) currentTab = Tab.ViewCSV;
        if (GUILayout.Toggle(currentTab == Tab.ManageFolders, "Manage Scan Folders", "LargeButton")) currentTab = Tab.ManageFolders;
        GUILayout.EndHorizontal();
        GUILayout.Space(10);

        switch (currentTab)
        {
            case Tab.ScanAndExport:
                DrawScanExportTab();
                break;
            case Tab.ViewCSV:
                DrawViewCsvTab();
                break;
            case Tab.ManageFolders:
                DrawManageFoldersTab();
                break;
        }
    }

    private void DrawScanExportTab()
    {
        GUILayout.Label("Scanning in folders:", EditorStyles.boldLabel);
        foreach (var folder in scanFolders)
            GUILayout.Label(folder, EditorStyles.miniLabel);
        GUILayout.Label("You can add/remove folders in 'Manage Scan Folders' tab.", EditorStyles.wordWrappedLabel);
        GUILayout.Space(8);

        GUILayout.Label("Scan for Unused Textures", EditorStyles.boldLabel);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Run Scan"))
        {
            RunScan();
            currentTab = Tab.ScanAndExport;
            Repaint();
        }
        if (GUILayout.Button("Open Export Folder")) OpenExportFolder();
        GUILayout.EndHorizontal();

        GUILayout.Space(5);
        GUILayout.Label($"Report folder:\n{exportFolderFullPath}", EditorStyles.helpBox);

        if (unreferencedImagePaths.Count > 0)
        {
            GUILayout.Space(10);
            GUILayout.Label($"Found {unreferencedImagePaths.Count} unused images (excluded whitelist and atlas references)", EditorStyles.helpBox);
            if (GUILayout.Button("Export Unused Textures to CSV"))
            {
                ExportCSVWithTimestamp();
                EditorUtility.DisplayDialog("Export Success", $"CSV exported to:\n{exportFolderFullPath}", "OK");
            }
            GUILayout.Space(10);
            GUILayout.Label("Unused Textures Preview:", EditorStyles.boldLabel);
            scrollPosScan = GUILayout.BeginScrollView(scrollPosScan);
            foreach (var path in unreferencedImagePaths)
            {
                GUILayout.BeginHorizontal();
                var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                if (tex != null)
                    GUILayout.Label(tex, GUILayout.Width(previewSize), GUILayout.Height(previewSize));
                else
                    GUILayout.Label("(NO preview)", GUILayout.Width(previewSize), GUILayout.Height(previewSize));
                GUILayout.Label(path);
                GUILayout.EndHorizontal();
            }
            GUILayout.EndScrollView();
        }
        else
            GUILayout.Label("No unused textures found or scan not run yet.", EditorStyles.helpBox);
    }

    private void DrawViewCsvTab()
    {
        GUILayout.Label("Load CSV file exported by this tool to preview unused textures.", EditorStyles.boldLabel);
        if (GUILayout.Button("Load CSV"))
        {
            string csvPath = EditorUtility.OpenFilePanel("Open Unused Textures CSV", exportFolderFullPath, "csv");
            if (!string.IsNullOrEmpty(csvPath))
            {
                LoadCSV(csvPath);
                Repaint();
            }
        }
        if (loadedCsvTexturePaths.Count == 0)
        {
            GUILayout.Label("No CSV loaded or no texture paths to display.");
            return;
        }
        GUILayout.Space(5);
        GUILayout.Label($"Loaded {loadedCsvTexturePaths.Count} textures from CSV", EditorStyles.helpBox);
        scrollPosCsv = GUILayout.BeginScrollView(scrollPosCsv);
        int failCount = 0;
        foreach (var path in loadedCsvTexturePaths)
        {
            GUILayout.BeginHorizontal();
            var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
            if (tex != null)
                GUILayout.Label(tex, GUILayout.Width(previewSize), GUILayout.Height(previewSize));
            else
            {
                failCount++;
                GUILayout.Label("(NO preview)", GUILayout.Width(previewSize), GUILayout.Height(previewSize));
            }
            GUILayout.Label(path);
            GUILayout.EndHorizontal();
        }
        GUILayout.EndScrollView();
        if (failCount > 0)
            GUILayout.Label($"Warning: {failCount} textures failed to load. Please check path casing and extension.", EditorStyles.wordWrappedLabel);
    }

    private void DrawManageFoldersTab()
    {
        GUILayout.Label("Manage Folders to Scan for Prefab References", EditorStyles.boldLabel);
        GUILayout.Label($"Config file: {Path.Combine(exportFolderFullPath, SCAN_FOLDERS_FILE)}", EditorStyles.miniLabel);
        GUILayout.Space(6);
        if (GUILayout.Button("Add Folder via Folder Panel"))
        {
            string selectedFolder = EditorUtility.OpenFolderPanel("Select Folder to Add to Scan List", Application.dataPath, "");
            if (!string.IsNullOrEmpty(selectedFolder))
            {
                string projPath = Application.dataPath;
                if (!projPath.EndsWith(Path.DirectorySeparatorChar.ToString()))
                    projPath += Path.DirectorySeparatorChar;
                selectedFolder = selectedFolder.Replace("\\", "/");
                projPath = projPath.Replace("\\", "/");

                if (selectedFolder.StartsWith(projPath))
                {
                    string relativePath = selectedFolder.Substring(projPath.Length);
                    if (!relativePath.StartsWith("Assets/"))
                        relativePath = "Assets/" + relativePath;
                    while (relativePath.StartsWith("Assets/Assets/"))
                        relativePath = relativePath.Substring("Assets/".Length);

                    if (!scanFolders.Contains(relativePath))
                    {
                        scanFolders.Add(relativePath);
                        UnityEngine.Debug.Log($"Added scan folder: {relativePath}");
                        SaveScanFolders();
                    }
                    else
                        UnityEngine.Debug.LogWarning($"Folder already added: {relativePath}");
                }
                else
                    UnityEngine.Debug.LogWarning("Please select a folder within the project.");
            }
        }
        GUILayout.Space(10);
        GUILayout.Label("Scan folders:", EditorStyles.boldLabel);
        for (int i = 0; i < scanFolders.Count; i++)
        {
            GUILayout.BeginHorizontal();
            GUILayout.Label(scanFolders[i], EditorStyles.label);
            if (GUILayout.Button("Remove", GUILayout.MaxWidth(60)))
            {
                scanFolders.RemoveAt(i);
                UnityEngine.Debug.Log($"Removed scan folder at index {i}");
                SaveScanFolders();
                break;
            }
            GUILayout.EndHorizontal();
        }
        if (scanFolders.Count == 0)
            GUILayout.Label("Scan folder list is empty. Use the button above to add at least one folder.", EditorStyles.helpBox);
    }

    private void LoadWhitelist()
    {
        whitelistPaths.Clear();
        string whitelistPath = Path.Combine(exportFolderFullPath, WHITELIST_CSV_NAME);
        if (File.Exists(whitelistPath))
        {
            var lines = File.ReadAllLines(whitelistPath);
            foreach (var line in lines)
            {
                string trimmed = line.Trim();
                if (!string.IsNullOrEmpty(trimmed) && !trimmed.StartsWith("#"))
                    whitelistPaths.Add(trimmed.Replace("\\", "/"));
            }
            UnityEngine.Debug.Log($"Loaded {whitelistPaths.Count} whitelist entries from {whitelistPath}");
        }
        else
            UnityEngine.Debug.LogWarning($"{WHITELIST_CSV_NAME} not found, continuing without whitelist.");
    }

    private void LoadScanFolders()
    {
        scanFolders.Clear();
        string configPath = Path.Combine(exportFolderFullPath, SCAN_FOLDERS_FILE);
        if (File.Exists(configPath))
        {
            var lines = File.ReadAllLines(configPath);
            foreach (var line in lines)
            {
                string trimmed = line.Trim();
                if (!string.IsNullOrEmpty(trimmed) && !trimmed.StartsWith("#") && !scanFolders.Contains(trimmed))
                    scanFolders.Add(trimmed);
            }
            UnityEngine.Debug.Log($"Loaded {scanFolders.Count} scan folders from {configPath}");
        }
        else
        {
            scanFolders.Add("Assets/UI");
            SaveScanFolders();
            UnityEngine.Debug.Log("Created default scan folder configuration with Assets/UI");
        }
    }

    private void SaveScanFolders()
    {
        string configPath = Path.Combine(exportFolderFullPath, SCAN_FOLDERS_FILE);
        try
        {
            File.WriteAllLines(configPath, scanFolders);
            UnityEngine.Debug.Log($"Saved scan folders to {configPath}");
        }
        catch (Exception e)
        {
            UnityEngine.Debug.LogError($"Failed to save scan folders: {e.Message}");
        }
    }

    private void RunScan()
    {
        unreferencedImagePaths.Clear();

        // 1. 收集所有Assets/UI目录下的Texture2D资源
        string[] textureGuids = AssetDatabase.FindAssets("t:Texture2D", new[] { UI_TEXTURE_FOLDER });
        HashSet<string> allTextures = new HashSet<string>(textureGuids.Select(g => AssetDatabase.GUIDToAssetPath(g)).Distinct());

        UnityEngine.Debug.Log($"Found {allTextures.Count} Texture2D assets under {UI_TEXTURE_FOLDER}.");

        // 2. 扫描scanFolders目录下所有资源的依赖，收集Assets/UI下的图片依赖
        HashSet<string> referencedTextures = new HashSet<string>();
        for (int i = 0; i < scanFolders.Count; i++)
        {
            var folder = scanFolders[i];
            UnityEngine.Debug.Log($"Scanning folder ({i + 1}/{scanFolders.Count}): {folder}");

            string[] allAssets = AssetDatabase.FindAssets("", new[] { folder })
                .Select(AssetDatabase.GUIDToAssetPath)
                .ToArray();

            for (int j = 0; j < allAssets.Length; j++)
            {
                var assetPath = allAssets[j];
                if (string.IsNullOrEmpty(assetPath))
                    continue;

                if ((j % 50) == 0)
                    EditorUtility.DisplayProgressBar("Scanning dependencies", assetPath, (float)j / allAssets.Length);

                // 如果是图集本体，跳过它的依赖扫描（但图集依赖的图片依旧会在其他资源的依赖中被统计）
                if (assetPath.StartsWith(SPRITE_ATLAS_FOLDER))
                    continue;
                string[] dependencies = AssetDatabase.GetDependencies(assetPath, true);
                foreach (var dep in dependencies)
                {
                    if (string.IsNullOrEmpty(dep)) continue;
                    if (dep == assetPath) continue; // 这行防止依赖自己
                    // 这里**不排除**图集中依赖的图片，只要是Assets/UI下的图片资源都加
                    if (!dep.StartsWith(UI_TEXTURE_FOLDER))
                        continue;

                    string ext = Path.GetExtension(dep).ToLower();
                    if (ext == ".png" || ext == ".jpg" || ext == ".jpeg" || ext == ".tga" || ext == ".psd"
                        || ext == ".gif" || ext == ".bmp" || ext == ".spriteatlas" || ext == ".sprite" || ext == ".asset")
                    {
                        referencedTextures.Add(dep.Replace("\\", "/"));
                    }
                }
            }
        }
        EditorUtility.ClearProgressBar();

        UnityEngine.Debug.Log($"Collected {referencedTextures.Count} referenced textures inside scan folders (excluding atlas files themselves).");

        // 3. 计算未引用的贴图 = 所有图片 - 被引用图片
        List<string> unreferenced = allTextures.Except(referencedTextures).ToList();

        // 4. 白名单过滤
        if (whitelistPaths.Count > 0)
        {
            int beforeCount = unreferenced.Count;
            unreferenced = unreferenced.Where(p => !whitelistPaths.Contains(p)).ToList();
            UnityEngine.Debug.Log($"Applied whitelist, filtered out {beforeCount - unreferenced.Count} entries.");
        }

        unreferencedImagePaths = unreferenced;

        Repaint();

        EditorUtility.DisplayDialog("Scan Complete", $"Found {unreferencedImagePaths.Count} potentially unused textures in {UI_TEXTURE_FOLDER}.", "OK");
    }

    private void ExportCSVWithTimestamp()
    {
        string timeStamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        string fileName = $"UnusedUITextures_Report_{timeStamp}.csv";
        string fullPath = Path.Combine(exportFolderFullPath, fileName);

        using (StreamWriter sw = new StreamWriter(fullPath, false, System.Text.Encoding.UTF8))
        {
            sw.WriteLine("AssetPath");
            foreach (var path in unreferencedImagePaths)
            {
                sw.WriteLine(path.Replace(",", "\\,"));
            }
        }

        UnityEngine.Debug.Log($"Exported CSV report: {fullPath}");
    }

    private void OpenExportFolder()
    {
        if (Directory.Exists(exportFolderFullPath))
        {
#if UNITY_EDITOR_WIN
            System.Diagnostics.Process.Start("explorer.exe", exportFolderFullPath.Replace("/", "\\"));
#elif UNITY_EDITOR_OSX
            System.Diagnostics.Process.Start("open", exportFolderFullPath);
#else
            UnityEngine.Debug.LogWarning("Open folder is not supported on this platform.");
#endif
        }
        else
        {
            UnityEngine.Debug.LogWarning($"Export folder does not exist: {exportFolderFullPath}");
        }
    }

    private void LoadCSV(string csvFullPath)
    {
        loadedCsvTexturePaths.Clear();
        try
        {
            var lines = File.ReadAllLines(csvFullPath);
            int failCount = 0;
            for (int i = 1; i < lines.Length; i++) // skip header
            {
                var line = lines[i].Trim().Replace("\\,", ",");
                if (!string.IsNullOrEmpty(line))
                {
                    loadedCsvTexturePaths.Add(line);
                    var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(line);
                    if (tex == null) failCount++;
                }
            }

            UnityEngine.Debug.Log($"Loaded {loadedCsvTexturePaths.Count} texture paths from CSV: {csvFullPath}");
            if (failCount > 0)
                UnityEngine.Debug.LogWarning($"Warning: {failCount} texture paths failed to load. Please check path casing and extension.");

            Repaint();
        }
        catch (Exception e)
        {
            UnityEngine.Debug.LogError($"Failed to load CSV: {e.Message}");
        }
    }
}

