using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CameraShakeBehaviour : PlayableBehaviour
{
    public string trigger;

    [NonSerialized]
    public Animator animator = null;

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (trigger != null && trigger != "" && animator)
            animator.SetTrigger(trigger);
    }
}

