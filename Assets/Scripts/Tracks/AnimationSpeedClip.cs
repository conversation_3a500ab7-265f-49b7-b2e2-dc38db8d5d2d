using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;

[Serializable]
public class AnimationSpeedClip : PlayableAsset, ITimelineClipAsset
{
    public AnimationSpeedBehaviour template = new AnimationSpeedBehaviour();

    public ClipCaps clipCaps
    {
        get { return ClipCaps.Blending; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<AnimationSpeedBehaviour>.Create(graph, template);
        //AnimationSpeedBehaviour clone = playable.GetBehaviour();
        return playable;
    }
}
