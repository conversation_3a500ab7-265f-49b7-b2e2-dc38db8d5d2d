using System;
using UnityEngine;
using UnityEngine.Playables;
using War.Base;
using War.Battle;

public class ParticleSystemDriver
{
    public PlayableDirector playable;
    public ParticleSystem particleSystem;
    public GameObject instance;
    public AnimatorHub animatorHub;
    public SkillEffectConfig config_new;
    public ParticleConfig config;

    [NonSerialized] 
    public GenericObjectPool.GameObjectInfo objectInfo;
    public ParticleSystemDriver(GameObject prefab)
    {
        if (prefab == null) return;

        objectInfo = GameObjectPool.GetPrefabPoolGameObject(prefab, PoolType.ParticleSystemDriver, null, false);
        instance = objectInfo.gameObject;
        if (instance == null)
        {
            return;
        }
        playable = instance.GetComponent<PlayableDirector>();
        config_new = instance.GetComponent<SkillEffectConfig>();
        config = instance.GetComponent<ParticleConfig>();

        if (playable)
        {
            playable.Stop();
            if (config)
            {
                config.Reset();
            }
            else if(config_new != null)
            {
                config_new.Reset();
            }

            if(playable.playableAsset == null)
            {
                Debug.LogError("Timeline is missing！Effect name :" + instance.ToString());
            }
        }
        else
        {
            Debug.LogWarning("PlayableDirector Component is Missing！Using ParticleSystem Effect：" + instance.ToString());
            particleSystem = instance.GetComponent<ParticleSystem>();
            if (particleSystem)
            {
                particleSystem.Stop();
                if (config)
                {
                    config.Reset();
                }
                else if (config_new != null)
                {
                    config_new.Reset();
                }
            }
        }


        animatorHub = instance.GetComponent<AnimatorHub>();
        if (animatorHub)
            animatorHub.SetSpeed(0);
    }

    public void PrepareParticleSystem(float duration)
    {
        if (playable == null)
        {
            ////Disable automatic random seed to get deterministic results.
            //if (particleSystem.useAutoRandomSeed)
            //    particleSystem.useAutoRandomSeed = false;

            //// Override the random seed number.
            //if (particleSystem.randomSeed != randomSeed)
            //    particleSystem.randomSeed = randomSeed;


            //Particle system duration should be longer than the track duration.
            if (particleSystem != null)
            {
                ParticleSystem.MainModule main = particleSystem.main;
                if (main.duration < duration)
                    main.duration = duration;
            }
        }
    }

    public void Attach(Transform transform, Vector3 offset)
    {
        if (transform && instance)
        {
            if (playable)
            {
                playable.transform.SetParent(transform, false);
                playable.transform.localPosition = offset;
                playable.transform.localScale = Vector3.one;
                playable.Stop();
                War.Script.Utility.SetLayer(instance, transform.gameObject.layer);
            }
            else if (particleSystem)
            {
                particleSystem.transform.SetParent(transform, false);
                particleSystem.transform.localPosition = offset;
                particleSystem.transform.localScale = Vector3.one;
                particleSystem.Stop();
                War.Script.Utility.SetLayer(instance, transform.gameObject.layer);
            }
        }

    }

    public void Attach(BattleActorNode source, BattleActorNode dest, int index)
    {
        if (config)
        {
            config.Initialize(index == 0);
            config.Tweak(source, dest);
        }
        else if (config_new != null)
        {
            config_new.Initialize(index, source);
            config_new.Tweak(source, dest);
        }
    }
    
    public void Attach(BattleActorNode node)
    {
        if (instance && node)
        {
            instance.transform.SetParent(node.transform, false);
            instance.transform.localPosition = Vector3.zero;
            instance.transform.localRotation = Quaternion.identity;
            instance.transform.localScale = Vector3.one;

            ParticleUtility.ReplaceMeshForNode(instance, node);
        }
    }

    public virtual void Destroy()
    {
        if (instance)
        {
            if (config_new && config_new.needDestroy)
            {
                //GameObjectPool.ReleaseWithCache("character", instance);
                GameObject.DestroyImmediate(instance);
            }
            else
            {
                GameObjectPool.ReleasePrefabPoolGameObject(PoolType.ParticleSystemDriver, objectInfo);
                //War.Base.PrefabObjectPool.Release(instance);
            }
        }
        objectInfo = null;
        instance = null;
    }

    public virtual void Clear()
    {
        if (playable)
        {
            playable.Stop();
        }
        else if (particleSystem)
        {
            particleSystem.Clear();
        }

    }

    public void Simulate(float time, float duration)
    {
        if (playable == null && particleSystem != null) {
            if (!Application.isPlaying && !particleSystem.isPlaying)
                PrepareParticleSystem(duration);

            if (Application.isPlaying)
            {
                //// Play mode time control: Only resets the simulation when a large
                //// gap between the time variables was found.
                //var maxDelta = Mathf.Max(1.0f / 30, Time.smoothDeltaTime * 2);

                //if (Mathf.Abs(time - particleSystem.time) > maxDelta)
                //{
                //    ResetSimulation(time);
                //    particleSystem.Play();
                //}
            }
            else
            {
                //Edit mode time control
                var minDelta = 1.0f / 240;
                var smallDelta = Mathf.Max(0.1f, Time.fixedDeltaTime * 2);
                var largeDelta = 0.2f;

                if (time < particleSystem.time ||
                    time > particleSystem.time + largeDelta)
                {
                    // Backward seek or big leap
                    // Reset the simulation with the current playhead position.
                    ResetSimulation(time);
                }
                else if (time > particleSystem.time + smallDelta)
                {
                    // Fast-forward seek
                    // Simulate without restarting but with fixed steps.
                    particleSystem.Simulate(time - particleSystem.time, true, false, true);
                }
                else if (time > particleSystem.time + minDelta)
                {
                    // Edit mode playback
                    // Simulate without restarting nor fixed step.
                    particleSystem.Simulate(time - particleSystem.time, true, false, false);
                }
                else
                {
                    // Delta time is too small; Do nothing.
                }
            }
        }
        else if(playable)
        {
            //Debug.Log("Timeline "+playable.name+" Playing Timing: "+ GetCurrentTime());
        }
    }

    public void ResetSimulation(float time)
    {
        if(playable == null)
        {
            const float maxSimTime = 2.0f / 3;

            if (time < maxSimTime)
            {
                particleSystem.Simulate(time);
            }
            else
            {
                // The target time is larger than the threshold: The default
                // simulation can be heavy in this case, so use fast-forward
                // (simulation with just a single step) then simulate for a small
                // period of time.
                particleSystem.Simulate(time - maxSimTime, true, true, false);
                particleSystem.Simulate(maxSimTime, true, false, true);
            }
        }

    }

    public void Play()
    {
        if (playable)
        {
            playable.Play();
        }

        else if (particleSystem)
            particleSystem.Play();

        if (animatorHub)
            animatorHub.SetSpeed(1);
    }

    public double GetMaxTime()
    {
        return playable.duration;
    }

    public double GetCurrentTime()
    {
        if (playable)
        {
            return playable.time;
        }
        return 0;
    }
}
