using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

public class SpriteSelfLightClip : PlayableAsset, ITimelineClipAsset
{
    public SpriteSelfLightBehaviour template = new SpriteSelfLightBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.Blending; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<SpriteSelfLightBehaviour>.Create(graph, template);
        SpriteSelfLightBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        return playable;
    }
}
