using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.528f, 0.965f, 0.425f)]
[TrackClipType(typeof(JumpoutClip))]
[TrackBindingType(typeof(BattleActorNode))]
public class JumpoutTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();
        BattleActorNode node = director.GetGenericBinding(this) as BattleActorNode;

        foreach (TimelineClip clip in GetClips())
        {
            JumpoutClip playableAsset = clip.asset as JumpoutClip;

            if (playableAsset)
            {
                playableAsset.node = node;
            }
        }
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Animator;
        if (comp == null)
            return;
        var so = new UnityEditor.SerializedObject(comp);
        var iter = so.GetIterator();
        while (iter.NextVisible(true))
        {
            if (iter.hasVisibleChildren)
                continue;
            driver.AddFromName<Animator>(comp.gameObject, iter.propertyPath);
        }
#endif
        base.GatherProperties(director, driver);
    }
}
