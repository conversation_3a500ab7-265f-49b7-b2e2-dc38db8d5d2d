
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;

public class ParticleSpawnClip : PlayableAsset, ITimelineClipAsset
{
    public ParticleSpawnBehaviour template = new ParticleSpawnBehaviour();
    [NonSerialized]
    public Transform parent;
    [NonSerialized]
    public List<Transform> parents;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<ParticleSpawnBehaviour>.Create(graph, template);
        ParticleSpawnBehaviour clone = playable.GetBehaviour();
        clone.parent = parent;
        clone.parents = parents;
        clone.drivers = new List<ParticleSystemDriver>();
        return playable;
    }
}
