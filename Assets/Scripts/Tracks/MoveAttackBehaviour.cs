using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

/// <summary>
/// 2021-12-29 lqx改为3D模型，不需要转换成snap
/// </summary>
[Serializable]
public class MoveAttackBehaviour : PlayableBehaviour
{
    [Tooltip("卡牌移动配置")]
    public MoveAttackConfig moveAttackConfig;
    [Header("镜头聚焦")]
    public FoucesInfo dest_camera  = new FoucesInfo(1, 0.5f);
    [NonSerialized]
    public Transform parent = null;
    [NonSerialized]
    public BattleActorNode source;
    [NonSerialized]
    public BattleActorNode dest = null;
    [NonSerialized]
    public int destPosition = -1;
    [NonSerialized]
    public CinemachineController cineControl;

    private Vector3 orgPos;
    private Vector3 targetPos;

    Vector3 orgSize;
    Vector3 targeSize;

    [NonSerialized]
    public float percent = 0;

    [NonSerialized]
    public Animator animator;

    private MoveAttackConfig realMoveAtkCfg;

    public Vector3 OrgPos //声明public field为预防后期需要lua hotfix
    {
        get
        {
            return orgPos;
        }
        set
        {
            orgPos = value;
        }
    }

    public Vector3 TargetPos //声明public field为预防后期需要lua hotfix
    {
        get
        {
            return targetPos;
        }
        set
        {
            targetPos = value;
        }
    }

    public void SetTargetPosition(Vector3 curPos,Vector3 curSize)
    {
        if (source == null || dest == null || !War.Base.Config.EnableMoveAttackBehaviour)
        {
            //Debug.LogError("移动角色失败："+ source.gameObject.name);
            return;
        }
        source.card.gameObject.transform.position = curPos;
        source.card.SetupShadow();//不一直设置Shadow的话会有曝光异常
        source.card.gameObject.transform.localScale = curSize;
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (source == null || dest == null || !War.Base.Config.EnableMoveAttackBehaviour)
        {
            //Debug.LogError("移动角色失败："+ source.gameObject.name);
            return;
        }
        float time = (float)playable.GetTime();
        float duration = (float)playable.GetDuration();

        if(moveAttackConfig.moveType == MoveAttackConfig.MoveType.Forward)
        {
            percent = time / duration;
        }
        else
        {
            percent = 1-(time / duration);
        }
        float posPercent = moveAttackConfig.moveCurve.Evaluate(percent);

        Vector3 curPos = Vector3.LerpUnclamped(orgPos, targetPos, posPercent);
        //考虑缩放
        Vector3 curSize = Vector3.LerpUnclamped(orgSize, targeSize, posPercent);
        SetTargetPosition(curPos,curSize);
    }

    public override void OnGraphStart(Playable playable)
    {
        realMoveAtkCfg = moveAttackConfig;
        if (source)
        {
            orgPos = source.gameObject.transform.position;
            orgSize = source.card.gameObject.transform.localScale;
        }
        else
        {
            orgPos = Vector3.zero;
            return;
        }
        if (dest)
        {
            targetPos = dest.gameObject.transform.position;
            targeSize = dest.card.gameObject.transform.localScale/ dest.card.config.size;
        }
        else
        {
            targetPos = Vector3.zero;
        }

        /*
        if(source && dest && source.actorMoveAttackCfg.Count > 0)
        {
            bool bFindTarget = false;
            var actorMoveAttackCfg = source.actorMoveAttackCfg;
            for (int i = 0; i < actorMoveAttackCfg.Count; i++)
            {
                if(actorMoveAttackCfg[i].targetLayoutPos == destPosition)
                {
                    realMoveAtkCfg = actorMoveAttackCfg[i].moveAttackConfig;
                    bFindTarget = true;
                    break;
                }
            }
            var defAttackId = source.defaultActorMoveAttackId;
            if (!bFindTarget && defAttackId >= 0 && defAttackId < actorMoveAttackCfg.Count)
            {
                realMoveAtkCfg = actorMoveAttackCfg[defAttackId].moveAttackConfig;
            }
        }
        */

        //可能会需要超过1的情况，取消该修正
//#if UNITY_EDITOR
//        float maxTime = float.MinValue;
//        float minTime = float.MaxValue;
//        var keys = realMoveAtkCfg.moveCurve.keys;
//        foreach(var keyframe in keys)
//        {
//            if(maxTime < keyframe.time)
//            {
//                maxTime = keyframe.time;
//            }
//            if(minTime > keyframe.time)
//            {
//                minTime = keyframe.time;
//            }
//        }
//        if(maxTime > realMoveAtkCfg.MaxTime || minTime < realMoveAtkCfg.MinTime)
//        {
//            Debug.LogError("AnimationCurve 时间需要在[" + realMoveAtkCfg.MinTime + " ~ " + realMoveAtkCfg.MaxTime + "]之间");
//        }
//#endif
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (cineControl)
        {
            cineControl.SetFocuse(dest, dest_camera);
        }

        if (moveAttackConfig.AnimTrigger && animator != null)
        {
            animator.SetTrigger(moveAttackConfig.m_AnimStr[(int)moveAttackConfig.moveType]);
            Debug.LogError("AnimationTriggerBehaviour:" + animator + " >>>> Tigger:" + moveAttackConfig.m_AnimStr[(int)moveAttackConfig.moveType]);
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        //为了修正归位不准确问题
        if (moveAttackConfig.moveType == MoveAttackConfig.MoveType.Backward)
        {
            SetTargetPosition(orgPos, orgSize);
        }
        if (dest_camera.weight == 0 && dest_camera.radius == 0)
        {
            dest_camera.weight = 1;
            dest_camera.radius = 0.5f;
        }
    }
}
