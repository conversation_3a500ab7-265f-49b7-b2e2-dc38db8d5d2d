using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class GraynessBehaviour : PlayableBehaviour
{
    public AnimationCurve curve = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));
    private bool flashed = false;

    private BattleActorNode node = null;
    private float inverseDuration = 1;

    public override void OnGraphStart(Playable playable)
    {
        double duration = playable.GetDuration();
        inverseDuration = 1f / (float)duration;
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node == null)
            node = playerData as BattleActorNode;

        float normalisedTime = (float)(playable.GetTime() * inverseDuration);
        UpdateGrayScale(normalisedTime);
        
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        switch (info.evaluationType)
        {
            case FrameData.EvaluationType.Evaluate:
                if (info.weight == 0)
                {

                }
                break;
            case FrameData.EvaluationType.Playback:
                if (info.deltaTime != 0)
                {
                    UpdateGrayScale(1);
                }
                break;
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        // unity bug OnBehaviourPause for info.deltaTime != 0 有时候没有调用，在这里再补调一次。
        UpdateGrayScale(1);
    }

    private void UpdateGrayScale(float normalized)
    {
        if (node && node.card)
        {
            float gray = curve.Evaluate(normalized);
            node.card.SetGrayness(gray);
        }
    }
}