//#define DEBUG_ACTION

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CardDissolveBehaviour : PlayableBehaviour
{
    public AnimationCurve dissolve = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));
    public bool autoReset = true;

    [NonSerialized]
    public BattleActorNode node;

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
#if DEBUG_ACTION
        Debug.LogError("OnBehaviourPlay " + (node ? node.card.name : "") + " disslove");
#endif
        if (node && node.card)
        {
            node.card.MarkResetting(CardResetting.CardDisslove);
        }
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node && node.card)
        {
            float time = (float)(playable.GetTime() / playable.GetDuration());
            float v = dissolve.Evaluate(time);
            node.card.SetDissolve(v);
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        if (node && node.card && autoReset)
            node.card.SetDissolve(0);
    }
}
