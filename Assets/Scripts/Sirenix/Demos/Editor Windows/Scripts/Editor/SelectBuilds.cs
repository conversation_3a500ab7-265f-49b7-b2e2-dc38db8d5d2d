using System.Net;
using System.Threading;

#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;
    using War.Base;
    using System.Text;
    using System.Text.RegularExpressions;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class SelectBuilds : GlobalConfig<SelectBuilds>
    {
        [Serializable]
        public class InputItem
        {
            [FolderPath]
            public string folder;
            public string abname;
            public string suffix;
        }
        [System.NonSerialized]
        public List<InputItem> assetPaths = new List<InputItem>{
            new InputItem()
            {
             folder ="Assets/LuaVice/luascript",
             abname = "luascript",
             suffix = ".txt"
            },
            new InputItem()
            {
             folder ="Assets/LuaVice/exec",
             abname = "",
             suffix = ".txt"
            },
        };
        [Sirenix.OdinInspector.FilePath]
        public List<string> filePaths = new List<string>
        {
        };
        [Sirenix.OdinInspector.FilePath]
        public string filesConfigPath;


        [FolderPath]
        public string outputPath; 

        [TextArea]
        public string blackboard = "";

        [HorizontalGroup]
        public bool hasSpritePacker = false; //是否需要打包图集

        [LabelText("ServerMark，热更标记:")]
        public string ServerMark = "ServerMark";

        string baseKey = "init_scr";


       


        public void InjectFixscr()          //创建lua覆盖patch
        {
            var mark = "--fixscr";

            var ip = assetPaths.Find(i => i.abname == "luascript");
            if (ip == null)
            {
                "".Print("InjectClearCrc failed,no luascript folder");
                return;
            }
            var fs = Directory.GetFiles(ip.folder, $"{baseKey}.txt", SearchOption.AllDirectories);
            

            if (fs.Length>0)
            {
                var f = fs[0];
                var content = File.ReadAllText(f);
                if(content.Contains(mark)==false)
                {
                    "".Print("InjectFixscr inject failed no mark");
                    return;
                }
                string luaRoot = Path.GetDirectoryName(ip.folder);

                var fuids = new FileUids();                         
                fuids.UpdateFolder(luaRoot);

                var ori_fuids_str = File.ReadAllText(fuids.localPath);
                var ori_fuids = UIHelper.ToObj<FileUids>(ori_fuids_str);

                var difflist = fuids.Diff(ori_fuids);
                Debug.Log($"show_difflist_count:{difflist.Count}");

                if (difflist.Count <= 0)                                //如果没有差异，不需要修改init_scr.txt的fix_list
                {
                    Debug.Log($"没有差异，不需要injectFixscr后续操作. ");
                    return;
                }

                bool isContailBaseScript = false;                     //第一次修改，可能未修改init_scr，导致差异列表会没有init_scr
                foreach (var fname in difflist)
                {
                    if (fname.CompareTo(baseKey) == 0)
                    {
                        isContailBaseScript = true;
                        break;
                    }
                }
                if (!isContailBaseScript)
                {
                    difflist.Add(baseKey);
                }

                "".Print("InjectFixscr difflist",UIHelper.ToJson(difflist));

                var sb = new StringBuilder();
                foreach (var fname in difflist)
                {
                    if(content.Contains($"'{fname}',")==false)
                    {
                        sb.AppendLine($"'{fname}',");
                    }
                }

                content = content.Replace(mark, $"{mark}\n{sb.ToString()}");
                File.WriteAllText(f,content);

                foreach (var fname in difflist)
                {
                    if (string.IsNullOrEmpty(fname)) continue;
                    var fpath = ori_fuids.GetFilePath(fname);
                    if (File.Exists(fpath))
                    {
                        string tpath = $"{ip.folder}/{fname}.txt";
                        if (Path.GetFullPath(fpath) == Path.GetFullPath(tpath)) continue;
                        "".Print("difflist copy", fpath, tpath);
                        File.Copy(fpath, tpath, true);
                    }
                }
                foreach (var fname in difflist)
                {
                    if (string.IsNullOrEmpty(fname)) continue;
                    var fpath = fuids.GetFilePath(fname);
                    if (fpath.Contains(ip.folder)) continue;
                    if (File.Exists(fpath))
                    {
                        "".Print("difflist del",fpath);
                        //File.Delete(fpath);
                    }
                }
                AssetDatabase.Refresh();

                //string OldValue = "InitLuaScr(cb)";
                //content = content.Replace(OldValue, OldValue +  code);
                //File.WriteAllText(f, content);
                //AssetDatabase.Refresh();
            }
            else
            {
                "".PrintError("difflist init_scr not found");
            }
        }
        [Button]
        public void build(BuildTarget buildTarget)
        {
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();

            InjectFixscr();

            var abdic = new Dictionary<string, List<string>>();

            System.Func<string, List<string>> getlist = (a) =>
             {
                 List<string> l = null;
                 abdic.TryGetValue(a, out l);
                 l = l ?? new List<string>();
                 abdic[a] = l;
                 return l;
             };

            foreach (var ii in assetPaths)
            {
                var abname = ii.abname;
                if(string.IsNullOrEmpty(abname))
                {
                    abname = null;
                }
                var assets = AssetDatabase.FindAssets("",new string[] { ii.folder });

                foreach (var item in assets)
                {
                    var asset = AssetDatabase.GUIDToAssetPath(item);
                    if (string.IsNullOrEmpty(ii.suffix) == false)
                    {
                        if (asset.EndsWith(ii.suffix)==false)
                        {
                            continue;
                        }
                    }
                    var abnThis = abname ?? AssetDatabase.GetImplicitAssetBundleName(asset);

                    if (!string.IsNullOrEmpty(abnThis))
                    {
                        var l = getlist(abnThis);
                        if (l.IndexOf(asset) == -1)
                        {
                            l.Add(asset);
                        }
                    }
                }
            }

            //var assets = AssetDatabase.FindAssets("", new List<string>(assetPaths.Convert<string>(it => (it as InputItem).folder)).ToArray());

            //foreach (var item in assets)
            //{
            //    var asset = AssetDatabase.GUIDToAssetPath(item);
            //    var abnThis = AssetDatabase.GetImplicitAssetBundleName(asset);
            //    if (!string.IsNullOrEmpty(abnThis))
            //    {
            //        var l = getlist(abnThis);
            //        l.Add(asset);
            //    }
            //}
            foreach (var item in filePaths)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                var asset = item;
                var abnThis = AssetDatabase.GetImplicitAssetBundleName(asset);
                if (!string.IsNullOrEmpty(abnThis))
                {
                    var l = getlist(abnThis);
                    if (l.IndexOf(asset) == -1)
                    {
                        l.Add(asset);
                    }
                }
            }

            blackboard = blackboard.Replace("\r", "");
            string[] fileList = blackboard.Split(new string[] {"\n"}, StringSplitOptions.None);

            List<string> inputFilePaths = new List<string>{};
            inputFilePaths.AddRange(fileList);

            //输入的列表文件，如果没有abname，使用文件路径作为abname
            foreach (var item in inputFilePaths)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                var asset = item;
                var abnThis = AssetDatabase.GetImplicitAssetBundleName(asset);
                if (string.IsNullOrEmpty(abnThis))
                {
                    abnThis = asset.Replace("Assets/", ""); //统一路径移除Assets/
                }
                if (!string.IsNullOrEmpty(abnThis))
                {
                    var l = getlist(abnThis);
                    if (l.IndexOf(asset) == -1)
                    {
                        l.Add(asset);
                    }
                }
            }

            var buildmap = new AssetBundleBuild[abdic.Count]; 
             
            int index = 0;
            foreach (var p in abdic)
            {
                var abd = new AssetBundleBuild();
                abd.assetBundleName = p.Key;
                abd.assetNames = p.Value.ToArray();

                //buildmap[0] = abd;
                //string assetsStr = string.Join("\t", abd.assetNames);
                //Debug.Log(string.Format("{0}\t{1}", p.Key, assetsStr));

                if (abd.assetNames.Length > 0)
                {
                    buildmap[index] = abd;
                    index ++;
                }
            }

            var cacheMode = EditorSettings.spritePackerMode;
            if (hasSpritePacker == true)
            {
                EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn;
            }

            EditorUtility.UnloadUnusedAssetsImmediate();

            EditorHelp.CheckDir(outputPath + "/");

            BuildPipeline.BuildAssetBundles(outputPath, buildmap
                    , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
                    , buildTarget);


            if (hasSpritePacker == true)
            {
                EditorSettings.spritePackerMode = cacheMode;
            }
            AssetDatabase.Refresh();

            War.Base.BuildScript.EncryptAssetBundles(outputPath);
#if UNITY_2020
            SendLuaToFtp();
#elif UNITY_2022
            SendLuaToFtpAsync();
#else
            SendLuaToFtpAsync();
#endif
            sw.Stop();
            Debug.LogError("buildAndSendToUseTime11");
            Debug.Log($"buildAndSendToUseTime:{sw.Elapsed.TotalSeconds}");
            //BuildInjecCatagory(buildmap);
        }

        void SendLuaToFtp()
        {
            var luascriptItem = assetPaths.Find(i => i.abname == "luascript");
            if (luascriptItem == null)
            {
                "".Print("SendLuaToFtp failed,no luascript folder");
                return;
            }
            if (string.IsNullOrEmpty(ServerMark))
            {
                ServerMark = "ServerMark";
            }

            var FTP_FOLDER = "ftp://**************:21/";
            var FTP_ACCOUNT = "wmzz";
            var FTP_PASS = "wmzz123";
            "".PrintError("Ftp", FTP_FOLDER, FTP_ACCOUNT,FTP_PASS);

            var LocalPath = $"{outputPath}/{luascriptItem.abname}.zip";
            var ServerPath = $"{FTP_FOLDER}/ServerMark/{ServerMark}_{luascriptItem.abname}.zip";
            if(ServerMark != "ServerMark")
            {
                ServerPath = $"{FTP_FOLDER}/ServerMark/ServerMark{ServerMark}_{luascriptItem.abname}.zip";
            }

            var FtpLuaPath = $"{FTP_FOLDER}/ServerMark/{luascriptItem.abname}.zip";

            "".Print("SendFileToFtp01",LocalPath,ServerPath);

            try
            {
                Debug.LogError("??ServerPath??");
                //发送带serverMark标记的luascript.zip到ftp
                LogDebug.Instance.SendFileToFtp(LocalPath, ServerPath, FTP_ACCOUNT, FTP_PASS);

                EditorUtility.DisplayDialog("Success", "发送脚本完成，测试设备开启fix后重启验证", "ok");
            }
            catch (Exception e)
            {
                "".PrintError("SendFileToFtp02", LocalPath, ServerPath,e.ToString());
            }
            try
            {
                Debug.LogError("??FtpLuaPath??");
                LogDebug.Instance.SendFileToFtp(LocalPath, FtpLuaPath, FTP_ACCOUNT, FTP_PASS);
            }
            catch (Exception e)
            {
                "".PrintError("SendFileToFtp03", LocalPath, FtpLuaPath,e.ToString());
            }
        }

        void BuildInjecCatagory(AssetBundleBuild[] builds)
        {
            var hc = FileSizeMgr.Instance.HashC; 
            if (!hc)
            {
                return;
            }

            if (string.IsNullOrEmpty(filesConfigPath))
            {
                return;
            }
            if (File.Exists(filesConfigPath) == false)
            {
                return;
            }
            var filesConfigC = File.ReadAllText(filesConfigPath);
            var localhc = War.Base.hashCheck.Parse(filesConfigC);

            if (!localhc)
            {
                return;
            }

            string files_path = outputPath + "/files";
            EditorHelp.CheckThisDir(files_path);
            string new_caching_path = outputPath + "/files/NewCaching";
            EditorHelp.CheckThisDir(new_caching_path);

            //var luascript_path = files_path + "/AssetBundles/Android/luascript.zip";

            foreach (var build in builds)
            {
                var abn = build.assetBundleName;
                abn = GetEncryFileName(abn);
                var md5 = hc?[abn];
                var bNotExist = string.IsNullOrEmpty(md5);
                
                var s = outputPath + "/" + abn;
                var t = new_caching_path + "/" + abn;

                if(bNotExist)
                {
                    md5 = ToolUti.File2MD5(s);
                }
                Hash128 hEmpty = new Hash128();
                var t_file = $"{t}/{(bNotExist ? hEmpty : Hash128.Parse(md5))}/_data";


                var crc32 = ToolUti.File2CRC32(s);
                var list = new List<string>()
                        {"current", md5, new FileInfo(s).Length + "", crc32+"",  ""};

                if(hc.list.TryGetValue(abn,out var flist))
                {
                    list[0] = flist[0];
                }
                localhc.list[abn] = list.ToArray();

                "".Print("cp", s, t_file);
                EditorHelp.CheckDir(t_file);
                File.Copy(s, t_file, true);
            }

            File.WriteAllText(new_caching_path + "/ncfiles.txt", localhc.ToJson());
        }
        static public string GetEncryFileName(string fileName)
        {
            War.Base.AssetBundleManager.SetEncryptSets();
            if (War.Base.AssetBundleManager.IsEncryptType(fileName) && !fileName.EndsWith(".zip"))
            {
                return fileName + ".zip";
            }

            return fileName;
        }
        [Button]
        void ToggleFolder()
        {
            foreach (var ii in assetPaths)
            {
                var p = ii.folder;
                if(System.IO.Directory.Exists(p))
                {
                    var tfolder = string.Format("{0}/~{1}", Path.GetDirectoryName(p), Path.GetFileName(p));
                    Directory.Move(p, tfolder);
                    File.Move(p+".meta", tfolder+".meta");
                }
                else
                {
                    var tfolder = string.Format("{0}/~{1}", Path.GetDirectoryName(p), Path.GetFileName(p));
                    if(Directory.Exists(tfolder))
                    {
                        Directory.Move(tfolder,p);
                        File.Move(tfolder + ".meta", p+".meta");
                    }
                }
            }
            AssetDatabase.Refresh();
        }

        public static void BuildAssetBundleNoAssets()
        {
            var cacheMode = EditorSettings.spritePackerMode;

            var cacheFiles = new List<string>() {
                "Android",
                "Android.manifest",
            };
            string outputPath = Path.Combine(War.Base.BuildScript.AssetBundlesOutputPath, War.Base.BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));

            var backupPath = outputPath + "/backup/";
            EditorHelp.CheckDir(backupPath);
            var count = cacheFiles.Count;
            for (int i = 0; i < count; i++)
            {
                var item = cacheFiles[i];
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildAssetBundleNoAssets..cache", item, (float)i / (float)count);
                if (isCancel)
                {
                    break;
                }
                EditorHelp.CopyFile(outputPath + "/" + item, backupPath + item);
            }
            EditorUtility.ClearProgressBar();

            EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
            /// abname:path
            var dic = new Dictionary<string, string>() {
                { "luascript","Assets/Lua/b" },
                //{ "battlescp","Assets/BattleScp" },
                //{ "gamescp","Assets/GameScp" },
                //{ "configs","Assets/Configs" },
            };
            var buildmap = new AssetBundleBuild[1];
            var assNames = new List<string>();
            var abd = new AssetBundleBuild();

            foreach (var pair in dic)
            {
                var abn = pair.Key;
                var assets = AssetDatabase.FindAssets("", new string[] { pair.Value });
                assNames.Clear();
                foreach (var item in assets)
                {
                    var asset = AssetDatabase.GUIDToAssetPath(item);
                    var abnThis = AssetDatabase.GetImplicitAssetBundleName(asset);
                    if (abnThis == abn)
                    {
                        assNames.Add(asset);
                    }
                }
                abd.assetBundleName = abn;
                abd.assetNames = assNames.ToArray();
                buildmap[0] = abd;
                Debug.Log(string.Format("abn:{1} assNames.Count:{0}", assNames.Count, abn));
                if (assNames.Count > 0)
                {
                    BuildPipeline.BuildAssetBundles(outputPath, buildmap
                        , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
                        , EditorUserBuildSettings.activeBuildTarget);
                }
            }
            EditorSettings.spritePackerMode = cacheMode;

            AssetDatabase.Refresh();

            //EncryptAssetBundles(outputPath);

            foreach (var item in cacheFiles)
            {
                EditorHelp.CopyFile(backupPath + item, outputPath + "/" + item);
            }
            Directory.Delete(backupPath, true);

        }
        [PropertyOrder(100)]
        public Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj.CheckABTc checkab = new Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj.CheckABTc();

        /// <summary>
        /// 上传Lua脚本异步
        /// </summary>
        void SendLuaToFtpAsync()
        {
            var luascriptItem = assetPaths.Find(i => i.abname == "luascript");
            if (luascriptItem == null)
            {
                "".Print("SendLuaToFtp failed,no luascript folder");
                return;
            }

            if (string.IsNullOrEmpty(ServerMark))
            {
                ServerMark = "ServerMark";
            }

            var FTP_FOLDER = "ftp://**************:21/";
            var FTP_ACCOUNT = "wmzz";
            var FTP_PASS = "wmzz123";

            var LocalPath = $"{outputPath}/{luascriptItem.abname}.zip";
            var ServerPath = $"{FTP_FOLDER}/ServerMark/{ServerMark}_{luascriptItem.abname}.zip";
            if (ServerMark != "ServerMark")
            {
                ServerPath =
                    $"{FTP_FOLDER}/ServerMark/ServerMark{ServerMark}_{luascriptItem.abname}.zip";
            }

            var FtpLuaPath = $"{FTP_FOLDER}/ServerMark/{luascriptItem.abname}.zip";

            CancellationTokenSource cts = new CancellationTokenSource();
            bool uploadDone = false;
            string message = "等待上传...";

            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    SendFileToFtp(LocalPath, ServerPath, FTP_ACCOUNT, FTP_PASS);
                    SendFileToFtp(LocalPath, FtpLuaPath, FTP_ACCOUNT, FTP_PASS);
                    uploadDone = true;
                    message = "上传完成！";
                }
                catch (Exception e)
                {
                    uploadDone = true;
                    message = $"上传失败: {e.Message}";
                }
            }, cts.Token);

            EditorApplication.update += EditorUpdate;

            void EditorUpdate()
            {
                if (uploadDone)
                {
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update -= EditorUpdate;
                    EditorUtility.DisplayDialog("上传结果", message, "OK");
                    cts?.Dispose();
                    cts = null;
                    return;
                }

                if (EditorUtility.DisplayCancelableProgressBar("上传Lua脚本", message, 0f))
                {
                    cts?.Cancel();
                    uploadDone = true;
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update -= EditorUpdate;
                }
            }
        }
        void SendFileToFtp(string filePath, string url, string ftpUserName, string ftpPassworld)
        {
            try
            {
                //iOS的问题，尝试先用WebClient，表现可能好一点
                using (WebClient client = new WebClient())
                {
                    client.Credentials = new NetworkCredential(ftpUserName, ftpPassworld);

                    byte[] fileData;
                    using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read,
                               FileShare.ReadWrite))
                    {
                        fileData = new byte[fs.Length];
                        fs.Read(fileData, 0, fileData.Length);
                    }
                    client.UploadData(url, "STOR", fileData);
                    return;
                }
            }
            catch (Exception ex)
            {
                try
                {
                    // 失败的话 用回直接的FTP请求，不要代理了。
                    FtpWebRequest request = (FtpWebRequest)System.Net.WebRequest.Create(url);
                    request.Method = WebRequestMethods.Ftp.UploadFile;
                    request.Credentials = new NetworkCredential(ftpUserName, ftpPassworld);
                    request.UsePassive = true;
                    request.UseBinary = true;
                    request.KeepAlive = false;

                    // 不用代理
                    request.Proxy = null;

                    byte[] fileContents;
                    using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read,
                               FileShare.ReadWrite))
                    {
                        fileContents = new byte[fs.Length];
                        fs.Read(fileContents, 0, fileContents.Length);
                    }

                    request.ContentLength = fileContents.Length;
                    using (Stream requestStream = request.GetRequestStream())
                    {
                        requestStream.Write(fileContents, 0, fileContents.Length);
                    }

                    using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                    {
                        Console.WriteLine("Upload File Complete, status {0}", response.StatusDescription);
                    }
                }
                catch (Exception fallbackEx)
                {
                    Console.WriteLine(fallbackEx.Message);
                }
            }
        }
    }
 





}
#endif
