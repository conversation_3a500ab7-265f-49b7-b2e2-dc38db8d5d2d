using War.UI;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using SuperTools;

public class SpriteAssetsExporter
{
    // [MenuItem("WLEditor/AutoExportSubSpriteAssets")]
    [SuperTMenuItem(EMenuType.Main, "WLEditor/AutoExportSubSpriteAssets")]
    // [SuperTMenuItem(EMenuType.Main, "Assets/UI/CreateSingleSpritePack")]
    public static void ExportSubAtlasesForCI()
    {
        // 记录开始时间
        System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
        stopwatch.Start();

        Debug.Log("开始导出子图集操作...");

        // 查找所有SpriteAssets文件
        string[] guids = AssetDatabase.FindAssets("t:SpriteAssets");
        int exportedCount = 0;
        int totalCount = guids.Length;

        Debug.Log($"找到 {totalCount} 个SpriteAssets文件");

        List<string> exportedFiles = new List<string>();

        foreach (string guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            SpriteAssets spriteAsset = AssetDatabase.LoadAssetAtPath<SpriteAssets>(assetPath);

            if (spriteAsset != null && spriteAsset.isMainSpriteAsset)
            {
                try
                {
                    Debug.Log($"正在导出子图集: {assetPath}");
                    // 调用导出子图集的方法
                    // if (exportedCount == 0)
                    // {
                        ExportSubAtlas(spriteAsset);
                    // }
                    exportedCount++;
                    exportedFiles.Add(assetPath);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"导出子图集失败: {assetPath}, 错误: {e.Message}");
                }
            }
        }

        // 记录结束时间
        stopwatch.Stop();

        Debug.Log($"子图集导出完成! 总共处理: {totalCount} 个文件, 导出: {exportedCount} 个子图集");
        Debug.Log($"耗时: {stopwatch.ElapsedMilliseconds / 1000f} 秒");

        if (exportedFiles.Count > 0)
        {
            Debug.Log("已导出的文件列表:");
            foreach (string file in exportedFiles)
            {
                Debug.Log(file);
            }
        }
    }

    // 导出子图集的方法，根据您现有的导出子图集功能进行调用
    private static void ExportSubAtlas(SpriteAssets spriteAsset)
    {
        if (spriteAsset == null)
        {
            Debug.LogError("导出子图集失败：SpriteAssets对象为空");
            return;
        }
    
        // 检查是否是主图集
        if (!spriteAsset.isMainSpriteAsset)
        {
            Debug.Log($"跳过导出子图集：{AssetDatabase.GetAssetPath(spriteAsset)} - 不是主图集");
            return;
        }
    
        try
        {
            // 获取资源路径
            string assetPath = AssetDatabase.GetAssetPath(spriteAsset);
            Debug.Log($"ExportSubAtlas 开始导出子图集：{assetPath}");
            
            SpriteAssetsUtility tempEditor = Editor.CreateEditor(spriteAsset) as SpriteAssetsUtility;

            if (tempEditor != null)
            {
                tempEditor.UpdateEditorSpriteDict();
                CreateAssetsConfigData.Instance.InitSpriteAssetsData(spriteAsset, tempEditor.GetEditorSpriteDict());
                // 标记资源为已修改并保存
                EditorUtility.SetDirty(spriteAsset);
                AssetDatabase.SaveAssets();
                Debug.Log($"子图集导出完成：{assetPath}");
            }
            return;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"导出子图集过程中发生错误：{e.Message}\n{e.StackTrace}");
            return;
        }   
    }
}