using System.Linq;
using UnityEngine;
using System.Collections.Generic;
using System.IO;
using UnityEditor.Sprites;
using UnityEditor;
using System.Reflection;
using System.Text;
using UnityEditor.U2D;
using UnityEngine.U2D;
using SuperTools;
/// <summary>
/// 自动生成图集
/// </summary>
public class AutoCreateSpriteAtlas
{
    public static void CLog(object message)
    {
        string time = System.DateTime.Now.ToString("HH:mm:ss");
        Debug.Log($"[CLog] {time} ==================================================================={message}");
    }
    [SuperTMenuItem(EMenuType.Main, "WLEditor/AutoCreateSpritePack_New")]
    //[MenuItem("WLEditor/AutoCreateSpritePack")]
    public static void AutoCreate()
    {
        //DeleteAtlasByPath();
        CLog("start auto create spriteatlas");
        try
        {
            AssetDatabase.StartAssetEditing();
            FinishSpriteAtlas();
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    public static Dictionary<string, string> SetTag()
    {
        var guids = AssetDatabase.FindAssets("t:Sprite", new string[] { "Assets/UI" });
        var dic = new Dictionary<string, string>();
        CLog($"CreateAtlasBySprites SetTag Count {guids.Length}");
        for (int i = 0; i < guids.Length; i++)
        {
            string guid = guids[i];
            //i++;if (i > 10) return guid;
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var ai = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ai == null)
            {
                CLog($"SetTag=== ai is null {path}");
                continue;
            }

            var tag = ai.spritePackingTag;
            if (string.IsNullOrEmpty(tag) == false)
            {
                dic[path] = tag;
            }
        }
        return dic;
    }

    public static void FinishSpriteAtlas()
    {
        var allCachedFiles = GetAllCacheFiles();
        if (allCachedFiles != null)
            CLog($"allCachedFiles  {allCachedFiles.Count}");
        else
            CLog($"allCachedFiles  count  = 0");
        var dic = SetTag();
        var dictags = new Dictionary<string, List<string>>();
        List<string> list = null;
        System.Action<string, string> addtag = (s, tag) =>
        {
            if (dictags.TryGetValue(tag, out list) == false)
            {
                dictags[tag] = new List<string>();
                list = dictags[tag];
            }
            list.Add(s);
        };
        foreach (var k in dic.Keys)
        {
            var tag = dic[k];
            addtag(k, tag);
        }
        DeleteAtlasByPath();
        string[] guids = new List<string>(dictags.Keys).ToArray();
        CLog($"dictags count  {guids.Length}");
        Dictionary<string, List<string>> altasRes = new Dictionary<string, List<string>>();
        
        for (int i = 0; i < guids.Length; i++)
        {
            string tag = guids[i];
            List<string> list1 = dictags[tag];
            CLog($"list1.Count {list1.Count}");
            if (File.Exists(GetAtlasPath(tag)) == false)
            {
                if (list1.Count < 2)
                {
                    CLog($"list1.Count {list1.Count}");
                    continue;
                }
            }
            string resPath = CreateAtlasFile(tag, true);
            altasRes.Add(resPath, list1);
        }
        AssetDatabase.StopAssetEditing();
        AssetDatabase.Refresh();
        AssetDatabase.StartAssetEditing();
        foreach (var item in altasRes)
        {
            CreateAtlasBySprites(item.Key, item.Value, allCachedFiles);
        }
    }

    public static void DeleteAtlasByPath()
    {
        CLog("start delete spriteatlas");
        string path = "Assets/UI/0SpriteAtlas";
        List<string> delPaths = new List<string>();
        delPaths.AddRange(Directory.GetFiles(path, "*.spriteatlas"));
        CLog($"check files count = {delPaths.Count}");
        for (int i = 0; i < delPaths.Count; i++)
        {
            if (File.Exists(delPaths[i]))
            {
                File.Delete(delPaths[i]);
                CLog($"delete path {delPaths[i]}");
            }
            else
            {
                CLog($"delete path is not exist {delPaths[i]}");
            }
        }
    }

    readonly private static string sptSrcDir = Application.dataPath + "/UI";
    public static string ui_atlas_root = "Assets/UI/0SpriteAtlas";


    public static string GetAtlasPath(string tag)
    {
        string atlasName = string.Format("{0}/{1}.spriteatlas", ui_atlas_root, tag);
        return atlasName;
    }


    public static Dictionary<string, string> GetAllCacheFiles()
    {
        var cacheDir = $"{Application.dataPath}/../Library/AtlasCache/";
        if (Directory.Exists(cacheDir) == false)
        {
            return null;
        }
        var allFiles = Directory.GetFiles(cacheDir, "*.*", SearchOption.AllDirectories);
        Dictionary<string, string> allCachedFiles = new Dictionary<string, string>();
        foreach (var cachedFile in allFiles)
        {
            var hash = Path.GetFileNameWithoutExtension(cachedFile);
            allCachedFiles[cachedFile] = hash;
        }
        return allCachedFiles;
    }

    public static void SetPlatformSetting(SpriteAtlas spriteAtlas)
    {
        if (spriteAtlas == null)
        {
            return;
        }
        var format = PlatformSettingCfg.Texture_IOS_Default_ImporterFormat;
        string ios_platformString = "iPhone";
        string android_platformString = "Android";
        TextureImporterPlatformSettings ios_textureImporterPlatformSettings = spriteAtlas.GetPlatformSettings(ios_platformString);
        TextureImporterPlatformSettings android_textureImporterPlatformSettings = spriteAtlas.GetPlatformSettings(android_platformString);
        Debug.Log(spriteAtlas.name);
        if (spriteAtlas.name.Equals("ui.gwhomescenebuilding"))
        {
            //先硬编码处理图集大小，后面需要优化为配置
            ios_textureImporterPlatformSettings.maxTextureSize = 4096;
            android_textureImporterPlatformSettings.maxTextureSize = 4096;
        }
        if (spriteAtlas.name.Equals("ui.gwfirstrecharge"))
        {
            ios_textureImporterPlatformSettings.compressionQuality = 0;
            android_textureImporterPlatformSettings.compressionQuality = 0;
            var setting = spriteAtlas.GetPackingSettings();
            setting.padding = 8;
#if UNITY_2022_1_OR_NEWER
            setting.enableAlphaDilation = true;
#endif
            spriteAtlas.SetPackingSettings(setting);
        }
        else if (spriteAtlas.name.Equals("ui.gwsandtopleveluisdf"))
        {
            var setting = spriteAtlas.GetPackingSettings();
            setting.padding = 8;
            
            var ts = spriteAtlas.GetTextureSettings();
            ts.sRGB = false;

            format = TextureImporterFormat.RGB24;

            spriteAtlas.SetPackingSettings(setting);
            spriteAtlas.SetTextureSettings(ts);

        }
        if (!android_textureImporterPlatformSettings.overridden || android_textureImporterPlatformSettings.format != format)
        {
            android_textureImporterPlatformSettings.overridden = true;
            android_textureImporterPlatformSettings.format = format;

            //android_textureImporterPlatformSettings.compressionQuality

            spriteAtlas.SetPlatformSettings(android_textureImporterPlatformSettings);
        }
        
        if (!ios_textureImporterPlatformSettings.overridden || ios_textureImporterPlatformSettings.format != format)
        {
            ios_textureImporterPlatformSettings.overridden = true;
            ios_textureImporterPlatformSettings.format = format;

            spriteAtlas.SetPlatformSettings(ios_textureImporterPlatformSettings);
        }
    }

    public static string CreateAtlasFile(string tag, bool resetatlas = false)
    {
        var filePath = CreateAtlas(GetAtlasPath(tag), resetatlas);
        CLog($"CreateAtlasFile filePath = {filePath}");
        return filePath;
    }
 
    public static void CreateAtlasBySprites(string filePath, List<string> list, Dictionary<string, string> allCachedFiles = null)
    {
        CLog($"CreateAtlasBySprites  {filePath}");
        SpriteAtlas sptAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(filePath);
        if(sptAtlas == null)
        {
            Debug.LogError($"CreateAtlasBySprites error {filePath}");
            return;
        }
        var fileName = Path.GetFileName(filePath);
        if ((fileName != "ui.gwsandtopleveluisdf.spriteatlas" && fileName != "ui.gwhomebuildingicon.spriteatlas" && fileName.StartsWith("ui.gwhome", System.StringComparison.OrdinalIgnoreCase) || fileName.StartsWith("ui.gwsand", System.StringComparison.OrdinalIgnoreCase)))
        {
            //城建，沙盘相关的UI打出的图集需要mimap
            var setting = sptAtlas.GetTextureSettings();
            setting.generateMipMaps = true;
            sptAtlas.SetTextureSettings(setting);

            //EditorUtility.SetDirty(sptAtlas);
            //AssetDatabase.Refresh();
        };
        List<Sprite> spts = new List<Sprite>();
        foreach (var assetPath in list)
        {
            Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath);
            if (IsPackable(sprite) && spts.Contains(sprite) == false)
            {
                spts.Add(sprite);
            }
        }
        try
        {
            SetPlatformSetting(sptAtlas);
            if (allCachedFiles != null && sptAtlas)
            {
                ClearSpriteAtlasCache(sptAtlas, allCachedFiles);
            }
            AddPackAtlas(sptAtlas, spts.ToArray());
            EditorUtility.SetDirty(sptAtlas);
            PackAtlas(sptAtlas);
            CLog($"Create Atlas success {filePath}");
        }
        catch (System.Exception e)
        {
            CLog($"Create error  {filePath}   {e}");
            throw;
        }
    }

    static bool IsPackable(Object o)
    {
        return o != null && (o.GetType() == typeof(Sprite) || o.GetType() == typeof(Texture2D) || (o.GetType() == typeof(DefaultAsset) && ProjectWindowUtil.IsFolder(o.GetInstanceID())));
    }

    static void ClearSpriteAtlasCache(SpriteAtlas spriteAtlas, Dictionary<string, string> allCachedFiles)
    {
        var spriteAtlasExtensionType = typeof(SpriteAtlasExtensions);
        // internal static Hash128 GetStoredHash([UnityEngine.Bindings.NotNull("NullExceptionObject")] this SpriteAtlas spriteAtlas)
        var GetStoredHash = spriteAtlasExtensionType.GetMethod("GetStoredHash", BindingFlags.Public | BindingFlags.Static | BindingFlags.NonPublic);
        var storedHash = (Hash128)GetStoredHash.Invoke(null, new object[] { spriteAtlas });
        var strStroedHash = storedHash.ToString();

        foreach (var hashFile in allCachedFiles)
        {
            if (hashFile.Value == strStroedHash)
            {
                File.Delete(hashFile.Key);
                Debug.Log($"删除缓存图集:{hashFile.Key}");
            }
        }
    }

    static void AddPackAtlas(SpriteAtlas atlas, Object[] spt)
    {
        //MethodInfo methodInfo = System.Type
        //     .GetType("UnityEditor.U2D.SpriteAtlasExtensions, UnityEditor")
        //     .GetMethod("Add", BindingFlags.Public | BindingFlags.Static);
        //if (methodInfo != null)
        //    methodInfo.Invoke(null, new object[] { atlas, spt });
        //else
        //    Debug.Log("methodInfo is null");
        if(atlas)
        SpriteAtlasExtensions.Remove(atlas, spt);
        SpriteAtlasExtensions.Add(atlas, spt);

        //PackAtlas(atlas);
    }

    static void PackAtlas(SpriteAtlas atlas)
    {
        SpriteAtlasUtility.PackAtlases(new[] { atlas }, EditorUserBuildSettings.activeBuildTarget);
    }

    public static string CreateAtlas(string atlasPath, bool reset = false, bool isVariant = false)
    {
        string yaml;
        if (!isVariant)
        {
            yaml = @"%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: New Sprite Atlas
  m_EditorData:
    textureSettings:
      serializedVersion: 2
      anisoLevel: 1
      compressionQuality: 50
      maxTextureSize: 2048
      textureCompression: 1
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings: []
    packingParameters:
      serializedVersion: 2
      padding: 4
      blockOffset: 1
      allowAlphaSplitting: 0
      enableRotation: 1
      enableTightPacking: 0
    variantMultiplier: 1
    packables: []
    bindAsDefault: 1
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_Tag: New Sprite Atlas
  m_IsVariant: 0
";
        }
        else
        {
            yaml = @"%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: New Sprite Atlas
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 1
      compressionQuality: 50
      maxTextureSize: 2048
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings: []
    packingSettings:
      serializedVersion: 2
      padding: 4
      blockOffset: 1
      allowAlphaSplitting: 0
      enableRotation: 1
      enableTightPacking: 1
    secondaryTextureSettings: {}
    variantMultiplier: 0.5
    packables: []
    totalSpriteSurfaceArea: 0
    bindAsDefault: 1
    isAtlasV2: 0
    cachedData: {fileID: 0}
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_RenderDataMap: {}
  m_Tag: New Sprite Atlas
  m_IsVariant: 1
";
        }
        return CreateAtlasBy(yaml, atlasPath, reset);
    }

    public static string CreateAtlasBy(string yaml, string atlasPath, bool reset = false)
    {
        if (File.Exists(atlasPath))
        {
            if (reset)
            {
                File.Delete(atlasPath);
                //AssetDatabase.Refresh();
            }
            else
            {
                "".Print("CreateAtlas", atlasPath, "exist");
                return atlasPath;
            }
        }

        CLog($"CreateAtlasBy {atlasPath}");
        ToolUti.CheckDir(atlasPath);
        FileStream fs = new FileStream(atlasPath, FileMode.OpenOrCreate);
        byte[] bytes = new UTF8Encoding().GetBytes(yaml);
        fs.Write(bytes, 0, bytes.Length);
        fs.Close();
        ////AssetDatabase.Refresh();
        CLog($"CreateAtlasBy success {atlasPath}");
        return atlasPath;
    }

}
