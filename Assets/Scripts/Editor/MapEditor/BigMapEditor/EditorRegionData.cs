using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[System.Serializable]
public class EditorRegionData : ScriptableObject
{
    public static int s_HideViewAreaId = -1;
    //id
    public int ID;

    //绑定的城池id
    public int CityID;

    // //区域范围
    // public string Scope;
    // //特殊区域
    // public string ZoneBuff;
    //资源点配置
    public string ResourcesDeploy;

    //怪物数量
    public int MonsterNumber;

    //怪物最低数量
    public int LowNumber;

    //野怪补充刷新数量
    public int RenovateNumber;

    //野怪刷新比例
    public string Ratio;

    public Color RegionColor = Color.cyan;
    public Color SubRegionColor = Color.yellow;

    public string AdjacentRegion;

    //区域范围
    public HashSet<EditorGridItem> Scope = new HashSet<EditorGridItem>();
    //所有选择的范围，并不受区域等限制
    public HashSet<EditorGridItem> AllSelectScope = new HashSet<EditorGridItem>();
    //区域中间的格子
    public HashSet<EditorGridItem> RegionGrid = new HashSet<EditorGridItem>();

    //中间区域的格子（简化，不记录所有的点，只记录线段的起点和终点）
    public SortedDictionary<int, List<(int,int)>> regionSimpleGrid = new SortedDictionary<int, List<(int,int)>>();
    
    //中间区域的格子（简化，不记录所有的点，只记录线段的起点和终点）
    public SortedDictionary<int, List<(int,int)>> subRegionSimpleGrid = new SortedDictionary<int, List<(int,int)>>();
  

    //特殊区域
    public HashSet<EditorGridItem> ZoneBuff = new HashSet<EditorGridItem>();
    //子区域中间的格子
    public HashSet<EditorGridItem> ZoneGrid = new HashSet<EditorGridItem>();

    public Region2DData Region2DData = new Region2DData();
    public Region2DData SubRegion2DData = new Region2DData();    
    public void TryAddBindPoint(EditorGridItem gridItem)
    {
        AllSelectScope.Add(gridItem);
        if (gridItem.RegionData) return;
        Scope.Add(gridItem);
        gridItem.SetRegionData(this);
    }

    public void TryRemoveBindPoint(EditorGridItem gridItem)
    {
        AllSelectScope.Remove(gridItem);
        gridItem.SetRegionData(null);
        Scope.Remove(gridItem);
        if (ZoneBuff.Contains(gridItem))
        {
            TryRemoveSubBindPoint(gridItem);
        }

        if (ZoneGrid.Contains(gridItem))
        {
            ZoneGrid.Remove(gridItem);
        }
    }

    public void TryAddSubBindPoint(EditorGridItem gridItem)
    {
        if (!gridItem.RegionData || (!Scope.Contains(gridItem) && !RegionGrid.Contains(gridItem))) return;
        ZoneBuff.Add(gridItem);
        // gridItem.SetRegionData(this);
    }

    public void TryRemoveSubBindPoint(EditorGridItem gridItem)
    {
        if (!ZoneBuff.Contains(gridItem)) return;
        // gridItem.SetRegionData(null);
        ZoneBuff.Remove(gridItem);
    }

    public void RemoveSubRegionData()
    {
        ResetSubItemData();
    }

    public void ResetItemData()
    {
        foreach (var item in Scope)
        {
            item.SetRegionData(null);
        }
        ClearRegionGrid();
        Scope.Clear();
        AllSelectScope.Clear();
    }

    public void ResetSubItemData()
    {
        ClearZoneGrid();
        ZoneBuff.Clear();
    }

    public void ClearRegionGrid()
    {
        foreach (var item in RegionGrid)
        {
            if (item.BuildData) continue;
            item.SetRegionData(null);
        }
        RegionGrid.Clear();
        Region2DData.ResetData();
    }

    public void ClearZoneGrid()
    {
        ZoneGrid.Clear();
    }

    public void TryAddZoneGrid(EditorGridItem item)
    {
        item.SetRegionData(this);
        ZoneGrid.Add(item);
    }

    public void TryAddRegionGrid(EditorGridItem item)
    {
        item.SetRegionData(this);
        RegionGrid.Add(item);
    }

    //计算多边形数据
    public void CalculatePolygonData(bool isSubRegion)
    {
        if (isSubRegion)
        {
            SubRegion2DData.ResetData();
            foreach (var grid in ZoneGrid)
            {
                SubRegion2DData.SetPointState(grid.GridPos.x,grid.GridPos.y,1);
            }
            foreach (var grid in ZoneBuff)
            {
                SubRegion2DData.SetPointState(grid.GridPos.x,grid.GridPos.y,1);
            }
            SubRegion2DData.RefreshPolygon();
        }
        else
        {
            Region2DData.ResetData();
            foreach (var grid in RegionGrid)
            {
                Region2DData.SetPointState(grid.GridPos.x,grid.GridPos.y,1);
            }
            foreach (var grid in Scope)
            {
                Region2DData.SetPointState(grid.GridPos.x,grid.GridPos.y,1);
            }
            Region2DData.RefreshPolygon();
        }
    }

    //转化简易数据
    public void CalculateSimpleData(bool isSubRegion)
    {
        var simpleGridData = isSubRegion? subRegionSimpleGrid:regionSimpleGrid;
        var grids = isSubRegion? ZoneGrid: RegionGrid;
        var subGrids = isSubRegion ?  ZoneBuff:  Scope;
        Dictionary<int, List<EditorGridItem>> temp = new Dictionary<int, List<EditorGridItem>>();
        simpleGridData.Clear();
        foreach (var it in grids)
        {
            if (!temp.ContainsKey(it.GridPos.y))
            {
                temp.Add(it.GridPos.y,new List<EditorGridItem>(128));
            }
            temp[it.GridPos.y].Add(it);
        }
        foreach (var it in subGrids)
        {
            if (!temp.ContainsKey(it.GridPos.y))
            {
                temp.Add(it.GridPos.y,new List<EditorGridItem>(128));
            }
            temp[it.GridPos.y].Add(it);
        }
        foreach (var it in temp)
        {
            it.Value.Sort((item1, item2) => { return item1.GridPos.x.CompareTo(item2.GridPos.x);});
        }

        foreach (var it in temp)
        {
            if (!simpleGridData.ContainsKey(it.Key))
            {
                simpleGridData.Add(it.Key,new List<(int,int)>(4));
            }
            int count = 0;
            int curX = it.Value[0].GridPos.x;
            if (it.Value.Count == 1)
            {
                simpleGridData[it.Key].Add((curX,curX));
                continue;
            }
            for (int i = 1; i < it.Value.Count; i++)
            {
                //找到未断开的最后一个
                if (it.Value[i].GridPos.x - it.Value[i - 1].GridPos.x == 1)
                {
                    count++;
                    //如果是最后一个
                    if (i == it.Value.Count - 1)
                    {
                        simpleGridData[it.Key].Add((curX,it.Value[i].GridPos.x));
                    }
                }
                else
                {
                    if (count == 0)
                    {
                        simpleGridData[it.Key].Add((curX,curX));
                    }
                    else
                    {
                        simpleGridData[it.Key].Add((curX,it.Value[i-1].GridPos.x));
                    }
                    curX = it.Value[i].GridPos.x;
                    count = 0;
                    //如果是最后一个
                    if (i == it.Value.Count - 1)
                    {
                        simpleGridData[it.Key].Add((curX, curX));
                    }
                }
            }
        }
    }
    public void DrawRegionPreview(bool isSubRegion, bool needDrawBorder = true)
    {
        if (s_HideViewAreaId!= -1 &&ID == s_HideViewAreaId)
        {
            return;
        }
        //必须改成绘制多边形才不会卡
         int Size = BigMapEditor.GridSize;
         Handles.color = isSubRegion ? SubRegionColor : RegionColor;
         var simpleData = isSubRegion ? subRegionSimpleGrid : regionSimpleGrid;
         foreach (var sData in simpleData)
         {
             foreach (var area in sData.Value)
             {
                 //一行合并画
                 var BlockGridPos = new Vector3[4];
                 var lbPos = new Vector3(area.Item1 - Size / 2f,  EditorGridItem.AREA_HEIGHT, sData.Key- Size / 2f);
                 var ltPos = new Vector3(area.Item1 - Size / 2f, EditorGridItem.AREA_HEIGHT, sData.Key + Size / 2f);
                 var rtPos = new Vector3(area.Item2 + Size / 2f, EditorGridItem.AREA_HEIGHT, sData.Key + Size / 2f);
                 var rbPos = new Vector3(area.Item2 + Size / 2f, EditorGridItem.AREA_HEIGHT, sData.Key - Size / 2f);
                 BlockGridPos[0] = lbPos;
                 BlockGridPos[1] = ltPos;
                 BlockGridPos[2] = rtPos;
                 BlockGridPos[3] = rbPos;
                 if (needDrawBorder)
                 {
                    
                     Handles.DrawSolidRectangleWithOutline(BlockGridPos, Handles.color,
                         Color.black);
                 }
                 else
                 {
                     Handles.DrawSolidRectangleWithOutline(BlockGridPos, Handles.color,
                         Handles.color);
                 }
             }
         }
        
    }
}