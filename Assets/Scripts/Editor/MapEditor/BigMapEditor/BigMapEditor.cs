using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public enum EditorMode
{
    None,
    Bind,
    Delete,
    SubBind,
    SubDelete,
}

/// <summary>
/// Scene中视口缩放时的层级
/// </summary>
public enum ViewPortLayer
{
    None,
    Layer1,     //摄像机距离0-20
    Layer2,     //摄像机距离20-40
    Layer3,     //摄像机距离40-100
    Layer4,     //摄像机距离>100
}

public class BigMapEditor : EditorWindow
{
    [MenuItem("Tool/BigMapEditor")]
    private static void ShowWindow()
    {
        CheckScene();
        var window = GetWindow<BigMapEditor>();
        window.titleContent = new GUIContent("BigMapEditor");
        window.Show();
    }

    private const string EDITORSCENE = "Assets/Scene/BigMapEditorScene.unity";

    private const string BUILDPREFABPATH = "Assets/EditorConfig/MapEditorConfig/BuildIng.prefab";

    private Vector2Int m_mapSize;
    public const int GridSize = 1;
    private Vector2 scroll;
    private GameObject m_gridRoot;
    private Dictionary<int, EditorGridItem> m_listGrid = new Dictionary<int, EditorGridItem>();
    private bool isGen;
    private EditorGridItem m_selectItem;
 	private EditorGridItem lastDragTouchItem;
    //上一次按住shift后点击用于绑定的item
    private  EditorGridItem lastShiftClickBindItem;
    private Vector3[] m_selectPos;
    private string m_buildingPath;
    private EditorBuildingDataTemp m_buildingData;

    private int m_viewSize;
    private int m_hideViewAreaId = -1;

    private bool TestBool;

    //建造和预览
    private EditorBuildingData m_curBuildingData;

    private GameObject m_buildingPreview;

    //有绑定建筑的格子
    private List<EditorGridItem> m_buildingList = new List<EditorGridItem>();
    private List<EditorRegionData> m_regionData = new List<EditorRegionData>();

    private Transform m_model;
    private GameObject m_previewModle;
    private ParallelLoopResult m_parallelRes;
    private BuildingEditorWindow m_buildingEditorWindow;

    private EditorMode BindMode = EditorMode.None;
    private bool m_isBindDrag;
    private bool m_isBuindingDraw;
    private Vector2Int jumpPos;
    private int curSelectEmptyGridsIndex = -1;

    private ViewPortLayer curViewPortLayer = ViewPortLayer.None;
    //坐标系比例
    private int coordinateSystemsScale = 1;
    //是否是2D视角
    private bool cameraOrthographic = true;
    //设置位置文本的显示间隔
    private int showPosTextSpace = 1;
    private float lastClickTime = 0;

    #region GuiStyle
    private GUIStyle  coordinateSystemsStyle;
    private GUIStyle  reginDataStyle;
    private GUIStyle  selectStyle;
    #endregion

    private static bool CheckScene()
    {
        if (EditorApplication.isPlaying)
        {
            return true;
        }

        if (!EDITORSCENE.Equals(SceneManager.GetActiveScene().path))
        {
            EditorSceneManager.OpenScene(EDITORSCENE);
        }

        EditorApplication.isPlaying = true;
        return false;
    }

    private void Init()
    {
        InitGuiStyle();
        m_gridRoot = GameObject.Find("GridRoot");
        var plane = GameObject.Find("Plane");
        Vector3 pos = plane.transform.position;
        pos.y = EditorGridItem.AREA_HEIGHT - 0.5f;
        plane.transform.position = pos;

        m_buildingEditorWindow = ScriptableObject.CreateInstance<BuildingEditorWindow>();
        m_buildingEditorWindow.Awake();
        m_viewSize = EditorPrefs.GetInt("ViewSize", 0);
        var mapSizeX = EditorPrefs.GetInt("MapSizeX", 0);
        var mapSizeY = EditorPrefs.GetInt("MapSizeY", 0);
        m_mapSize = new Vector2Int(mapSizeX, mapSizeY);
        m_buildingEditorWindow.OnBuildingDelete += OnBuildingDelete;
        BindMode = EditorMode.None;
    }

    private void InitGuiStyle()
    {
        coordinateSystemsStyle =  coordinateSystemsStyle = new GUIStyle();
        coordinateSystemsStyle.normal.textColor = Color.green;
        coordinateSystemsStyle.fontSize = 15;
        coordinateSystemsStyle.alignment = TextAnchor.MiddleCenter;
        
        reginDataStyle = new GUIStyle();
        reginDataStyle.normal.textColor = Color.black;
        reginDataStyle.fontSize = 15;
        reginDataStyle.alignment = TextAnchor.MiddleCenter;
        
        selectStyle = new GUIStyle();
        selectStyle.normal.textColor = Color.black;
        selectStyle.fontSize = 15;
        selectStyle.alignment = TextAnchor.MiddleCenter;
    }

    private void OnEnable()
    {
        SceneView.duringSceneGui -= OnSceneGui;
        SceneView.duringSceneGui += OnSceneGui;
        EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
        m_selectItem = null;
        Init();
    }

    private void OnDisable()
    {
        SceneView.duringSceneGui -= OnSceneGui;
        EditorApplication.playModeStateChanged -= OnPlayModeStateChanged;
        m_buildingEditorWindow.OnBuildingDelete -= OnBuildingDelete;
    }

    private void OnPlayModeStateChanged(PlayModeStateChange state)
    {
        if (state == PlayModeStateChange.ExitingPlayMode)
        {
            OnDestroy();
        }
    }

    private void OnDestroy()
    {
        isGen = false;
        foreach (var item in m_listGrid.Values)
        {
            item.Destroy();
        }

        if (m_buildingPreview)
        {
            DestroyImmediate(m_buildingPreview);
            m_previewModle = null;
            m_buildingPreview = null;
            m_model = null;
        }

        m_listGrid.Clear();
        m_buildingList.Clear();
        m_selectItem = null;
        lastDragTouchItem = null;
        BindMode = EditorMode.None;
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Vector2.zero, new Vector2(200, position.height - 100)));
        if (!isGen)
        {
            var newMapSize = EditorGUILayout.Vector2IntField("地图大小", m_mapSize);
            if (newMapSize != m_mapSize)
            {
                m_mapSize = newMapSize;
                EditorPrefs.SetInt("MapSizeX", m_mapSize.x);
                EditorPrefs.SetInt("MapSizeY", m_mapSize.x);
            }

            if (GUILayout.Button("生成地图"))
            {
                if (m_mapSize.x <= 0 || m_mapSize.y <= 0)
                {
                    Debug.LogError("地图大小不能小于0");
                    return;
                }

                if (GridSize <= 0)
                {
                    Debug.LogError("格子大小不能小于0");
                    return;
                }

                GenMap();
            }

            GUILayout.EndArea();
            return;
        }

        if (m_selectItem != null)
        {
            GUILayout.Label($"当前选中格子：{m_selectItem.GridPos.x},{m_selectItem.GridPos.y}");
        }

        // EditorGUILayout.BeginHorizontal();

        // GUILayout.Label("建筑路径:");
        // GUILayout.Label(m_buildingPath);
        //
        // if (GUILayout.Button("选择建筑路径"))
        // {
        //     m_buildingPath = EditorUtility.OpenFolderPanel("选择建筑路径", "", "");
        //     m_buildingPath = EditorUtil.GetAssetPath(m_buildingPath);
        // }
        //
        // if (!m_buildingData)
        // {
        //     if (GUILayout.Button("加载建筑配置"))
        //     {
        //         m_buildingData =
        //             AssetDatabase.LoadAssetAtPath<EditorBuildingDataTemp>($"{m_buildingPath}/BuildingDataTemp.asset");
        //         if (!m_buildingData)
        //         {
        //             ShowNotification(new GUIContent("建筑配置不存在"));
        //         }
        //     }
        // }

        // EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();

        if (m_curBuildingData)
        {
            GUILayout.Label($"当前选中建筑：{m_curBuildingData.Name}_{m_curBuildingData.CityLevel}");
            if (GUILayout.Button("取消选中"))
            {
                CancelSelectBuilding();
            }
        }

        EditorGUILayout.EndHorizontal();

        var newViewSize = EditorGUILayout.IntField("视野范围", m_viewSize);
        if (newViewSize != m_viewSize)
        {
            m_viewSize = newViewSize;
            EditorPrefs.SetInt("ViewSize", m_viewSize);
        }
        
        var m_newViewAreaId = EditorGUILayout.IntField("隐藏指定区域Id", m_hideViewAreaId);
        if (m_newViewAreaId != m_hideViewAreaId)
        {
            m_hideViewAreaId = m_newViewAreaId;
            EditorRegionData.s_HideViewAreaId = m_hideViewAreaId;
        }
        
        jumpPos = EditorGUILayout.Vector2IntField("跳转坐标", jumpPos);
        if (GUILayout.Button("跳转"))
        {
            JumpViewPoint(jumpPos.x, jumpPos.y);
        }
        if (GUILayout.Button("检测区域重复"))
        {
            HashSet<Vector2Int> vector2Ints = new HashSet<Vector2Int>();
            //检测选中的是否和其他有重叠
            foreach (var region in m_regionData)
            {
                if(region == null)
                    continue;
                foreach (var region1 in m_regionData)
                {
                    if(region1 == null)
                        continue;
                    if (region.ID != region1.ID)
                    {
                        foreach (var grid in region.Scope)
                        {
                            var b = region1.Scope.Where(x => x.GridPos.x == grid.GridPos.x && x.GridPos.y == grid.GridPos.y).Any();
                            if (b)
                            {
                                vector2Ints.Add(grid.GridPos);
                            }
                        }
                    }
                }
            }
            if (vector2Ints.Count > 0)
            {
                string str  = "存在重复区域的坐标：";
                    
                foreach (var vector2Int in vector2Ints)
                {
                    str +=  vector2Int.x+","+vector2Int.y+"  ";
                }
                Debug.LogError("区域重复:"+ str);
            }
        }
        // if (GUILayout.Button("获取所有空格"))
        // {
        //     var items = GetHaveEmptyGrids();
        //     if (items != null && items.Count > 0)
        //     {
        //         curSelectEmptyGridsIndex = 0;
        //         JumpViewPoint(items[curSelectEmptyGridsIndex].GridPos.x, items[curSelectEmptyGridsIndex].GridPos.y);
        //     }
        //     else
        //     {
        //         curSelectEmptyGridsIndex = -1;
        //     }
        // }
        if (GUILayout.Button("跳转下一个空格"))
        {
            var items = GetHaveEmptyGrids();
            if (items != null && items.Count > 0)
            {
                curSelectEmptyGridsIndex ++;
                curSelectEmptyGridsIndex %= items.Count;
                JumpViewPoint(items[curSelectEmptyGridsIndex].GridPos.x, items[curSelectEmptyGridsIndex].GridPos.y);
            }
        }
        

        if (m_selectItem != null && m_selectItem.RegionData != null)
        {
            if (GUILayout.Button("跳转当前区域城池"))
            {
                foreach (var build in m_buildingList)
                {
                    if(build.RegionData!=null&& build.RegionData == m_selectItem.RegionData)
                    {
                        JumpViewPoint(build.GridPos.x,build.GridPos.y);
                        break;
                    }
                }
            }
           
        }

        int count = 0;
        foreach (var region in m_regionData)
        {
            if(region == null)
                continue;
            if (region.RegionGrid!= null && region.Scope != null)
            {
                count += region.RegionGrid.Count+region.Scope.Count;
            }
        }
        //所有区域总格子数
        GUILayout.Label($"所有区域格子总数：{count}");

        TestBool = GUILayout.Toggle(TestBool, "测试TestBool");
        m_isBuindingDraw = GUILayout.Toggle(m_isBuindingDraw, "有城池的格子不着色");

        //绘制建筑列表
        // DrawBuildingList();

        m_buildingEditorWindow.DrawBuildList(HandleBuildingClick);

        if (GUILayout.Button("复位相机"))
        {
            var view = SceneView.lastActiveSceneView;
            view.rotation = Quaternion.Euler(45, 0, 0);
        }

        GUILayout.EndArea();

        DrawRight();

        DrawBottom();
    }

    private void DrawRight()
    {
        GUILayout.BeginArea(new Rect(new Vector2(205, 0), new Vector2(position.width - 205, position.height - 100)));

        if (m_selectItem != null)
        {
            DrawRegion();
            GUILayout.EndArea();
            return;
        }

        m_buildingEditorWindow.DrawBuildDesc();

        GUILayout.EndArea();
    }

    private void DrawBottom()
    {
        GUILayout.BeginArea(new Rect(new Vector2(0, position.height - 100), new Vector2(position.width, 100)));
        m_buildingEditorWindow.DrawBottomButton();
        EditorGUILayout.BeginHorizontal();

        // if (GUILayout.Button("临时保存网格信息"))
        // {
        //     if (string.IsNullOrEmpty(AssetDatabase.GetAssetPath(m_buildingList)))
        //     {
        //         AssetDatabase.CreateAsset(m_buildingList, m_buildingEditorWindow.SavePath + "/GridItemDataTemp.asset");
        //     }
        //
        //     AssetDatabase.SaveAssets();
        //     AssetDatabase.Refresh();
        // }
        if (GUILayout.Button("导出城池配置"))
        {
            if (BindMode != EditorMode.None)
            {
                EditorUtility.DisplayDialog("提示", "请先退出到普通模式", "确定");
            }
            else
            {
                m_buildingEditorWindow.ExportCsv();
            }
        }

        if (GUILayout.Button("导入城池配置"))
        {
            if (BindMode != EditorMode.None)
            {
                EditorUtility.DisplayDialog("提示", "请先退出到普通模式", "确定");
            }
            else
            {
                m_buildingEditorWindow.ImportCsv();
            }
        }

        if (GUILayout.Button("导出区域配置"))
        {
            if (BindMode != EditorMode.None)
            {
                EditorUtility.DisplayDialog("提示", "请先退出到普通模式", "确定");
            }
            else
            {
                ExportRegion();
            }
        }

        if (GUILayout.Button("导入区域配置"))
        {
            if (BindMode != EditorMode.None)
            {
                EditorUtility.DisplayDialog("提示", "请先退出到普通模式", "确定");
            }
            else
            {
                ImportCsv();
            }
        }

        EditorGUILayout.EndHorizontal();
        GUILayout.EndArea();
    }

    private void DrawRegion()
    {
        var griBuildData = m_selectItem.BuildData;
        if (!griBuildData)
        {
            GUILayout.Label("格子中无建筑");
            return;
        }

        EditorGUILayout.BeginHorizontal();

        GUILayout.Label($"格子中的建筑:{griBuildData.Name}_{griBuildData.CityLevel}");
        if (GUILayout.Button("删除建筑"))
        {
            m_selectItem.RemoveBuilding();
            if (m_selectItem.RegionData)
            {
                m_regionData.Remove(m_selectItem.RegionData);
            }
            m_buildingList.Remove(m_selectItem);
            m_selectItem = null;
        }

        EditorGUILayout.EndHorizontal();

        if (m_selectItem == null) return;
        if (!m_selectItem.RegionData && BindMode == EditorMode.None)
        {
            GUILayout.Label("该格子无区域信息");
            if (GUILayout.Button("创建并开始绑定区域"))
            {
                BindMode = EditorMode.Bind;
                var regionData = ScriptableObject.CreateInstance<EditorRegionData>();
                if (regionData)
                {
                    m_selectItem.SetRegionData(regionData);
                    m_regionData.Add(regionData);
                }
            }

            return;
        }

        if (m_selectItem.RegionData)
        {
            EditorUtil.DrawSo(m_selectItem.RegionData);
        }

        GUILayout.Space(15);

        GUILayout.Label($"区域格数:{m_selectItem.RegionData.Scope.Count + m_selectItem.RegionData.RegionGrid.Count }");
        GUILayout.Label($"子区域格数:{m_selectItem.RegionData.ZoneBuff.Count + m_selectItem.RegionData.ZoneGrid.Count}");

        GUILayout.Space(15);
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("退出/进入绑定模式"))
        {
            EnterOrExitBindMode(EditorMode.Bind);
        }

        if (GUILayout.Button("退出/进入删除模式"))
        {
            EnterOrExitBindMode(EditorMode.Delete);
        }


        if (GUILayout.Button("删除区域"))
        {
            m_selectItem.RegionData.ResetSubItemData();
            m_selectItem.RegionData.ResetItemData();
            m_regionData.Remove(m_selectItem.RegionData);
            m_selectItem.SetRegionData(null);
            BindMode = EditorMode.None;
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("退出/进入子区域绑定模式"))
        {
            EnterOrExitBindMode(EditorMode.SubBind);
        }

        if (GUILayout.Button("退出/进入子区域删除模式"))
        {
            EnterOrExitBindMode(EditorMode.SubDelete);
        }

        if (GUILayout.Button("删除子区域"))
        {
            if (m_selectItem == null || !m_selectItem.RegionData) return;
            m_selectItem.RegionData.ResetSubItemData();
            // m_selectItem.RegionData.RemoveSubRegionData();
            BindMode = EditorMode.None;
        }

        EditorGUILayout.EndHorizontal();
    }

    private void EnterOrExitBindMode(EditorMode mode)
    {
        lastDragTouchItem = null;
        var isSub = BindMode == EditorMode.SubBind || BindMode == EditorMode.SubDelete;
        //CheckAndAddRegion(m_selectItem, isSub);
        CheckAndAddRegion1(m_selectItem, isSub);
       

        BindMode = BindMode == mode ? EditorMode.None : mode;

        // if (!)
        // {
        //     EditorUtility.DisplayDialog("提示", "区域无法闭合", "确定");
        // }
        // if (BindMode != EditorMode.SubBind)
        // {
        //     if (!CheckAndAddRegion(isSub))
        //     {
        //         EditorUtility.DisplayDialog("提示", "区域无法闭合", "确定");
        //     }
        // }
    }

    private void HandleBuildingClick(EditorBuildingData data)
    {
        m_curBuildingData = data;
        DestroyImmediate(m_previewModle);

        m_selectItem = null;
        BindMode = EditorMode.None;
    }


    private void OnBuildingDelete(EditorBuildingData buildingData)
    {
        if (!buildingData) return;
        for (var index = 0; index < m_buildingList.Count; index++)
        {
            var item = m_buildingList[index];
            if (item.BuildData != buildingData) continue;
            item.RemoveBuilding();
            m_buildingList.Remove(item);
            index--;
        }

        if (m_curBuildingData == buildingData)
        {
            m_curBuildingData = null;
        }

        if (m_selectItem == null || m_selectItem.BuildData != buildingData) return;
        m_selectItem.RemoveBuilding();
        m_selectItem = null;
    }


    private void OnSceneGui(SceneView view)
    {
        // DrawGrid();
        HandleUtility.AddDefaultControl(GUIUtility.GetControlID(FocusType.Passive));
        switch (BindMode)
        {
            case EditorMode.None:
                ProcessPickItem();
                break;
            case EditorMode.Bind:
            case EditorMode.SubBind:
                ProcessBindItem();
                break;
            case EditorMode.Delete:
            case EditorMode.SubDelete:
                ProcessDeleteItem();
                break;
        }

        CheckInputEvent();
        if (Event.current.type != EventType.Repaint) return;
        CheckViewArea(view);
        Handles.BeginGUI();

        switch (BindMode)
        {
            case EditorMode.Bind:
                GUI.Label(new Rect(10, 10, 100, 100), "绑定模式");
                break;
            case EditorMode.Delete:
                GUI.Label(new Rect(10, 10, 100, 100), "删除模式");
                break;
            case EditorMode.SubBind:
                GUI.Label(new Rect(10, 10, 100, 100), "子区域绑定模式");
                break;
            case EditorMode.SubDelete:
                GUI.Label(new Rect(10, 10, 100, 100), "子区域删除模式");
                break;
        }

        Handles.EndGUI();

        DrawGrid(view);
        if (m_selectItem != null)
        {
            DrawSelectGrid();
        }
        DrawMapArea();
    }

    //检测是否有input事件
    private void CheckInputEvent()
    {
        var current = Event.current;
        if (current == null)
        {
            return;
        }

        EventType type = current.type;
        var hit = GetHitPoint(current.mousePosition);
        if (current.isMouse && current.type == EventType.MouseDown && current.button == 0)
        {
            float timeSinceLastClick = Time.time - lastClickTime;

            if (timeSinceLastClick <= 0.4f)
            {
                //这里保护一下 ，防止短时间多次触发双击
                lastClickTime = -1; // 重置上次点击时间，防止多次触发

                if (curViewPortLayer >= ViewPortLayer.Layer2 && !current.shift)
                {
                    int x = Mathf.RoundToInt(hit.point.x);
                    int y = Mathf.RoundToInt(hit.point.z);
                    //Debug.LogError($"点击了 {hit.point}");
                    JumpViewPoint(x,y);
                }
            }
            else
            {
                // 更新上次点击时间
                lastClickTime = Time.time;
            }
        }
    }

    //绘制地图边框区域线
    private void DrawMapArea()
    {
        if(m_listGrid == null || m_listGrid.Count == 0)
            return;
        int size = GridSize;
        Handles.color = Color.green;
        var point1 = new Vector3( -size / 2f,  EditorGridItem.AREA_HEIGHT, -size / 2f);
        var point2 = new Vector3(-size / 2f, EditorGridItem.AREA_HEIGHT, m_mapSize.y * size - size / 2f);
        var point3 = new Vector3( m_mapSize.x * size - size / 2f, EditorGridItem.AREA_HEIGHT, m_mapSize.y * size - size / 2f);
        var point4 = new Vector3(m_mapSize.x * size - size / 2f, EditorGridItem.AREA_HEIGHT, -size / 2f);
        Handles.DrawLine(point1, point2);
        Handles.DrawLine(point2, point3);
        Handles.DrawLine(point3, point4);
        Handles.DrawLine(point4, point1);
    }

    /// <summary>
    /// 监测视野区域
    /// </summary>
    private void CheckViewArea(SceneView view)
    {
        if (view)
        {
            Camera sceneCamera = view.camera;
            int instanceId = view.GetInstanceID();
            if (sceneCamera != null)
            {
                if (sceneCamera.orthographic != cameraOrthographic)
                    cameraOrthographic = sceneCamera.orthographic;
                float size = cameraOrthographic ? sceneCamera.orthographicSize : sceneCamera.nearClipPlane;  //fieldOfView不变，监测没意义
                SetViewLayer(cameraOrthographic,size);
            }
        }
    }
    
    //设置渲染层级
    private void SetViewLayer(bool ortho,float size)
    {
        //Debug.Log($"Size = {size}" );
        if (ortho)
        {
            if (size <= 0)
            {
                curViewPortLayer = ViewPortLayer.None;
                coordinateSystemsScale = 1;
                showPosTextSpace = 1;
            }else if (size < 10)
            {
                curViewPortLayer = ViewPortLayer.Layer1;
                coordinateSystemsScale = 1;
                showPosTextSpace = 1;
            }
            else if (size < 20)
            {
                curViewPortLayer = ViewPortLayer.Layer1;
                coordinateSystemsScale = 1;
                showPosTextSpace = 2;
            }
            else if (size < 30)
            {
                curViewPortLayer = ViewPortLayer.Layer2;
                coordinateSystemsScale = 1;
                showPosTextSpace = 3;
            }
            else if (size < 40)
            {
                curViewPortLayer = ViewPortLayer.Layer2;
                coordinateSystemsScale = 5;
            }
            else if (size < 100)
            {
                curViewPortLayer = ViewPortLayer.Layer3;
                coordinateSystemsScale = 10;
            }else if (size < 200)
            {
                curViewPortLayer = ViewPortLayer.Layer3;
                coordinateSystemsScale = 100;
            }
            else 
            {
                coordinateSystemsScale = 500;
                curViewPortLayer = ViewPortLayer.Layer4;
            }
        }
        else
        {
            if (size <= 0)
            {
                curViewPortLayer = ViewPortLayer.None;
                coordinateSystemsScale = 1;
            }
            else if (size < 0.1f)
            {
                curViewPortLayer = ViewPortLayer.Layer1;
                coordinateSystemsScale = 1;
            }
            else if (size < 0.2f)
            {
                curViewPortLayer = ViewPortLayer.Layer2;
                coordinateSystemsScale = 1;
            }
            else if (size < 0.3f)
            {
                curViewPortLayer = ViewPortLayer.Layer2;
                coordinateSystemsScale = 5;
            }
            else if (size < 1f)
            {
                curViewPortLayer = ViewPortLayer.Layer3;
                coordinateSystemsScale = 10;
            }else if (size < 2f)
            {
                curViewPortLayer = ViewPortLayer.Layer3;
                coordinateSystemsScale = 100;
            }
            else 
            {
                coordinateSystemsScale = 500;
                curViewPortLayer = ViewPortLayer.Layer4;
            }
        }
    }

    public EditorGridItem GetHaveEmptyGrid()
    {
        int minX = 0;
        int minY = 0;
        int maxX = 1000;
        int maxY = 1000;
        for (int i = minX; i < maxX; i++)
        {
            for (int j = minY; j < maxY; j++)
            {
                var item = GetBlockItem(i, j);
                if(item == null)
                    continue;
                if (item.RegionData == null)
                    return item;
            }
        }
        return null;
    }
    
    public List<EditorGridItem> GetHaveEmptyGrids()
    {
        List<EditorGridItem> items = new List<EditorGridItem>(8);
        int minX = 0;
        int minY = 0;
        int maxX = 1000;
        int maxY = 1000;
        for (int i = minX; i < maxX; i++)
        {
            for (int j = minY; j < maxY; j++)
            {
                var item = GetBlockItem(i, j);
                if(item == null)
                    continue;
                if (item.RegionData == null)
                    items.Add(item);
            }
        }
        return items;
    }


    #region  跳转

    /// <summary>
    /// 按点跳转，注意，当前直接2D视角了
    /// </summary>
    /// <param name="sceneView"></param>
    /// <param name="target"></param>
    /// <param name="size"></param>
    private void JumpViewPoint(int x,int y,SceneView sceneView = null,float size  = 5f)
    {
        //Debug.LogError($"jump to {x}，{y}");
        if (sceneView == null)
            sceneView = SceneView.lastActiveSceneView;
        if(sceneView == null)
            return;
        Vector3 target = new Vector3();
        target.x = x;
        target.y = y;
        target.z = EditorGridItem.AREA_HEIGHT;
        sceneView.pivot = target;
        sceneView.in2DMode = true;
        sceneView.rotation = Quaternion.Euler(45, 0, 0);
        sceneView.size = size;
        // 切换到正确的摄像视角
        sceneView.Repaint();
    }

    

    #endregion

    #region 区域绑定
    private void ProcessBindItem()
    {
        var current = Event.current;
        if (current == null)
        {
            return;
        }

        EventType type = current.type;
        // HandleUtility.AddDefaultControl(GUIUtility.GetControlID(FocusType.Passive));
        switch (type)
        {
            case EventType.MouseUp:
            case EventType.MouseDrag:
                if (current.button != 0) return;
                if (m_selectItem == null || !m_selectItem.RegionData) return;

                var hit = GetHitPoint(current.mousePosition);
                var gridItem = GetBlockItem(hit.point);
                if (gridItem == null) return;
                if (!current.shift)
                {
                    if (lastDragTouchItem != null && gridItem != lastDragTouchItem && lastDragTouchItem.GridPos != Vector2Int.zero)
                    {
                        var points = MathUtil.GetLinePoints(lastDragTouchItem.GridPos.x, lastDragTouchItem.GridPos.y, gridItem.GridPos.x,
                            gridItem.GridPos.y);
                        if (points.Count > 2)
                        {
                            //hashset不会添加重复的
                            // points.Remove(lastDragTouchItem.GridPos);
                            // points.Remove(gridItem.GridPos);
                            foreach (var point in points)
                            {
                                var grid = GetBlockItem((int)point.x,(int)point.y);
                                if(grid == null)
                                    continue;
                                TryAddBindPoint(BindMode, m_selectItem.RegionData, grid); 
                            }
                        }
                    }

                    TryAddBindPoint(BindMode, m_selectItem.RegionData, gridItem);   
                    if (type == EventType.MouseDrag)
                    {
                        lastDragTouchItem = gridItem;
                    }
                }
                //如果按下了shift 则自动补全
                else
                {
                    if (type == EventType.MouseUp)
                    {
                        if (lastShiftClickBindItem != null)
                        {
                            //自动补全line
                            var points = MathUtil.GetLinePoints(lastShiftClickBindItem.GridPos.x, lastShiftClickBindItem.GridPos.y, gridItem.GridPos.x,
                                gridItem.GridPos.y);
                            foreach (var point in points)
                            {
                                var grid = GetBlockItem((int)point.x,(int)point.y);
                                if(grid == null)
                                    continue;
                                TryAddBindPoint(BindMode, m_selectItem.RegionData, grid); 
                            }
                        }
                        else
                        {
                            TryAddBindPoint(BindMode, m_selectItem.RegionData, gridItem);   
                        }
                        lastShiftClickBindItem = gridItem;
                    }

                   
                }
                break;
                // case EventType.MouseDown:
                //     if (current.button != 0) break;
                //     m_isBindDrag = true;
                //     break;
                // case EventType.MouseUp:
                //     if (current.button != 0) break;
                //     m_isBindDrag = false;
                //     break;
                // case EventType.MouseMove:
                //     if (current.button != 0) break;
                //     if (!m_isBindDrag) break;
                //     Debug.Log("====>" + current.mousePosition);
                break;
        }
        switch (type)
        {
            case EventType.MouseUp:
                //清空上次拖动接触Grid
                lastDragTouchItem = null;
                break;
        }
        
        if (!current.shift && lastShiftClickBindItem != null)
        {
            lastShiftClickBindItem = null;
        }
    }

    private void TryAddBindPoint(EditorMode mode, EditorRegionData region, EditorGridItem gridItem)
    {
        switch (mode)
        {
            case EditorMode.Bind:
                region.TryAddBindPoint(gridItem);
                break;
            case EditorMode.SubBind:
                region.TryAddSubBindPoint(gridItem);
                break;
        }
    }

    private void ProcessDeleteItem()
    {
        var current = Event.current;
        if (current == null)
        {
            return;
        }

        EventType type = current.type;
        // HandleUtility.AddDefaultControl(GUIUtility.GetControlID(FocusType.Passive));

        if (type != EventType.MouseDrag && type != EventType.MouseUp) return;
        if (current.button != 0) return;
        if (m_selectItem == null || !m_selectItem.RegionData) return;


        var hit = GetHitPoint(current.mousePosition);
        var gridItem = GetBlockItem(hit.point);
        if (gridItem == null) return;

        switch (BindMode)
        {
            case EditorMode.Delete:
                m_selectItem.RegionData.TryRemoveBindPoint(gridItem);
                break;
            case EditorMode.SubDelete:
                m_selectItem.RegionData.TryRemoveSubBindPoint(gridItem);
                break;
        }
    }

    //检测区域是否合法
    private bool CheckAndAddRegion(EditorGridItem selectItem, bool isSubRegion = false)
    {
        //检测区域是否闭合 同时将闭合范围内的格子添加进绑定
        if (selectItem == null || !selectItem.RegionData) return false;
        var scope = selectItem.RegionData.Scope;

        if (isSubRegion)
        {
            scope = selectItem.RegionData.ZoneBuff;
        }

        if (isSubRegion)
        {
            selectItem.RegionData.ClearZoneGrid();
        }
        else
        {
            selectItem.RegionData.ClearRegionGrid();
        }

        // 如果点的数量少于3，则无法构成封闭区域
        if (scope.Count < 3) return false;

        //先算出一个边界
        var firstItem = scope.FirstOrDefault();
        if (firstItem == null)
        {
            // 处理集合为空的情况
            return false;
        }

        var minX = firstItem.GridPos.x;
        var maxX = minX;
        var minY = firstItem.GridPos.y;
        var maxY = minY;

        foreach (var item in scope)
        {
            minX = Mathf.Min(minX, item.GridPos.x);
            maxX = Mathf.Max(maxX, item.GridPos.x);
            minY = Mathf.Min(minY, item.GridPos.y);
            maxY = Mathf.Max(maxY, item.GridPos.y);
        }

        //广度优先 超出范围则不在区域内
        var queue = new Queue<EditorGridItem>();
        var visited = new HashSet<EditorGridItem>();

        // 从建筑的点开始遍历
        var startItem = selectItem;

        queue.Enqueue(startItem);
        visited.Add(startItem);

        // 检查相邻的四个方向
        var directions = new Vector2Int[]
        {
            Vector2Int.left, Vector2Int.right,
            Vector2Int.down, Vector2Int.up
        };
        var listContains = new List<EditorGridItem>((maxX - minX) * (maxY - minY));

        while (queue.Count > 0)
        {
            var currentItem = queue.Dequeue();

            foreach (var direction in directions)
            {
                var newPos = currentItem.GridPos + direction;
                if (newPos.x < minX || newPos.x > maxX || newPos.y < minY || newPos.y > maxY)
                {
                    if (!isSubRegion)
                    {
                        selectItem.RegionData.ResetSubItemData();
                    }
                    return false; // 直接算不合法
                }

                var neighbor = GetBlockItem(newPos);
                if (neighbor != null)
                {
                    if (!scope.Contains(neighbor) && visited.Add(neighbor))
                    {
                        queue.Enqueue(neighbor);
                    }
                    else
                    {
                        listContains.Add(neighbor);
                    }
                }
            }
        }

        // 移除未访问过的点，即超出范围的点
        foreach (var item in visited)
        {
            if (isSubRegion)
            {
                if (!selectItem.RegionData.ZoneGrid.Contains(item))
                {
                    selectItem.RegionData.TryAddZoneGrid(item);
                }
            }
            else
            {
                if (!selectItem.RegionData.Scope.Contains(item))
                {
                    selectItem.RegionData.TryAddRegionGrid(item);
                }
            }
        }

        //删除所有 孤立的格子 和画在区域里的格子 只留下边框
        var list = scope.ToList();
        var gridScope = selectItem.RegionData.RegionGrid;
        if (isSubRegion)
        {
            gridScope = selectItem.RegionData.ZoneGrid;
        }

        foreach (var item in list)
        {
            var count = 0;
            //周围没有非空的格子 就算他是孤立的格子
            foreach (var direction in directions)
            {
                var newPos = item.GridPos + direction;
                var newItem = GetBlockItem(newPos);
                if (!gridScope.Contains(newItem) && !scope.Contains(newItem))
                {
                    count++;
                }
            }

            var isIn = count <= 0;
            var isout = !listContains.Contains(item) && count > 2;
            if (isIn)
            {
                if (isSubRegion)
                {
                    selectItem.RegionData.TryRemoveSubBindPoint(item);
                    selectItem.RegionData.TryAddZoneGrid(item);
                }
                else
                {
                    selectItem.RegionData.TryRemoveBindPoint(item);
                    selectItem.RegionData.TryAddRegionGrid(item);
                }
            }

            if (isout)
            {
                if (isSubRegion)
                {
                    selectItem.RegionData.TryRemoveSubBindPoint(item);
                }
                else
                {
                    selectItem.RegionData.TryRemoveBindPoint(item);
                }
            }
        }

        selectItem.RegionData.CalculateSimpleData(isSubRegion);
        selectItem.RegionData.CalculatePolygonData(isSubRegion);
        return true;
    }
    
    Queue<EditorGridItem> queue = new Queue<EditorGridItem>();
    HashSet<EditorGridItem> visited = new HashSet<EditorGridItem>();
    HashSet<EditorGridItem> allVisitedScop = new HashSet<EditorGridItem>();
    List<EditorGridItem> listContains = new List<EditorGridItem>();
    //检测区域是否合法
    private int CheckAndAddRegion1(EditorGridItem selectItem, bool isSubRegion = false)
    {
        //检测区域是否闭合 同时将闭合范围内的格子添加进绑定
        if (selectItem == null || !selectItem.RegionData) return -1;
        var scope = selectItem.RegionData.Scope;

        if (isSubRegion)
        {
            scope = selectItem.RegionData.ZoneBuff;
        }

        if (isSubRegion)
        {
            selectItem.RegionData.ClearZoneGrid();
        }
        else
        {
            selectItem.RegionData.ClearRegionGrid();
        }

        // 如果点的数量少于3，则无法构成封闭区域
        if (scope.Count < 3) return -1;

        //先算出一个边界
        var firstItem = scope.FirstOrDefault();
        if (firstItem == null)
        {
            // 处理集合为空的情况
            return -1;
        }

        var minX = firstItem.GridPos.x;
        var maxX = minX;
        var minY = firstItem.GridPos.y;
        var maxY = minY;

        if (!isSubRegion && selectItem.RegionData.AllSelectScope.Count !=0 &&selectItem.RegionData.AllSelectScope.Count != scope.Count)
        {
            foreach (var item in selectItem.RegionData.AllSelectScope)
            {
                minX = Mathf.Min(minX, item.GridPos.x);
                maxX = Mathf.Max(maxX, item.GridPos.x);
                minY = Mathf.Min(minY, item.GridPos.y);
                maxY = Mathf.Max(maxY, item.GridPos.y);
            }
        }
        else
        {
            foreach (var item in scope)
            {
                minX = Mathf.Min(minX, item.GridPos.x);
                maxX = Mathf.Max(maxX, item.GridPos.x);
                minY = Mathf.Min(minY, item.GridPos.y);
                maxY = Mathf.Max(maxY, item.GridPos.y);
            }
        }
        //广度优先 超出范围则不在区域内
        queue.Clear();
        visited.Clear();
        allVisitedScop.Clear();

        // 从建筑的点开始遍历
        var startItem = selectItem;

        queue.Enqueue(startItem);
        visited.Add(startItem);

        // 检查相邻的四个方向
        var directions = new Vector2Int[]
        {
            Vector2Int.left, Vector2Int.right,
            Vector2Int.down, Vector2Int.up
        };
        listContains.Clear();

        while (queue.Count > 0)
        {
            var currentItem = queue.Dequeue();

            foreach (var direction in directions)
            {
                var newPos = currentItem.GridPos + direction;
                
                if (newPos.x < minX || newPos.x > maxX || newPos.y < minY || newPos.y > maxY)
                {
                    if (!isSubRegion)
                    {
                        selectItem.RegionData.ResetSubItemData();
                    }
                    return -1; // 直接算不合法
                }               
                var neighbor = GetBlockItem(newPos);
                if (neighbor != null)
                {
                    //如果非scope范围框且有区域，则表示进入了其他区域
                    bool contain = scope.Contains(neighbor);
                    if (!isSubRegion)
                    {
                        if (!contain && neighbor.BuildData == null && neighbor.RegionData != null )
                        {
                            allVisitedScop.Add(currentItem);
                            continue;
                        }
                    }
                    if (!contain && visited.Add(neighbor))
                    {
                        queue.Enqueue(neighbor);
                    }
                    else
                    {
                        listContains.Add(neighbor);
                    }
                }
            }
        }
        //只处理大区域
        if (allVisitedScop.Count > 0)
        {
            foreach (var item in allVisitedScop)
            {
                selectItem.RegionData.TryAddBindPoint(item);
            }
            CheckAndAddRegion1(selectItem,isSubRegion);
            return 1;
        }
        // 移除未访问过的点，即超出范围的点
        foreach (var item in visited)
        {
            if (isSubRegion)
            {
                if (!selectItem.RegionData.ZoneGrid.Contains(item))
                {
                    selectItem.RegionData.TryAddZoneGrid(item);
                }
            }
            else
            {
                if (!selectItem.RegionData.Scope.Contains(item))
                {
                    selectItem.RegionData.TryAddRegionGrid(item);
                }
            }
        }

        //删除所有 孤立的格子 和画在区域里的格子 只留下边框
        var list = scope.ToList();
        var gridScope = selectItem.RegionData.RegionGrid;
        if (isSubRegion)
        {
            gridScope = selectItem.RegionData.ZoneGrid;
        }

        foreach (var item in list)
        {
            var count = 0;
            //周围没有非空的格子 就算他是孤立的格子
            foreach (var direction in directions)
            {
                var newPos = item.GridPos + direction;
                var newItem = GetBlockItem(newPos);
                if (!gridScope.Contains(newItem) && !scope.Contains(newItem))
                {
                    count++;
                }
            }

            var isIn = count <= 0;
            var isout = !listContains.Contains(item) && count > 2;
            if (isIn)
            {
                if (isSubRegion)
                {
                    selectItem.RegionData.TryRemoveSubBindPoint(item);
                    selectItem.RegionData.TryAddZoneGrid(item);
                }
                else
                {
                    selectItem.RegionData.TryRemoveBindPoint(item);
                    selectItem.RegionData.TryAddRegionGrid(item);
                }
            }

            if (isout)
            {
                if (isSubRegion)
                {
                    selectItem.RegionData.TryRemoveSubBindPoint(item);
                }
                else
                {
                    selectItem.RegionData.TryRemoveBindPoint(item);
                }
            }
        }

        selectItem.RegionData.CalculateSimpleData(isSubRegion);
        selectItem.RegionData.CalculatePolygonData(isSubRegion);
        return 0;
    }


    

    #endregion

    #region 网格绘制和点击

    private void ProcessPickItem()
    {
        if (!isGen)
        {
            return;
        }

        var current = Event.current;
        if (current == null)
        {
            return;
        }

        EventType type = current.type;
        var hit = GetHitPoint(current.mousePosition);

        if (type == EventType.MouseUp && current.button == 0 && !current.alt)
        {
            // Debug.Log("====>" + hit.point);

            if (m_curBuildingData)
            {
                //放置建筑
                var gridItem = GetBlockItem(hit.point);

                if (string.IsNullOrEmpty(m_curBuildingData.ModelPath))
                {
                    ShowNotification(new GUIContent("该建筑没有模型无法放置"));
                    return;
                }

                if (gridItem != null && !CheckBuilding(gridItem) && !gridItem.RegionData)
                {
                    gridItem.SetBuilding(m_curBuildingData, m_gridRoot, BUILDPREFABPATH);
                    m_buildingList.Add(gridItem);
                }
                else
                {
                    ShowNotification(new GUIContent("该格在别的建筑范围内或不在地图中"));
                    return;
                }

                CancelSelectBuilding();
                m_selectItem = gridItem;
                Repaint();
            }
            else
            {
                TryPickItem(hit.point);
            }
        }

        SceneView.currentDrawingSceneView.Repaint();


        if (type == EventType.MouseMove && current.button == 0 && !current.alt)
        {
            ShowPreView(hit.point);
        }
    }

    private RaycastHit GetHitPoint(Vector2 mousePosition)
    {
        var ray = HandleUtility.GUIPointToWorldRay(mousePosition);
        RaycastHit hit = new RaycastHit();
        hit.normal = Vector3.up;
        hit.point = ray.origin + ray.direction * (Mathf.Abs(ray.origin.y / (Vector3.Dot(ray.direction, Vector3.up))));
        // HandleUtility.AddDefaultControl(GUIUtility.GetControlID(FocusType.Passive));
        return hit;
    }

    private void CancelSelectBuilding()
    {
        DestroyImmediate(m_previewModle);

        m_selectItem = null;
        m_curBuildingData = null;
        m_buildingEditorWindow.ReSetSelectBuild();
        BindMode = EditorMode.None;
    }

    private bool CheckBuilding(EditorGridItem gridItem)
    {
        foreach (var item in m_buildingList)
        {
            if (item == null || !item.BuildData) continue;
            //x方向碰撞检测 简单写下
            if (Mathf.Abs(item.GridPos.x - gridItem.GridPos.x) < item.BuildData.Size / 2f + m_curBuildingData.Size / 2f)
            {
                //y方向碰撞检测
                if (Mathf.Abs(item.GridPos.y - gridItem.GridPos.y) <
                    item.BuildData.Size / 2f + m_curBuildingData.Size / 2f)
                {
                    return true;
                }
            }
        }

        return false;
    }

    private bool CheckGridBuilding(EditorGridItem gridItem)
    {
        foreach (var item in m_buildingList)
        {
            //x方向碰撞检测 简单写下
            if (Mathf.Abs(item.GridPos.x - gridItem.GridPos.x) < item.BuildData.Size / 2f + gridItem.Size / 2f)
            {
                //y方向碰撞检测
                if (Mathf.Abs(item.GridPos.y - gridItem.GridPos.y) <
                    item.BuildData.Size / 2f + gridItem.Size / 2f)
                {
                    return true;
                }
            }
        }

        return false;
    }

    private void TryPickItem(Vector3 pos)
    {
        var item = GetBlockItem(pos);
        if (item == null)
        {
            m_selectItem = null;
            Repaint();
        }
        else if (m_selectItem == null || m_selectItem.PosID != item.PosID)
        {
            m_selectItem = item;
            m_selectPos = item.BlockGridPos;
            Repaint();
        }
    }

    private EditorGridItem GetBlockItem(Vector3 pos)
    {
        var gridPos = EditorGridItem.GetGridPos(pos, GridSize);

        var posId = EditorGridItem.GetPosID(gridPos);
        m_listGrid.TryGetValue(posId, out var item);
        return item;
    }

    private EditorGridItem GetBlockItem(Vector2Int gridPos)
    {
        var posId = EditorGridItem.GetPosID(gridPos);
        m_listGrid.TryGetValue(posId, out var item);
        return item;
    }
    private EditorGridItem GetBlockItem(int x,int y )
    {
        var posId = x * 1000 + y;
        m_listGrid.TryGetValue(posId, out var item);
        return item;
    }

    private void GenMap()
    {
        // m_buildingList = AssetDatabase.LoadAssetAtPath<EditorGridItemTemp>(m_buildingEditorWindow.SavePath + "/GridItemDataTemp.asset");
        // if (!m_buildingList)
        // {
        //     m_buildingList = ScriptableObject.CreateInstance<EditorGridItemTemp>();
        // }
        // foreach (var item in m_buildingList.BuildingList)
        // {
        //     m_listGrid.Add(item.PosID, item);
        // }
        if (!CheckScene())
        {
            return;
        }

        for (int i = 0; i < m_mapSize.x; i++)
        {
            for (int j = 0; j < m_mapSize.y; j++)
            {
                var pos = new Vector2(i * GridSize, j * GridSize);
                var grid = new EditorGridItem(pos, GridSize, m_gridRoot.transform);
                if (m_listGrid.ContainsKey(grid.PosID))
                {
                    continue;
                }

                m_listGrid.Add(grid.PosID, grid);
            }
        }

        var mainCamera = GameObject.Find("Main Camera");
        if (mainCamera)
        {
            var ctrl =mainCamera.GetComponent<MapCameraCtrl>();
            if(ctrl == null)
                ctrl = mainCamera.AddComponent<MapCameraCtrl>();
            ctrl.mapWidth = m_mapSize.x * GridSize;
            ctrl.mapHeight = m_mapSize.y * GridSize;
            ctrl.gridSize = GridSize;
        }

        isGen = true;
        Repaint();
    }

    // private ConcurrentDictionary<int, EditorGridItem> m_neddDrawList = new ConcurrentDictionary<int, EditorGridItem>();

    private void DrawGrid(SceneView view)
    {
        if (!isGen) return;

        // Handles.color = Color.red;
        // float maxDistanceForCulling = m_viewSize;
        var pos = GetHitPoint(new Vector2(view.position.width / 2, view.position.height / 2)).point;
        // maxDistanceForCulling *= maxDistanceForCulling;
        // m_neddDrawList.Clear();
        // Parallel.ForEach(m_listGrid.Values,(item) =>
        // {
        //     var dir = item.WorldPos - pos;
        //     if (dir.sqrMagnitude > maxDistanceForCulling * maxDistanceForCulling)
        //     {
        //         return;
        //     }
        //
        //     m_neddDrawList.TryAdd(item.PosID, item);
        //     // if (!EditorUtil.IsPointInView(item.WorldPos, view.camera))
        //     // {
        //     //     continue;
        //     // }
        // });

        // foreach (var item in m_listGrid.Values)
        // {
        //     var dir = item.WorldPos - pos;
        //     if (dir.sqrMagnitude > maxDistanceForCulling * maxDistanceForCulling)
        //     {
        //         continue;
        //     }
        //
        //     // if (!EditorUtil.IsPointInView(item.WorldPos, view.camera))
        //     // {
        //     //     continue;
        //     // }
        //
        //     if (item.RegionData)
        //     {
        //         if (m_isBuindingDraw)
        //         {
        //             if (!CheckGridBuilding(item))
        //             {
        //                 item.DrawRegion();
        //             }
        //         }
        //         else
        //         {
        //             item.DrawRegion();
        //         }
        //     }
        //     else
        //     {
        //         Handles.DrawSolidRectangleWithOutline(item.BlockGridPos, Color.clear, Color.red);
        //     }
        //
        //     item.DrawText();
        // }
        // var gridPos = EditorGridItem.GetGridPos(pos, m_gridSize);
        // var minX = gridPos.x - m_viewSize;
        // var maxX = gridPos.x + m_viewSize;
        // var minY = gridPos.y - m_viewSize;
        // var maxY = gridPos.y + m_viewSize;
        // var size = m_gridSize;
        
        //优化方案：1，通过监测场景摄像机的缩放（镜头远近的视口）来控制绘制的格子数 --2D正交模式已完成，基本不卡了 3D透视视角有需要再做了
        // 2，通过远近，控制耗时的Handles.Label函数的调用次数（接口本身耗+GC）（a,拉远一定位置，坐标文本不再显示,b，如果还是卡，把文本何在一起刷新）
        // 3，优化大量数据时，Dic会存在hash命中问题（可修改成list array来优化） TODO
        //计算范围，取的视野最中间的cell来向四周扩散
        var gridPos = EditorGridItem.GetGridPos(pos, GridSize);
        //缩放为1时  Y放置3个 928/3 =310
        float cell_size = 310;
        int width = (int)((view.position.width / cell_size ) * view.size  + 0.5f) + 2;    //+0.5是四舍五入 +2是扩展
        int length = (int)((view.position.height / cell_size ) * view.size + 0.5f) + 2;
        var minX = gridPos.x - width / 2;
        minX = Mathf.Clamp(minX, 0, m_mapSize.x);
        var maxX = gridPos.x + width / 2;
        maxX = Mathf.Clamp(maxX, 0, m_mapSize.x);
        var minY = gridPos.y - length /2;
        minY = Mathf.Clamp(minY, 0, m_mapSize.y);
        var maxY = gridPos.y + length / 2;
        maxY = Mathf.Clamp(maxY, 0, m_mapSize.y);
        var size = GridSize;
        bool showMeshText = (int)curViewPortLayer <= (int)ViewPortLayer.Layer1;
        bool needDrawBorder = (int)curViewPortLayer <= (int)ViewPortLayer.Layer2;
        bool needDrawPreviewMap = (int)curViewPortLayer >= (int)ViewPortLayer.Layer2;
        for (int j = minY; j <= maxY; j++)
        {
            if (j % coordinateSystemsScale == 0)
            {
                var columnStart = new Vector3(minX * size - size / 2f,  EditorGridItem.AREA_HEIGHT, j * size - size / 2f);
                var columnEnd = new Vector3(maxX * size - size / 2f, EditorGridItem.AREA_HEIGHT, j * size - size / 2f);
                Handles.color = Color.red;
                Handles.DrawLine(columnStart, columnEnd);
                if (minX <= 0)
                {
                    //刻上尺度
                    columnStart.x -= size;
                    //columnStart.z += size/2f;
                    Handles.Label(columnStart, j.ToString(), coordinateSystemsStyle);
                }
            }
        }
       
        //Debug.LogError($"当前x {width} : {minX}- {maxX} y {length}:{minY}-{maxY} ");
        for (int i = minX; i <= maxX; i++)
        {
            if (i % coordinateSystemsScale == 0)
            {
                var rowStart = new Vector3(i * size - size / 2f, EditorGridItem.AREA_HEIGHT, 0 );
                var rowEnd = new Vector3(i * size- size / 2f , EditorGridItem.AREA_HEIGHT, m_mapSize.y * size- size / 2f);
                Handles.color = Color.red;
                Handles.DrawLine(rowStart, rowEnd);
                if (minY <= 0)
                {
                    //刻上尺度
                    //rowStart.x += size/2f;
                    rowStart.z -= size;
                    Handles.Label(rowStart,i.ToString(),coordinateSystemsStyle);
                }
            }
            if (!needDrawPreviewMap)
            {
                
                
                for (int j = minY; j <= maxY; j++)
                {
                    var item = GetBlockItem(i, j);
                    if (item == null) continue;
                    if (item.RegionData)
                    {
                        if (m_isBuindingDraw)
                        {
                            if (!CheckGridBuilding(item))
                            {
                                item.DrawRegion(BindMode,needDrawBorder);
                            }
                        }
                        else
                        {
                            var canShow = m_hideViewAreaId == -1 || item.RegionData.ID == m_hideViewAreaId;
                            if (canShow)
                            {
                                item.DrawRegion(BindMode, needDrawBorder);
                            }
                        }
                    }
                    if (showMeshText)
                    {
                        if (i % showPosTextSpace == 0 && j % showPosTextSpace == 0)
                        {
                            item.DrawText();
                        }
                    }
                }
            }
        }
        if (needDrawPreviewMap)
        {
            //如果在预览模式，则需要把当前选中得区域
            if (m_selectItem != null && m_selectItem.RegionData != null)
            {
                switch (BindMode)
                {
                    case EditorMode.Bind:
                    case EditorMode.Delete:
                        foreach (var grid in m_selectItem.RegionData.Scope)
                        {
                            grid.DrawRegion(BindMode,true);
                        }
                        break;
                    case EditorMode.SubBind:
                    case EditorMode.SubDelete:
                        foreach (var grid in m_selectItem.RegionData.Scope)
                        {
                            grid.DrawRegion(BindMode,true);
                        }
                        foreach (var grid in m_selectItem.RegionData.ZoneBuff)
                        {
                            grid.DrawRegion(BindMode,true);
                        }
                        break;
                }
            }
            foreach (var region in m_regionData)
            {
                if(region == null)
                    continue;
                //如果当前是绑定模式，这绑定的region 不渲染
                if((BindMode == EditorMode.Bind|| BindMode == EditorMode.SubBind) && m_selectItem != null && m_selectItem.RegionData !=null && region == m_selectItem.RegionData)
                    continue;
                //region.DrawPreviewPolygon();
                region.DrawRegionPreview(false,false);
                region.DrawRegionPreview(true,false);
                if(m_selectItem == null||m_selectItem.RegionData == null)
                    continue;
                int count = region.RegionGrid.Count + region.Scope.Count;
                Handles.color = Color.green;
                int index = region.RegionGrid.Count / 2;
                if (region.RegionGrid.Count > index)
                {
                    var grid = region.RegionGrid.FirstOrDefault();
                    Handles.Label(grid.WorldPos,$"区域Id{grid.RegionData.ID} 大小 {count}",reginDataStyle);
                }
            }
        }
        
        if (TestBool)
        {
            if (m_selectItem != null && m_selectItem.RegionData != null)
            {
                // List<Vector2Int> another = new List<Vector2Int>(32);
                // for(int  m = 0;m < 200;m++)
                // {
                //     Region2DData data = new Region2DData();
                //     var region = m_selectItem.RegionData;
                //     foreach (var v in region.Scope)
                //     {
                //         data.SetPointState(v.GridPos.x,v.GridPos.y,1);
                //     }
                //     foreach (var v in region.RegionGrid)
                //     {
                //         data.SetPointState(v.GridPos.x,v.GridPos.y,1);
                //     }
                //
                //     var PolygonPoints = MathUtil.GetPolygonPathInArea(data.Data, data.MinX-data.StartX, data.MaxX-data.StartX, data.MinY-data.StartY, data.MaxY-data.StartY,false);
                //     //注意要处理一下偏移
                //     another.Clear();
                //     for (int i = 0; i < PolygonPoints.Count; i++)
                //     {
                //         another.Add(new Vector2Int(PolygonPoints[i].x+ data.StartX,PolygonPoints[i].y+data.StartY));
                //     }
                // }

                // foreach (var p in another)
                // {
                //     var item = GetBlockItem(p.x, p.y);
                //     if(item == null)
                //         continue;
                //     Handles.DrawSolidRectangleWithOutline(item.BlockGridPos, Color.blue, Color.green);
                // }
                if(m_selectItem.RegionData.Region2DData == null)
                    return;
                var PolygonPoints = m_selectItem.RegionData.Region2DData.PolygonPoints;
                if(PolygonPoints == null)
                    return;
                foreach (var p in PolygonPoints)
                {
                    var item = GetBlockItem(p.x, p.y);
                    if(item == null)
                        continue;
                    Handles.DrawSolidRectangleWithOutline(item.BlockGridPos, Color.blue, Color.green);
                }
                if(m_selectItem.RegionData.SubRegion2DData == null)
                    return;
                PolygonPoints = m_selectItem.RegionData.SubRegion2DData.PolygonPoints;
                if(PolygonPoints == null)
                    return;
                foreach (var p in PolygonPoints)
                {
                    var item = GetBlockItem(p.x, p.y);
                    if(item == null)
                        continue;
                    Handles.DrawSolidRectangleWithOutline(item.BlockGridPos, Color.red, Color.green);
                }


                // foreach (var v in m_selectItem.RegionData.Scope)
                // {
                //     var item = v;
                //     if (m_isBuindingDraw)
                //     {
                //         if (!CheckGridBuilding(item))
                //         {
                //             item.DrawRegion(BindMode,needDrawBorder);
                //         }
                //     }
                //     else
                //     {
                //         Handles.DrawSolidRectangleWithOutline(item.BlockGridPos, Color.blue, Color.green);
                //     }
                // }

            }

          
            
        }
    }

    private void DrawSelectGrid()
    {
        if (BindMode != EditorMode.None) return;
        //如果是建筑，我们将不着色
        if (m_selectItem.BuildData != null)
        {
            Handles.color = Color.green;
            Handles.DrawPolyLine(m_selectItem.BlockGridPos);
        }
        else
        {
            Handles.color = Color.green;
            Handles.DrawSolidRectangleWithOutline(m_selectItem.BlockGridPos, Color.green, Color.black);
        }
        // HandleUtility.handleMaterial.SetPass(0);
        // GL.PushMatrix();
        // GL.MultMatrix(Handles.matrix);
        // if (m_selectItem != null)
        // {
        //     Color c = Color.green;
        //     GL.Begin(GL.TRIANGLES);
        //     for (int index = 0; index < 2; ++index)
        //     {
        //         GL.Color(c);
        //         GL.Vertex(m_selectItem.BlockGridPos[index * 2]);
        //         GL.Vertex(m_selectItem.BlockGridPos[index * 2 + 1]);
        //         GL.Vertex(m_selectItem.BlockGridPos[(index * 2 + 2) % 4]);
        //         GL.Vertex(m_selectItem.BlockGridPos[index * 2]);
        //         GL.Vertex(m_selectItem.BlockGridPos[(index * 2 + 2) % 4]);
        //         GL.Vertex(m_selectItem.BlockGridPos[index * 2 + 1]);
        //     }
        //     GL.End();
        // }
        // GL.PopMatrix();
    }

    private void ShowPreView(Vector3 hitPoint)
    {
        if (!m_curBuildingData) return;

        if (!m_buildingPreview)
        {
            var buildPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(BUILDPREFABPATH);
            if (!buildPrefab)
            {
                Debug.LogError("找不到预览预制体");
                return;
            }

            m_buildingPreview = GameObject.Instantiate(buildPrefab, m_gridRoot.transform);

            m_model = m_buildingPreview.transform.Find("Model");
            if (!m_model)
            {
                Debug.LogError("建筑预制体错误");
            }
        }

        if (m_curBuildingData && !m_previewModle)
        {
            var editorModelPath = AssetDatabase.GetAssetPathsFromAssetBundle(m_curBuildingData.ModelPath);

            if (editorModelPath == null || editorModelPath.Length <= 0)
            {
                return;
            }

            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(editorModelPath[0]);
            if (!prefab)
            {
                return;
            }

            m_previewModle = Instantiate(prefab, m_model.transform);
            m_previewModle.transform.localPosition = Vector3.zero;
        }
        //预览建筑Y偏移 目前重置为0
        m_buildingPreview.transform.position = new Vector3(hitPoint.x + m_curBuildingData.OffSet.x, 0,
            hitPoint.z + m_curBuildingData.OffSet.y);
        // Debug.Log("===>" + hitPoint);
    }

    #endregion

    #region 导入导出

    private void ExportRegion()
    {
        var exportPath = EditorUtility.SaveFilePanel("选择路径", "", "MapRegion.csv", "csv");
        var dataList = new List<ExportRegionData>();
        var sb = new StringBuilder();
        foreach (var item in m_buildingList)
        {
            if (!item.RegionData) continue;
            
            var data = new ExportRegionData();
            data.RegionID = item.RegionData.ID;
            data.CityID = item.BuildData.Id;
            data.CityLevel = item.BuildData.CityLevel;
            data.CityPos = $"{item.GridPos.x}#{item.GridPos.y}";
            data.RegionSize = item.RegionData.RegionGrid.Count + item.RegionData.Scope.Count + item.RegionData.ZoneBuff.Count + item.RegionData.ZoneGrid.Count;
            // 区域范围
            sb.Clear();
            if (item.RegionData.Region2DData != null)
            {
                foreach (var it in item.RegionData.Region2DData.PolygonPoints)
                {
                    sb.Append($"{it.x}#{it.y}:");
                }
            }
            if(sb.Length >= 2)
                sb.Remove(sb.Length - 1, 1);
            data.Scope = sb.ToString();
            
            sb.Clear();
            foreach (var it in item.RegionData.regionSimpleGrid)
            {
                for (int i = 0; i < it.Value.Count; i++)
                {
                    sb.Append($"{it.Value[i].Item1}#{it.Key}:");
                    if(i == it.Value.Count-1)
                        sb.Append($"{it.Value[i].Item2}#{it.Key};");
                    else
                        sb.Append($"{it.Value[i].Item2}#{it.Key}:");
                }
            }
            if(sb.Length >= 2)
                sb.Remove(sb.Length - 1, 1);
            data.ScopeSc = sb.ToString();
            
            sb.Clear();
            // 区域范围
            foreach (var it in item.RegionData.subRegionSimpleGrid)
            {
                for (int i = 0; i < it.Value.Count; i++)
                {
                    sb.Append($"{it.Value[i].Item1}#{it.Key}:");
                    if(i == it.Value.Count-1)
                        sb.Append($"{it.Value[i].Item2}#{it.Key};");
                    else
                        sb.Append($"{it.Value[i].Item2}#{it.Key}:");
                }
            }
            if(sb.Length >= 2)
                sb.Remove(sb.Length - 1, 1);
            data.ZoneBuff = sb.ToString();
            
            data.Color = ColorUtility.ToHtmlStringRGBA(item.RegionData.RegionColor).Replace("#", "");
            data.ResourcesDeploy = item.RegionData.ResourcesDeploy;
            data.MonsterNumber = item.RegionData.MonsterNumber;
            data.LowNumber = item.RegionData.LowNumber;
            data.RenovateNumber = item.RegionData.RenovateNumber;
            data.Ratio = item.RegionData.Ratio;
            sb.Clear();
            foreach (var grid in item.RegionData.Scope)
            {
                sb.Append($"{grid.GridPos.x}#{grid.GridPos.y}:");
            }

            if (sb.Length >= 2)
            {
                sb.Remove(sb.Length - 1, 1);
            }
            data.EditorScope = sb.ToString();
            
            sb.Clear();
            foreach (var grid in item.RegionData.ZoneBuff)
            {
                sb.Append($"{grid.GridPos.x}#{grid.GridPos.y}:");
            }
            if (sb.Length >= 2)
            {
                sb.Remove(sb.Length - 1, 1);
            }
            data.EditorZoneBuff = sb.ToString();
            sb.Clear();
            dataList.Add(data);
        }
        EditorUtility.DisplayDialog("提示", EditorUtil.ExportCsv(exportPath, dataList) ? "导出成功" : "导出失败", "确定");
    }

    private void GetServerScope(HashSet<EditorGridItem> newGrid,HashSet<EditorGridItem> regionGrid , StringBuilder sb)
    {
        var editorGridItems = newGrid.GroupBy(t => t.GridPos.y).Select((gruop) => gruop.ToList()).ToList();
        editorGridItems.Reverse();

        foreach (var rowGrid in editorGridItems)
        {
            var max = rowGrid.Max(t => t.GridPos.x);
            var min = rowGrid.Min(t => t.GridPos.x);
            var list = new List<EditorGridItem>();
            var isflag = false;
            for (var j = min; j <= max; j++)
            {
                var nextItem = GetBlockItem(new Vector2Int(j, rowGrid[0].GridPos.y));

                if (newGrid.Contains(nextItem))
                {
                    list.Add(nextItem);
                    if (!isflag)
                    {
                        continue;
                    }

                    var nextNextItem = GetBlockItem(new Vector2Int(nextItem.GridPos.x + 1, rowGrid[0].GridPos.y));
                    if (!regionGrid.Contains(nextNextItem))
                    {
                        isflag = false;
                        continue;
                    }

                    isflag = false;
                }
                else if (regionGrid.Contains(nextItem))
                {
                    list.Add(nextItem);
                    isflag = true;
                    continue;
                }

                if (list.Count <= 0) continue;
                var fast = list.First();
                var lastPoint = list.Last();
                sb.Append(
                    $"{fast.GridPos.x}#{fast.GridPos.y}:{lastPoint.GridPos.x}#{lastPoint.GridPos.y};");
                list.Clear();
                var lastNextItem = GetBlockItem(new Vector2Int(lastPoint.GridPos.x + 1, rowGrid[0].GridPos.y));
                if (regionGrid.Contains(lastNextItem))
                {
                    list.Add(lastPoint);
                }
            }

            if (list.Count <= 0) continue;
            var fastrow = list.First();
            var lastPointrow = list.Last();
            sb.Append(
                $"{fastrow.GridPos.x}#{fastrow.GridPos.y}:{lastPointrow.GridPos.x}#{lastPointrow.GridPos.y};");
            list.Clear();
        }

        if (sb.Length >= 2)
        {
            sb.Remove(sb.Length - 1, 1);
        }
    }

    private void ImportCsv()
    {
        var importPath = EditorUtility.OpenFilePanel("选择文件", "", "csv");
        List<ExportRegionData> dataList = EditorUtil.ImportCsv<List<ExportRegionData>>(importPath);
        if (dataList == null || dataList.Count <= 0)
        {
            EditorUtility.DisplayDialog("提示", "导入失败", "确定");
            return;
        }

        //删除旧数据
        foreach (var item in m_buildingList)
        {
            if (!item.RegionData) continue;
            item.RegionData.ResetSubItemData();
            item.RegionData.ResetItemData();
            item.SetRegionData(null);
        }

        m_buildingList.Clear();

        //转换为EditorRegionData
        foreach (var data in dataList)
        {
            var regionData = ScriptableObject.CreateInstance<EditorRegionData>();
            m_regionData.Add(regionData);
            regionData.ID = data.RegionID;
            regionData.RegionColor =
                ColorUtility.TryParseHtmlString($"#{data.Color}", out var color) ? color : Color.white;
            regionData.ResourcesDeploy = data.ResourcesDeploy;
            regionData.MonsterNumber = data.MonsterNumber;
            regionData.LowNumber = data.LowNumber;
            regionData.RenovateNumber = data.RenovateNumber;
            regionData.Ratio = data.Ratio;
            
            if (!string.IsNullOrEmpty(data.EditorScope))
            {
                foreach (var grid in data.EditorScope.Split(':'))
                {
                    if(string.IsNullOrEmpty(grid))
                        continue;
                    var gridPos = grid.Split('#');
                    var item = GetBlockItem(new Vector2Int(int.Parse(gridPos[0]), int.Parse(gridPos[1])));
                    if (item != null)
                    {
                        regionData.Scope.Add(item);
                    }
                }
            }

            if (!string.IsNullOrEmpty(data.EditorZoneBuff))
            {
                foreach (var grid in data.EditorZoneBuff.Split(':'))
                {
                    if(string.IsNullOrEmpty(grid))
                        continue;
                    var gridPos = grid.Split('#');
                    var item = GetBlockItem(new Vector2Int(int.Parse(gridPos[0]), int.Parse(gridPos[1])));
                    if (item != null)
                    {
                        regionData.ZoneBuff.Add(item);
                    }
                }
            }

            var buildingData = m_buildingEditorWindow.GetBuildingData(data.CityID, data.CityLevel);
            if (!buildingData)
            {
                EditorUtility.DisplayDialog("提示", $"导入失败没有{data.CityID}建筑数据", "确定");
                return;
            }

            var gridItem = GetBlockItem(new Vector2Int(int.Parse(data.CityPos.Split('#')[0]),
                int.Parse(data.CityPos.Split('#')[1])));
            if (gridItem == null)
            {
                EditorUtility.DisplayDialog("提示", $"导入失败", "确定");
                return;
            }

            gridItem.SetBuilding(buildingData, m_gridRoot, BUILDPREFABPATH);
            gridItem.SetRegionData(regionData);
            foreach (var scope in regionData.Scope)
            {
                regionData.TryAddBindPoint(scope);
            }
            foreach (var scope in regionData.ZoneBuff)
            {
                regionData.TryAddSubBindPoint(scope);
            }
            CheckAndAddRegion(gridItem);
            CheckAndAddRegion(gridItem, true);

            m_buildingList.Add(gridItem);
        }

        EditorUtility.DisplayDialog("提示", "导入成功", "确定");
    }

    #endregion
}