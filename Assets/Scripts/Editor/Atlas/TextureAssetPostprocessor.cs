using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

class TextureAssetPostprocessor : AssetPostprocessor
{
    public static bool Disabled = false;
    static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets,
                                            string[] movedAssets, string[] movedFromAssetPaths)
    {
        if (Disabled)
            return;

        System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
        stopWatch.Start();

        AssetDatabase.StartAssetEditing();

        List<string> assetsToImport = new List<string>();
        bool dirty = false,textureDirty;
        int modifyCount = 0;
        foreach (var str in importedAssets)
        {
            textureDirty = OnProcessTexture(str);
            if(textureDirty)
            {
                //UnityEngine.Debug.LogWarning("deal texture :" + str);
                modifyCount++;
                assetsToImport.Add(str);
            }
            dirty |= textureDirty;
        }
        if(dirty)
        {
            //AssetDatabase.SaveAssets();
            ImportAssets(assetsToImport);

            stopWatch.Stop();
            UnityEngine.Debug.LogWarning("自定义处理图片格式完成，耗时:" + stopWatch.ElapsedMilliseconds + " ms,修改数量:" + modifyCount);
        }
        else
        {
            stopWatch.Stop();
        }

        AssetDatabase.StopAssetEditing();
    }

    static bool IsAssetResource(string filePath)
    {
        string fullPath = Path.GetFullPath(filePath);
        fullPath = fullPath.Replace("\\", "/");

        if (fullPath.Contains(Application.dataPath))
        {
            return true;
        }
        return false;
    }

    private static bool OnProcessTexture(string assetPath)
    {
        if(!IsAssetResource(assetPath))
        {
            return false;
        }
        var textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
        if(textureImporter == null)
        {
            return false;
        }
        bool dirty = false;

        //IOS平台格式处理
        bool iosDirty = OnProcessIOSTexture(textureImporter);
        if(iosDirty)
        {
            EditorUtility.SetDirty(textureImporter);
            //AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
        }
        dirty |= iosDirty;
        //其他平台处理在此添加

        return dirty;
    }

    static bool OnProcessIOSTexture(TextureImporter textureImporter)
    {
        if (textureImporter.textureType == TextureImporterType.Sprite || textureImporter.textureType == TextureImporterType.Default)
        {
            //IOS平台使用格式
            var format = PlatformSettingCfg.Texture_IOS_Default_ImporterFormat;
            if(textureImporter.assetPath.Contains("SDF"))
            {
                format = TextureImporterFormat.RGB24;
            }
            string platformString = "iPhone";

            //The values for the chosen platform are returned in the "out" parameters. The options for the platform string are "Standalone", 
            //"Web", "iPhone", "Android", "WebGL", "Windows Store Apps", "PS4", "XboxOne", "Nintendo 3DS" and "tvOS".
            TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(platformString);

            if (!textureImporterPlatformSettings.overridden
                || textureImporterPlatformSettings.format != format
            )
            {
                textureImporterPlatformSettings.overridden = true;
                textureImporterPlatformSettings.format = format;

                textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                //textureImporter.SaveAndReimport();

                return true;
            }
        }
        return false;
    }

    private static void ImportAssets(List<string> paths)
    {
        // When using the cache server we have to write all import settings to disk first.
        // Then perform the import (Otherwise the cache server will not be used for the import)
        foreach (string path in paths)
        {
            AssetDatabase.WriteImportSettingsIfDirty(path);
        }
        foreach (string path in paths)
        {
            AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
        }
    }
}
