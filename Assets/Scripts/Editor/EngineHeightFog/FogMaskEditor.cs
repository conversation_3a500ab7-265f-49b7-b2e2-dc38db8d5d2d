using UnityEngine;
using UnityEditor;
using System.IO;

namespace MiniGame.Fog
{
    [CustomEditor(typeof(EngineHeightFog))]
    public class FogMaskEditor : Editor
    {
        private bool isDrawingMode = false;
        private Texture2D fogMaskTexture;
        private Rect canvasRect = new Rect(-50, -50, 100, 100);
        private Vector2 canvasSize = new Vector2(100, 177);
        private Vector2 canvasCenter = new Vector2(0,65);
        private int textureResolution = 128;

        private bool showCanvasSettings = false;
        private bool showMaskSettings = false;
        private Color maskColor = Color.white;
        private float brushSize = 5f;
        private float brushStrength = 1f;

        private static readonly string MASK_TEXTURE_PATH = "Assets/cysoldierssortie/Shader/EngineFog/Textures/";

        private EngineHeightFog fogComponent;

        private void OnEnable()
        {
            SceneView.duringSceneGui += OnSceneGUI;
            fogComponent = (EngineHeightFog)target;
            InitializeMaskTexture();
            SyncCanvasDataFromComponent();
        }

        private void OnDisable()
        {
            SceneView.duringSceneGui -= OnSceneGUI;
        }

        private void UploadGPU()
        {
            if (isDrawingMode)
            {
                Shader.SetGlobalFloat("_FogMaskEnabled", 1);
                Shader.SetGlobalTexture("_FogMaskTexture", fogMaskTexture);
                Shader.SetGlobalVector("_FogMaskCenter", canvasCenter);
                Shader.SetGlobalVector("_FogMaskSize", canvasSize);
            }
            else
            {
                Shader.SetGlobalFloat("_FogMaskEnabled", 0);
            }
        }

        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Fog Mask Control", EditorStyles.boldLabel);

            showCanvasSettings = EditorGUILayout.Foldout(showCanvasSettings, "Canvas Settings");
            if (showCanvasSettings)
            {
                EditorGUI.indentLevel++;

                Vector2 newCenter = EditorGUILayout.Vector2Field("Canvas Center", canvasCenter);
                Vector2 newSize = EditorGUILayout.Vector2Field("Canvas Size", canvasSize);
                int newResolution = EditorGUILayout.IntSlider("Texture Resolution", textureResolution, 128, 2048);

                if (newCenter != canvasCenter || newSize != canvasSize || newResolution != textureResolution)
                {
                    canvasCenter = newCenter;
                    canvasSize = newSize;
                    if (newResolution != textureResolution)
                    {
                        textureResolution = newResolution;
                        InitializeMaskTexture();
                    }
                    UpdateCanvasRect();
                    SyncCanvasDataToComponent();
                    SceneView.RepaintAll();
                }

                EditorGUI.indentLevel--;

                if (GUILayout.Button("Update Canvas"))
                {
                    UpdateCanvasRect();
                    SyncCanvasDataToComponent();
                    SceneView.RepaintAll();
                }

                if (GUILayout.Button("Sync from Component"))
                {
                    SyncCanvasDataFromComponent();
                    SceneView.RepaintAll();
                }
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Paint Tools", EditorStyles.boldLabel);

            brushSize = EditorGUILayout.Slider("Brush Size", brushSize, 1f, 20f);
            brushStrength = EditorGUILayout.Slider("Brush Strength", brushStrength, 0.1f, 1f);
            maskColor = EditorGUILayout.ColorField("Mask Color", maskColor);

            EditorGUILayout.Space();
            GUILayout.BeginHorizontal();

            GUI.backgroundColor = isDrawingMode ? Color.green : Color.white;
            if (GUILayout.Button(isDrawingMode ? "Exit Drawing Mode" : "Enter Drawing Mode"))
            {
                isDrawingMode = !isDrawingMode;
                SceneView.RepaintAll();
            }
            GUI.backgroundColor = Color.white;

            if (GUILayout.Button("Clear Mask"))
            {
                ClearMask();
            }

            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Export Mask"))
            {
                ExportMask();
            }

            if (GUILayout.Button("Load Mask"))
            {
                LoadMask();
            }

            GUILayout.EndHorizontal();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Canvas Info", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Vector2Field("Canvas Min", canvasRect.min);
            EditorGUILayout.Vector2Field("Canvas Max", canvasRect.max);
            EditorGUILayout.Vector2Field("Canvas Size", canvasRect.size);
            EditorGUI.EndDisabledGroup();

            if (GUILayout.Button("Copy Canvas Rect to Clipboard"))
            {
                string rectInfo = $"Canvas Rect - Min: ({canvasRect.min.x:F2}, {canvasRect.min.y:F2}), Max: ({canvasRect.max.x:F2}, {canvasRect.max.y:F2}), Size: ({canvasRect.size.x:F2}, {canvasRect.size.y:F2})";
                EditorGUIUtility.systemCopyBuffer = rectInfo;
                Debug.Log("Canvas Rect copied to clipboard: " + rectInfo);
            }

            UploadGPU();
        }

        /// <summary>
        /// 获取鼠标位置对应的世界坐标
        /// </summary>
        private Vector3 GetWorldPositionFromMouse(Vector2 mousePosition)
        {
            Ray worldRay = HandleUtility.GUIPointToWorldRay(mousePosition);

            // 优先使用射线检测
            RaycastHit hit;
            if (Physics.Raycast(worldRay, out hit))
            {
                return hit.point;
            }

            // 如果没有碰撞体，投射到地面平面 (Y = 0)
            Plane groundPlane = new Plane(Vector3.up, Vector3.zero);
            if (groundPlane.Raycast(worldRay, out float distance))
            {
                return worldRay.GetPoint(distance);
            }

            // 返回零向量表示无效位置
            return Vector3.zero;
        }

        /// <summary>
        /// 绘制笔刷可视化
        /// </summary>
        private void DrawBrushVisualization(Vector2 mousePosition)
        {
            Vector3 worldPos = GetWorldPositionFromMouse(mousePosition);
            if (worldPos == Vector3.zero) return;

            // 检查是否在画布范围内
            bool isInCanvas = IsPointInCanvas(new Vector2(worldPos.x, worldPos.z));

            // 设置笔刷可视化位置（稍微抬高一点避免z-fighting）
            Vector3 brushPos = new Vector3(worldPos.x, worldPos.y + 0.1f, worldPos.z);

            // 计算笔刷在世界空间中的实际大小
            float worldBrushSize = brushSize * (canvasSize.x / textureResolution);

            if (isInCanvas)
            {
                // 在画布内：绘制完整的笔刷可视化

                // 外圈 - 笔刷边界
                Handles.color = new Color(maskColor.r, maskColor.g, maskColor.b, 0.8f);
                Handles.DrawWireDisc(brushPos, Vector3.up, worldBrushSize);

                // 内圈 - 笔刷强度指示
                float innerRadius = worldBrushSize * brushStrength;
                Handles.color = new Color(maskColor.r, maskColor.g, maskColor.b, 0.3f);
                Handles.DrawSolidDisc(brushPos, Vector3.up, innerRadius);

                // 中心点
                Handles.color = maskColor;
                Handles.DrawSolidDisc(brushPos, Vector3.up, worldBrushSize * 0.05f);

                // 绘制笔刷强度渐变圈
                int segments = 24;
                float strengthRadius = worldBrushSize * 0.8f;
                for (int i = 0; i < segments; i++)
                {
                    float angle1 = (float)i / segments * Mathf.PI * 2;
                    float angle2 = (float)(i + 1) / segments * Mathf.PI * 2;

                    Vector3 point1 = brushPos + new Vector3(Mathf.Cos(angle1), 0, Mathf.Sin(angle1)) * strengthRadius;
                    Vector3 point2 = brushPos + new Vector3(Mathf.Cos(angle2), 0, Mathf.Sin(angle2)) * strengthRadius;

                    Handles.color = new Color(maskColor.r, maskColor.g, maskColor.b, brushStrength * 0.5f);
                    Handles.DrawLine(point1, point2);
                }
            }
            else
            {
                // 在画布外：绘制灰色的无效状态笔刷
                Handles.color = new Color(0.5f, 0.5f, 0.5f, 0.5f);
                Handles.DrawWireDisc(brushPos, Vector3.up, worldBrushSize);

                // 绘制禁止符号
                Handles.color = Color.red;
                Handles.DrawLine(
                    brushPos + new Vector3(-worldBrushSize * 0.7f, 0, -worldBrushSize * 0.7f),
                    brushPos + new Vector3(worldBrushSize * 0.7f, 0, worldBrushSize * 0.7f)
                );
                Handles.DrawLine(
                    brushPos + new Vector3(-worldBrushSize * 0.7f, 0, worldBrushSize * 0.7f),
                    brushPos + new Vector3(worldBrushSize * 0.7f, 0, -worldBrushSize * 0.7f)
                );
            }

            // 绘制笔刷到画布边缘的距离指示线（如果接近边缘）
            Vector2 brushPos2D = new Vector2(worldPos.x, worldPos.z);
            float distanceToEdge = GetDistanceToCanvasEdge(brushPos2D);

            if (distanceToEdge < worldBrushSize * 2 && isInCanvas)
            {
                // 找到最近的边缘点
                Vector2 closestEdge = GetClosestPointOnCanvasEdge(brushPos2D);
                Vector3 edgePoint3D = new Vector3(closestEdge.x, worldPos.y + 0.1f, closestEdge.y);

                // 绘制到边缘的指示线
                Handles.color = new Color(1, 1, 0, 0.6f);
                Handles.DrawDottedLine(brushPos, edgePoint3D, 3f);

                // 在边缘点绘制小圆圈
                Handles.DrawWireDisc(edgePoint3D, Vector3.up, worldBrushSize * 0.1f);
            }
        }

        /// <summary>
        /// 计算点到画布边缘的最短距离
        /// </summary>
        private float GetDistanceToCanvasEdge(Vector2 point)
        {
            float distanceToLeft = point.x - canvasRect.xMin;
            float distanceToRight = canvasRect.xMax - point.x;
            float distanceToBottom = point.y - canvasRect.yMin;
            float distanceToTop = canvasRect.yMax - point.y;

            return Mathf.Min(distanceToLeft, distanceToRight, distanceToBottom, distanceToTop);
        }

        /// <summary>
        /// 获取画布边缘上最接近指定点的位置
        /// </summary>
        private Vector2 GetClosestPointOnCanvasEdge(Vector2 point)
        {
            float distanceToLeft = point.x - canvasRect.xMin;
            float distanceToRight = canvasRect.xMax - point.x;
            float distanceToBottom = point.y - canvasRect.yMin;
            float distanceToTop = canvasRect.yMax - point.y;

            float minDistance = Mathf.Min(distanceToLeft, distanceToRight, distanceToBottom, distanceToTop);

            if (minDistance == distanceToLeft)
                return new Vector2(canvasRect.xMin, point.y);
            else if (minDistance == distanceToRight)
                return new Vector2(canvasRect.xMax, point.y);
            else if (minDistance == distanceToBottom)
                return new Vector2(point.x, canvasRect.yMin);
            else
                return new Vector2(point.x, canvasRect.yMax);
        }

        private void OnSceneGUI(SceneView sceneView)
        {
            if (!isDrawingMode) return;

            Event e = Event.current;

            DrawCanvasBorder();

            DrawBrushVisualization(e.mousePosition);

            if (e.type == EventType.MouseDown && e.button == 0)
            {
                Vector3 mousePos = e.mousePosition;
                Ray worldRay = HandleUtility.GUIPointToWorldRay(mousePos);
                RaycastHit hit;
                Vector3 worldPos = new Vector3(1000, 1000, 1000);
                if (Physics.Raycast(worldRay, out hit))
                {
                    worldPos = hit.point;
                }
                if (IsPointInCanvas(new Vector2(worldPos.x, worldPos.z)))
                {
                    PaintOnMask(new Vector2(worldPos.x, worldPos.z));
                    e.Use();
                }
            }
            else if (e.type == EventType.MouseDrag && e.button == 0)
            {
                Vector3 mousePos = e.mousePosition;
                Ray worldRay = HandleUtility.GUIPointToWorldRay(mousePos);
                RaycastHit hit;
                Vector3 worldPos = new Vector3(1000, 1000, 1000);
                if (Physics.Raycast(worldRay, out hit))
                {
                    worldPos = hit.point;
                }
                if (IsPointInCanvas(new Vector2(worldPos.x, worldPos.z)))
                {
                    PaintOnMask(new Vector2(worldPos.x, worldPos.z));
                    e.Use();
                }
            }

            // 鼠标移动时重绘Scene视图以更新笔刷可视化
            if (e.type == EventType.MouseMove)
            {
                SceneView.RepaintAll();
            }

            Handles.BeginGUI();
            GUI.color = Color.yellow;
            GUI.Label(new Rect(10, 10, 300, 20), "Drawing Mode Active - Left click to paint fog mask");

            // 显示笔刷信息
            Vector3 currentWorldPos = GetWorldPositionFromMouse(e.mousePosition);
            if (currentWorldPos != Vector3.zero && IsPointInCanvas(new Vector2(currentWorldPos.x, currentWorldPos.z)))
            {
                GUI.color = Color.green;
                GUI.Label(new Rect(10, 30, 300, 20), $"Brush: Size={brushSize:F1}, Strength={brushStrength:F1}");
                GUI.Label(new Rect(10, 50, 300, 20), $"Position: ({currentWorldPos.x:F1}, {currentWorldPos.z:F1})");
            }
            else
            {
                GUI.color = Color.red;
                GUI.Label(new Rect(10, 30, 300, 20), "Outside canvas area");
            }

            GUI.color = Color.white;
            Handles.EndGUI();

            if (e.type == EventType.Layout)
            {
                HandleUtility.AddDefaultControl(GUIUtility.GetControlID(FocusType.Passive));
            }
        }

        private void InitializeMaskTexture()
        {
            if (fogMaskTexture == null)
            {
                fogMaskTexture = new Texture2D(textureResolution, textureResolution, TextureFormat.RGBA32, false);
                ClearMask();
            }
            UpdateCanvasRect();
        }

        private void UpdateCanvasRect()
        {
            canvasRect = new Rect(
                canvasCenter.x - canvasSize.x * 0.5f,
                canvasCenter.y - canvasSize.y * 0.5f,
                canvasSize.x,
                canvasSize.y
            );
        }

        private void SyncCanvasDataToComponent()
        {
            if (fogComponent != null)
            {
                Undo.RecordObject(fogComponent, "Update Canvas Data");
                fogComponent.m_fogData._FogMaskCenter = canvasCenter;
                fogComponent.m_fogData._FogMaskSize = canvasSize;
                EditorUtility.SetDirty(fogComponent);
            }
        }

        private void SyncCanvasDataFromComponent()
        {
            if (fogComponent != null)
            {
                canvasCenter = fogComponent.m_fogData._FogMaskCenter;
                canvasSize = fogComponent.m_fogData._FogMaskSize;
                UpdateCanvasRect();
            }
        }

        private void DrawCanvasBorder()
        {
            Vector3[] corners = new Vector3[5];
            corners[0] = new Vector3(canvasRect.xMin, 0, canvasRect.yMin);
            corners[1] = new Vector3(canvasRect.xMax, 0, canvasRect.yMin);
            corners[2] = new Vector3(canvasRect.xMax, 0, canvasRect.yMax);
            corners[3] = new Vector3(canvasRect.xMin, 0, canvasRect.yMax);
            corners[4] = corners[0];

            Handles.color = Color.yellow;
            Handles.DrawPolyLine(corners);

            Handles.color = Color.red;
            Handles.DrawWireCube(new Vector3(canvasCenter.x, 0, canvasCenter.y), Vector3.one * 2f);

            Handles.color = new Color(1, 1, 0, 0.3f);
            int gridLines = 10;
            for (int i = 1; i < gridLines; i++)
            {
                float t = (float)i / gridLines;
                Vector3 verticalStart = Vector3.Lerp(corners[0], corners[3], t);
                Vector3 verticalEnd = Vector3.Lerp(corners[1], corners[2], t);
                Vector3 horizontalStart = Vector3.Lerp(corners[0], corners[1], t);
                Vector3 horizontalEnd = Vector3.Lerp(corners[3], corners[2], t);

                Handles.DrawLine(verticalStart, verticalEnd);
                Handles.DrawLine(horizontalStart, horizontalEnd);
            }
        }

        private bool IsPointInCanvas(Vector2 point)
        {
            return canvasRect.Contains(point);
        }

        private void PaintOnMask(Vector2 worldPos)
        {
            Vector2 normalizedPos = new Vector2(
                (worldPos.x - canvasRect.xMin) / canvasRect.width,
                (worldPos.y - canvasRect.yMin) / canvasRect.height
            );

            int texX = Mathf.RoundToInt(normalizedPos.x * textureResolution);
            int texY = Mathf.RoundToInt(normalizedPos.y * textureResolution);

            int brushRadius = Mathf.RoundToInt(brushSize);
            for (int x = -brushRadius; x <= brushRadius; x++)
            {
                for (int y = -brushRadius; y <= brushRadius; y++)
                {
                    int pixelX = texX + x;
                    int pixelY = texY + y;

                    if (pixelX >= 0 && pixelX < textureResolution && pixelY >= 0 && pixelY < textureResolution)
                    {
                        float distance = Mathf.Sqrt(x * x + y * y);
                        if (distance <= brushRadius)
                        {
                            float falloff = 1f - (distance / brushRadius);
                            Color currentColor = fogMaskTexture.GetPixel(pixelX, pixelY);
                            Color newColor = Color.Lerp(currentColor, maskColor, falloff * brushStrength);
                            fogMaskTexture.SetPixel(pixelX, pixelY, newColor);
                        }
                    }
                }
            }

            fogMaskTexture.Apply();
            SceneView.RepaintAll();
        }

        private void ClearMask()
        {
            if (fogMaskTexture != null)
            {
                Color[] colors = new Color[textureResolution * textureResolution];
                for (int i = 0; i < colors.Length; i++)
                {
                    colors[i] = Color.black;
                }
                fogMaskTexture.SetPixels(colors);
                fogMaskTexture.Apply();
            }
        }

        /// <summary>
        /// 配置纹理导入设置（设置为ASTC6X6压缩）
        /// </summary>
        private void ConfigureTextureImportSettings(string fullPath)
        {
            try
            {
                // 转换为Assets相对路径
                string relativePath = "Assets" + fullPath.Substring(Application.dataPath.Length);

                // 获取纹理导入器
                TextureImporter importer = AssetImporter.GetAtPath(relativePath) as TextureImporter;
                if (importer != null)
                {
                    // 基本设置
                    importer.textureType = TextureImporterType.Default;
                    importer.isReadable = false;
                    importer.mipmapEnabled = true;
                    importer.generateCubemap = TextureImporterGenerateCubemap.AutoCubemap;
                    importer.wrapMode = TextureWrapMode.Clamp;
                    importer.filterMode = FilterMode.Bilinear;
                    importer.anisoLevel = 1;

                    // 设置不同平台的压缩格式
                    ConfigurePlatformSettings(importer, "Standalone", TextureImporterFormat.ASTC_6x6);
                    ConfigurePlatformSettings(importer, "Android", TextureImporterFormat.ASTC_6x6);
                    ConfigurePlatformSettings(importer, "iPhone", TextureImporterFormat.ASTC_6x6);
                    ConfigurePlatformSettings(importer, "WebGL", TextureImporterFormat.DXT1);

                    // 应用设置
                    EditorUtility.SetDirty(importer);
                    importer.SaveAndReimport();

                    Debug.Log($"Texture import settings configured for: {relativePath} (ASTC6X6 compression)");
                }
                else
                {
                    Debug.LogWarning($"Could not get TextureImporter for: {relativePath}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to configure texture import settings: {e.Message}");
            }
        }

        /// <summary>
        /// 配置特定平台的纹理设置
        /// </summary>
        private void ConfigurePlatformSettings(TextureImporter importer, string platform, TextureImporterFormat format)
        {
            var platformSettings = importer.GetPlatformTextureSettings(platform);

            platformSettings.overridden = true;
            platformSettings.format = format;
            platformSettings.maxTextureSize = GetOptimalTextureSize(textureResolution);
            platformSettings.compressionQuality = (int)TextureCompressionQuality.Normal;
            platformSettings.crunchedCompression = false;
            platformSettings.allowsAlphaSplitting = false;

            importer.SetPlatformTextureSettings(platformSettings);
        }

        /// <summary>
        /// 获取最优的纹理尺寸（2的幂次方）
        /// </summary>
        private int GetOptimalTextureSize(int originalSize)
        {
            // 确保纹理尺寸是2的幂次方
            int size = 64;
            while (size < originalSize && size < 2048)
            {
                size *= 2;
            }
            return size;
        }

        /// <summary>
        /// 导出遮罩纹理到指定路径
        /// </summary>
        private bool ExportMaskToPath(string fullPath)
        {
            try
            {
                if (fogMaskTexture == null) return false;

                // 确保目录存在
                string directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 导出纹理
                byte[] bytes = fogMaskTexture.EncodeToPNG();
                File.WriteAllBytes(fullPath, bytes);

                // 如果文件保存在Assets文件夹内，设置纹理导入参数
                if (fullPath.StartsWith(Application.dataPath))
                {
                    // 刷新资源数据库
                    AssetDatabase.Refresh();

                    // 设置纹理导入参数（ASTC6X6压缩）
                    ConfigureTextureImportSettings(fullPath);
                }
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to export fog mask: {e.Message}");
                return false;
            }
        }


        private void ExportMask()
        {
            if (fogMaskTexture == null)
            {
                EditorUtility.DisplayDialog("Error", "No mask texture to export!", "OK");
                return;
            }

            // 生成默认文件名
            string defaultFileName = $"FogMask_{System.DateTime.Now:yyyyMMdd_HHmmss}";

            // 获取默认保存路径（使用当前项目的Assets文件夹）
            string defaultPath = Application.dataPath;

            // 打开保存文件对话框，让用户选择路径和文件名
            string savePath = EditorUtility.SaveFilePanel(
                "Export Fog Mask",
                defaultPath,
                defaultFileName,
                "png"
            );

            if (!string.IsNullOrEmpty(savePath))
            {
                bool success = ExportMaskToPath(savePath);
                if (success)
                {
                    // 如果保存在Assets文件夹内，刷新资源数据库
                    if (savePath.StartsWith(Application.dataPath))
                    {
                        AssetDatabase.Refresh();

                        // 转换为相对路径并高亮显示
                        string relativePath = "Assets" + savePath.Substring(Application.dataPath.Length);
                        Object asset = AssetDatabase.LoadAssetAtPath<Texture2D>(relativePath);
                        if (asset != null)
                        {
                            EditorGUIUtility.PingObject(asset);
                        }
                    }

                    EditorUtility.DisplayDialog("Success", $"Fog mask exported successfully!\nPath: {savePath}", "OK");
                    Debug.Log($"Fog mask exported to: {savePath}");
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Failed to export fog mask!", "OK");
                }
            }
        }


        private void LoadMask()
        {
            string path = EditorUtility.OpenFilePanel("Load Fog Mask", MASK_TEXTURE_PATH, "png");
            if (!string.IsNullOrEmpty(path))
            {
                byte[] fileData = File.ReadAllBytes(path);
                Texture2D loadedTexture = new Texture2D(2, 2);
                loadedTexture.LoadImage(fileData);

                if (loadedTexture.width != textureResolution || loadedTexture.height != textureResolution)
                {
                    TextureScale.Bilinear(loadedTexture, textureResolution, textureResolution);
                }

                fogMaskTexture = loadedTexture;
                Debug.Log($"Fog mask loaded from: {path}");
            }
        }
    }

    public class TextureScale
    {
        public static void Bilinear(Texture2D tex, int newWidth, int newHeight)
        {
            Color[] newColors = new Color[newWidth * newHeight];
            float ratioX = 1.0f / ((float)newWidth / (tex.width - 1));
            float ratioY = 1.0f / ((float)newHeight / (tex.height - 1));

            for (int y = 0; y < newHeight; y++)
            {
                int yFloor = (int)Mathf.Floor(y * ratioY);
                var y1 = yFloor * tex.width;
                var y2 = (yFloor + 1) * tex.width;
                var yw = y * newWidth;

                for (int x = 0; x < newWidth; x++)
                {
                    int xFloor = (int)Mathf.Floor(x * ratioX);
                    var xLerp = x * ratioX - xFloor;
                    newColors[yw + x] = ColorLerpUnclamped(
                        ColorLerpUnclamped(tex.GetPixel(xFloor, yFloor), tex.GetPixel(xFloor + 1, yFloor), xLerp),
                        ColorLerpUnclamped(tex.GetPixel(xFloor, yFloor + 1), tex.GetPixel(xFloor + 1, yFloor + 1), xLerp),
                        y * ratioY - yFloor);
                }
            }

            //tex.Reinitialize(newWidth, newHeight);
            tex.SetPixels(newColors);
            tex.Apply();
        }

        private static Color ColorLerpUnclamped(Color c1, Color c2, float value)
        {
            return new Color(c1.r + (c2.r - c1.r) * value,
                           c1.g + (c2.g - c1.g) * value,
                           c1.b + (c2.b - c1.b) * value,
                           c1.a + (c2.a - c1.a) * value);
        }
    }
}