//#define DEBUG_AB_OFFSET
//#define UNITY_IOS

using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using UnityEditor.Sprites;
using War.Base;
using System.Linq;
using FtpTest1;
using ICSharpCode.SharpZipLib.Zip;
using CLeopardZip;
using System.Text;
using System.Text.RegularExpressions;
using LunarConsolePluginInternal;
using War.Common;
using XLua.LuaDLL;
using Sirenix.OdinInspector.Demos;
using System.Runtime.Serialization.Formatters.Binary;
using System.Net;
using BuildingHelpers;
using System.Data;
using UnityEditor.Android;
using UnityEngine.Networking;
#if UNITY_2022_3_OR_NEWER
using UnityEditor.UniversalRP.AssetPostProcessing;
#endif
namespace War.Base
{
    public class BuildScript
    {
        public class KeyValue
        {
            public KeyValue(string key, string value)
            {
                Key = key;
                Value = value;
            }

            public string Key;
            public string Value;
        }

        public const string AssetBundlesOutputPath = "AssetBundles";
        static readonly string editor_build_config = "../../Tools/GameConfig/custom/custom_editor_build_config.json";
        static readonly string build_config = "../../Tools/GameConfig/custom/custom_jenkins.json";

        public static bool bExistLocalServerList = false;
        public static string errorMessage;
        private static bool copyAssetBundle = false;

        public static List<string> excludeABFiles = new List<string>()
        {
            "update.json",
            "files.txt",
            "files2.txt",
            "files.txt.bytes",
            "files2.bytes",
            ".DS_Store",
            ".manifest",
            ".dll",
            ".mdb",
            "package.json"
        };

        public enum SEL_TABLE
        {
            cn,
            en,
            en_oumei,
            en_yatai,
        }


        public static void DeleteBakeAssetAB(string path)
        {
            if (!Directory.Exists(path))
            {
                Debug.LogError($"目录不存在: {path}");
                return;
            }

            int deletedCount = 0;
            foreach (string dir in Directory.GetDirectories(path, "bakedassets_*", SearchOption.AllDirectories))
            {
                try
                {
                    Directory.Delete(dir, true);
                    Debug.Log($"已删除: {dir}");
                    deletedCount++;
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"删除失败: {dir}\n{e.Message}");
                }
            }
            
            Debug.Log($"操作完成！共删除 {deletedCount} 个目录");
        }


        public static bool BuildAssetBundles()
        {
            return BuildAssetBundles(EditorUserBuildSettings.activeBuildTarget);
        }

        static bool b_strip_log = false;

        protected static bool BuildAssetBundles(BuildTarget buildTarget)
        {
            // RemoverForTinyGame.RemoveUnuseTinyGameFormConfig();
            LogHelp.Instance.Log("BuildOfflineConfig-start");
            //Exporter.BuildOfflineConfig();
            LogHelp.Instance.Log("BuildOfflineConfig-end");
            AssetDatabase.RemoveUnusedAssetBundleNames();
            // Choose the output path according to the build target.
            string outputPath = Path.Combine(AssetBundlesOutputPath, GetPlatformFolderForAssetBundles(buildTarget));
            if (!Directory.Exists(outputPath))
                Directory.CreateDirectory(outputPath);
            else
                DeleteBakeAssetAB(outputPath);
            LogHelp.Instance.Log("RemoveUnusedAssetBundleNames-end");

#if UNITY_2022_3_OR_NEWER
            var settings = TextureImportSettingsProvider.GetOrCreateSettings();
            if (settings != null && settings.enableAutoProcessing)
            {
                settings.ApplyForceMipMapSetting();
            }
#endif

            FilterTable();
            FilterResDifference();

            Dictionary<string, byte[]> luaOriginContents = null;
            Dictionary<string, byte[]> luaCullContents = null;

            /// 如果开启了luasrcdiet的lua代码精简，b_strip_log日志剔除逻辑会在 <see cref = BuildSimplifyLua.ExecuteLuaSrcDiet />
            var bCompressLua = JenkinsEnv.Instance.GetBool("enablue_compress_lua", false);
            if (!bCompressLua && b_strip_log)
            {
                luaOriginContents = Base.AssetbundlesMenuItems.CommentLuaPrint(out luaCullContents);
            }

            AssetDatabase.Refresh();
            LogHelp.Instance.Log("CommentLuaPrint-end");

            //增量ab不需要打ab,直接从服务器的ab进行加减法计算,参数版本"1,2,3"最终的资源= 3-2+1
            var choose_increase_ab_param = JenkinsEnv.Instance.Get("choose_increase_ab_param");
            bool build_ab_from_res = !string.IsNullOrEmpty(choose_increase_ab_param);

            "".Print("build_ab_from_res", build_ab_from_res);
            if (!build_ab_from_res)
            {
                // 屏蔽lua相关的生成 luascript.zip
                // var is_no_run_filePatch = JenkinsEnv.Instance.GetBool("is_no_run_filePatch", false);
                // //onlylua依赖BuildLuascriptVice
                // if (is_no_run_filePatch == false || buildOtherAb)
                // {
                //     LogHelp.Instance.Log($"start call BuildLuascriptVice(luaCullContents)");
                //     BuildLuaScriptPatch.BuildLuascriptVice(luaCullContents);
                // }
                // else
                // {
                //     LogHelp.Instance.Log(
                //         $"Get jenkins key: is_no_run_filePatch = {is_no_run_filePatch}, not do BuildLuascriptVice(luaCullContents)");
                // }
                //BuildLuascriptVice(luaCullContents);
                bool buildOtherAb = JenkinsEnv.Instance.GetBool("gen_other_ab", true);
                var gen_lua = JenkinsEnv.Instance.GetBool("gen_lua", true);
                var copy_last_lua = JenkinsEnv.Instance.GetBool("copy_last_lua", false);
                //如果开启了copy_last_lua,因为会从服务器复制最后一个版本的lua,所以跳过生成lua 
                if (gen_lua && !copy_last_lua)
                {
                    BuildLuaScriptPatch.BuildLuascriptVice(luaCullContents, b_strip_log);
                }
                else
                {
                    LogHelp.Instance.Log("not gen_lua" + copy_last_lua);
                }

                BuildLuaScriptPatch.BuildLuaPatch();

                //if (JenkinsEnv.Instance.GetBool("full_package", true))
                //{
                //}
                //ResModuleTool.Instance.ExeAction();

                LogHelp.Instance.Log("ResModuleTool-end");


                var cacheMode = EditorSettings.spritePackerMode;
                try
                {
                    //CheckUV.CheckAndFix();
                    if (buildOtherAb)
                    {
                        //CommentTMP_PostProcessor();

                        FileCheck.Instance.Exec(outputPath);

                        if (JenkinsEnv.Instance.GetBool("use_sprite_packer", false))
                        {
#if !UNITY_2022_3_OR_NEWER
                            //先恢复使用原 SpritePacker 加载方式，待 Sprite Atlas 流程完成后再修改
                            EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn;
                            Packer.SelectedPolicy = typeof(CustomPackerPolicy).Name;
                            Packer.RebuildAtlasCacheIfNeeded(buildTarget, true, Packer.Execution.ForceRegroup);
#endif
                        }
                        else
                        {
                            EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOnAtlas;
                            CustomCommandTool.Instance.mAdjust2OneAtlasTC.Filter();
                        }

                        LogHelp.Instance.Log("RebuildAtlasCacheIfNeeded-end");

                        var relative_res_ver = JenkinsEnv.Instance.GetInt("relative_res_ver", 0);
                        if (relative_res_ver > 0)
                        {
                            ModifyAB.Instance.GenSvnOldResFolder(relative_res_ver);
                        }


                        CasualGameBuild.Instance.BuildLuaAB();

                        if (buildTarget == BuildTarget.Android)
                        {
                            EditorUserBuildSettings.androidBuildSubtarget = MobileTextureSubtarget.ASTC;
                            // EditorUserBuildSettings.androidETC2Fallback = AndroidETC2Fallback.Quality32BitDownscaled;
                        }

                        LogHelp.Instance.Log("androidBuildSubtarget-end");


                        AssetBundleManifest abManifest = BuildPipeline.BuildAssetBundles(outputPath,
                            BuildAssetBundleOptions.ChunkBasedCompression
                            | BuildAssetBundleOptions.DeterministicAssetBundle
                            | BuildAssetBundleOptions.IgnoreTypeTreeChanges, buildTarget);
                        LogHelp.Instance.Log("BuildAssetBundles-DeterministicAssetBundle-end");
                        if (abManifest == null)
                        {
                            Debug.LogError("Build Asset bundle failed!");
                            return false;
                        }
                        else
                        {
                            Debug.LogWarning("Build Asset bundle Success!");

                            //var build_aab_pad = JenkinsEnv.Instance.GetBool("build_aab_asset_pack", false);
                            //var bundles = abManifest.GetAllAssetBundles();
                            //Debug.LogWarning($"build_aab_asset_pack:{build_aab_pad}, ab count:{bundles.Length}");
                            //if (build_aab_pad)
                            //{
                            //    //生成 Play Asset Delivery
                            //    var assetPackConfig = new Google.Android.AppBundle.Editor.AssetPackConfig();
                            //    foreach(var bunlde in bundles)
                            //    {
                            //        //AssetDatabase.GetAssetPathsFromAssetBundle(bunlde);
                            //        var bundlePath = outputPath + "/" + bunlde;
                            //        var bundleFullPath = Path.GetFullPath(bundlePath);
                            //        Debug.LogWarning($"aab asset pack bundle:{bunlde}, path:{bundleFullPath}");
                            //        assetPackConfig.AddAssetBundle(bundleFullPath, Google.Android.AppBundle.Editor.AssetPackDeliveryMode.InstallTime);
                            //    }
                            //    var buildAssetPackResult = Google.Android.AppBundle.Editor.Bundletool.BuildBundle(new BuildPlayerOptions(), assetPackConfig);
                            //    Debug.LogWarning($"Bundletool.BuildBundle resulte:{buildAssetPackResult}");
                            //    //assetPackConfig.AddAssetBundle()
                            //}
                        }
                    }
                    else
                    {
                        LogHelp.Instance.Log("Only_lua ,No gen_other_ab");
                    }
                    //#endif

                    LogHelp.Instance.Log("BuildAssetBundles-end");


                    FileCheck.CheckAndHandleAbVaild(outputPath, JenkinsEnv.Instance.GetInt("check_ab_vaild", 2));
                    LogHelp.Instance.Log("CheckAndHandleAbVaild-end");

                    EncryptAssetBundles(outputPath);

                    LogHelp.Instance.Log("EncryptAssetBundles-end");

                    BuildLuaScriptPatch.SetGenLua(outputPath);
                }
                finally
                {
                    EditorSettings.spritePackerMode = cacheMode;

                    if (b_strip_log)
                        if (luaOriginContents != null)
                        {
                            foreach (var luaFileInfo in luaOriginContents)
                            {
                                File.WriteAllBytes(luaFileInfo.Key, luaFileInfo.Value);
                            }

                            luaOriginContents.Clear();
                        }

                    LogHelp.Instance.Log("ResetLuaScript-end");
                }
            }
            else
            {
                ModifyAB.Instance.Build(outputPath);
            }

            BuildConfigList();

            CreatePatchFile(outputPath);

            LogHelp.Instance.Log("CreatePatchFile-end");


            ///清理没有匹配abname的资源ab(旧ab文件)
            //			DirectoryInfo info2 = new DirectoryInfo(outputPath);
            			// ClearAssetBundleFile(info2, assetBundleNamesDict(), GetPlatformFolderForAssetBundles(buildTarget));
            SetSerializationModeMixed();
            return true;
        }

        //再打包ab之后，从ab目录拷贝到StreamingAssets之前调用，主要是为了解决从外部拷贝到unity内部序列化可能不正常的问题
        public static void SetSerializationModeMixed()
        {
            LogHelp.Instance.Log("currentserializationMode:" + EditorSettings.serializationMode.ToString());
            EditorSettings.serializationMode = SerializationMode.Mixed;
            LogHelp.Instance.Log("currentserializationMode:" + EditorSettings.serializationMode.ToString());
        }

        /// <summary>
        /// 处理abjson等
        /// </summary>
        public static void BuildConfigList()
        {
            LogHelp.Instance.Log("CasualGameBuild+");
            //打包多个小游戏的配置自动设置进key2Mark
            // CasualGameBuild.Instance.BuildCombineMark();
            //小游戏合集
            // CasualGameBuild.Instance.AddCollectionGameRes2Key2Mark();
            //计算当前打包分版本资源各版本资源列表
            CasualGameBuild.Instance.AddCommonResKey();
            CasualGameBuild.Instance.BuildABList();

            LogHelp.Instance.Log("CasualGameBuild-");

            LogHelp.Instance.Log("CasualChannelBuild+");
            //计算当前渠道分版本资源各版本资源列表
            CasualChannelBuild.Instance.BuildABList();
            LogHelp.Instance.Log("CasualChannelBuild-");
        }

        // 国内表格默认cn，国外表格使用en_oumei
        public static String GetTableMarkStr()
        {
            var table_mark_str = SEL_TABLE.en_oumei.ToString();
            var strBuildConfig = File.ReadAllText(build_config);
            var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strBuildConfig);
            var isDomestic = GetBuildConfig(editorBuildConfig, "Q1SDK_DOMESTIC");
            if (!string.IsNullOrEmpty(isDomestic))
            {
                if (isDomestic == "True")
                {
                    table_mark_str = SEL_TABLE.cn.ToString();
                }
                else
                {
                    table_mark_str = SEL_TABLE.en_oumei.ToString();
                }
            }

            "".Print("table_mark_str", table_mark_str, " isDomestic:", isDomestic);
            return table_mark_str;
        }

        [MenuItem("AssetBundles/FilterTable")]
        public static void FilterTable()
        {
            var resPath = "Assets/Lua/Tables";
            var table_mark = JenkinsEnv.Instance.Get("table_mark", GetTableMarkStr());
            var rpath = "";
            foreach (SEL_TABLE m in Enum.GetValues(typeof(SEL_TABLE)))
            {
                var m_temp = m.ToString();
                if (m_temp != table_mark)
                {
                    rpath = resPath + "/" + m_temp;
                    if (Directory.Exists(rpath))
                    {
                        Directory.Delete(rpath, true);
                    }
                }
            }

            if (File.Exists(resPath + "/OptimizerTable.txt"))
            {
                File.Delete(resPath + "/OptimizerTable.txt");
            }

            if (Directory.Exists(resPath + "/Database"))
            {
                Directory.Delete(resPath + "/Database", true);
            }

            if (File.Exists(resPath + "/Database.meta"))
            {
                File.Delete(resPath + "/Database.meta");
            }

            AssetDatabase.Refresh();
        }

        [MenuItem("AssetBundles/FilterResDifference")]
        public static void FilterResDifference()
        {
            var resPath = "Assets/ArtDif";
            var table_mark = JenkinsEnv.Instance.Get("table_mark", GetTableMarkStr());
            var rpath = "";
            foreach (SEL_TABLE m in Enum.GetValues(typeof(SEL_TABLE)))
            {
                var m_temp = m.ToString();
                if (m_temp != table_mark)
                {
                    rpath = resPath + "/" + m_temp;
                    if (Directory.Exists(rpath))
                    {
                        Directory.Delete(rpath, true);
                    }
                }
            }

            AssetDatabase.Refresh();
        }

        public static void CreatePatch()
        {
            // Choose the output path according to the build target.
            string outputPath = Path.Combine(AssetBundlesOutputPath,
                GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
            if (!Directory.Exists(outputPath))
                Directory.CreateDirectory(outputPath);

            AssetBundleManager.SetEncryptSets();

            CreatePatchFile(outputPath);
            var files = ResTag.PackTag("mini");
            CreateMiniPatchFile(files, outputPath);
        }

        public static Dictionary<string, string[]> newestPatchFileDic;

        public static string GetNewestFileVersion(string name)
        {
            if (newestPatchFileDic != null)
            {
                newestPatchFileDic.TryGetValue(name, out var content);
                if (content != null && content.Length > 0)
                {
                    return content[0];
                }
            }

            return "current";
        }

        public static void SetFileVersionForFileTxt(string filesPath)
        {
            if (File.Exists(filesPath))
            {
                var filesContent = File.ReadAllText(filesPath);
                var filesDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(filesContent);
                if (filesDic.ContainsKey("list"))
                {
                    var listDic = filesDic["list"];
                    foreach (var dic in listDic)
                    {
                        var f = dic.Key;
                        var version = GetNewestFileVersion(f);
                        if (version != "current" && dic.Value.Length > 0)
                        {
                            dic.Value[0] = version;
                        }
                    }

                    File.WriteAllText(filesPath, UIHelper.ToJson(filesDic));
                }
            }
        }

        public static void CopyFilesTxt2Local()
        {
            string abOutputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
                ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
            var hc = ModifyAB.Instance.GetHashCheckFromFolder(abOutputPath, true);
            if (hc != null)
            {
                var list = new List<string>(hc.list.Keys);

                foreach (var key in list)
                {
                    var path = $"{Application.streamingAssetsPath}/{abOutputPath}/{key}";
                    if (!File.Exists(path))
                    {
                        hc.list.Remove(key);
                    }
                }

                string hashStreamPath = $"{Application.streamingAssetsPath}/{abOutputPath}/files.txt";
                string hcStr = hc.ToJson();
                File.WriteAllText(hashStreamPath, hcStr);
                Debug.Log("HashStreaming:" + hcStr);
            }
        }

        /// <summary>
        /// 拷贝服务器上的最新files2.txt 至本地
        /// </summary>
        /// <param name="patchmark"></param>
        /// <param name="filesPath"></param>
        public static void CopyFiles2Txt2Local(string patchmark, string localFilesPath)
        {
            if (string.IsNullOrEmpty(localFilesPath) || string.IsNullOrEmpty(patchmark))
            {
                return;
            }
            else
            {
                localFilesPath = localFilesPath.Replace("\\", "/");
            }

            string serverFiles2Path =
                GetNewestFilePath(patchmark).Replace("files.txt", "files2.txt").Replace("\\", "/");
            "".Print("CopyFiles2Txt2Local patchmark:", patchmark, "  localFiles2Path:", localFilesPath,
                "  serverFiles2Path:", serverFiles2Path);

            if (File.Exists(serverFiles2Path) == false)
            {
                "".PrintError("serverFiles2Path is null", serverFiles2Path);
                return;
            }

            if (File.Exists(localFilesPath))
            {
                File.Delete(localFilesPath);
            }

            //copy server file2.txt to StreamingAssets
            File.Copy(serverFiles2Path, localFilesPath, true);
        }


        [NUnit.Framework.Test]
        public static void TestGetNewestPathFile()
        {
            var patchmark = "rogue_pc_zhero_branch";
            GetNewestPatchFile(patchmark);
        }

        // 从服务器资源路径/resource 下读取最近一次版本的 files.txt文件路径
        public static string GetNewestFilePath(string patchmark)
        {
            if (string.IsNullOrEmpty(patchmark)) return "";

            string patchDir = "../../Tools/Patch";
            string configNewDir = patchDir + "/update_all/config_new.json";

            string configNewContent = File.ReadAllText(configNewDir);
            var configNewDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string>>>(configNewContent);
            if (configNewDic != null && configNewDic.ContainsKey(patchmark))
            {
                var configJson = configNewDic[patchmark];
                var publicPatch = configJson["PublicPath"];

                var platform = GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
                var resourceDir = publicPatch + "/" + platform + "/resource";

#if UNITY_EDITOR_WIN
                //基于 wsl 的 linux 路径，如果使用 cygwin 同样需要类似处理
                string linuxPrefix = "/mnt/";
                Regex driverRegex = new Regex(linuxPrefix + @"([A-Za-z]{1,1})/");
                var matches = driverRegex.Matches(resourceDir);
                if (matches.Count > 0)
                {
                    var match = matches[0];
                    var driverName = match.Groups[1].Value;
                    resourceDir = resourceDir.Replace(linuxPrefix + driverName + "/", driverName + ":/");
                }
#endif

                if (Directory.Exists(resourceDir) == false)
                {
                    return "";
                }

                var directories = Directory.GetDirectories(resourceDir, "*", SearchOption.TopDirectoryOnly);
                int newestVersion = 0;
                foreach (var d in directories)
                {
                    //var symbolIndex = d.LastIndexOf("/");
                    //var versionStr = d.Substring(symbolIndex + 1);
                    var versionStr = Path.GetFileName(d);
                    int version = 0;
                    // "".Print("GetNewestFilePath subdir:", resourceDir, d, versionStr);
                    if (int.TryParse(versionStr, out version))
                    {
                        if (version > newestVersion)
                        {
                            newestVersion = version;
                        }
                    }
                }

                var newestPatchFile = resourceDir + "/" + newestVersion.ToString() + "/files.txt";
                if (!File.Exists(newestPatchFile))
                {
                    "".PrintError("error files doesnt exist", newestPatchFile);
                    return newestPatchFile;
                }

                return newestPatchFile;
            }

            return "";
        }

        // 从服务器资源路径/resource 下读取最近一次版本的 files.txt文件，生成文件列表
        public static void GetNewestPatchFile(string patchmark)
        {
            if (newestPatchFileDic != null)
                return;

            string patchDir = "../../Tools/Patch";
            string configNewDir = patchDir + "/update_all/config_new.json";

            string configNewContent = File.ReadAllText(configNewDir);
            var configNewDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string>>>(configNewContent);
            if (configNewDic != null && configNewDic.ContainsKey(patchmark))
            {
                var configJson = configNewDic[patchmark];
                var publicPatch = configJson["PublicPath"];

                var platform = GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
                var resourceDir = publicPatch + "/" + platform + "/resource";

#if UNITY_EDITOR_WIN
                //基于 wsl 的 linux 路径，如果使用 cygwin 同样需要类似处理
                string linuxPrefix = "/mnt/";
                Regex driverRegex = new Regex(linuxPrefix + @"([A-Za-z]{1,1})/");
                var matches = driverRegex.Matches(resourceDir);
                if (matches.Count > 0)
                {
                    var match = matches[0];
                    var driverName = match.Groups[1].Value;
                    resourceDir = resourceDir.Replace(linuxPrefix + driverName + "/", driverName + ":/");
                }
#endif

                var directories = Directory.GetDirectories(resourceDir, "*", SearchOption.TopDirectoryOnly);
                int newestVersion = 0;
                foreach (var d in directories)
                {
                    var symbolIndex = d.LastIndexOf("/");
                    var versionStr = d.Substring(symbolIndex + 1);
                    int version = 0;
                    if (int.TryParse(versionStr, out version))
                    {
                        if (version > newestVersion)
                        {
                            newestVersion = version;
                        }
                    }
                }

                var newestPatchFile = resourceDir + "/" + newestVersion.ToString() + "/files.txt";
                if (!File.Exists(newestPatchFile))
                {
                    return;
                }

                var newestPatchFileContent = File.ReadAllText(newestPatchFile);
                if (newestPatchFileContent != null)
                {
                    var tempNewestPatchFileDic =
                        UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(newestPatchFileContent);
                    if (tempNewestPatchFileDic.ContainsKey("list"))
                    {
                        newestPatchFileDic = tempNewestPatchFileDic["list"];
                    }
                }
            }
        }

        public static bool ShouldExcludeABFile(string inputFileName)
        {
            foreach (var fileName in excludeABFiles)
            {
                if (inputFileName.EndsWith(fileName))
                {
                    return true;
                }
            }

            return false;
        }

        public static void CreatePatchFile(string outputPath)
        {
            /// create patch file
            try
            {
                var fileDic = new Dictionary<string, List<string>>();
                var sizeList = new List<List<string>>();
                var accList = new List<string>();

                var files = Directory.GetFiles(outputPath, "*", SearchOption.AllDirectories);
                var regex = new Regex(@"(?:update\.json|files\.txt|\.manifest|\.dll)$");
                var regexCrc = new Regex(@"CRC: (\d+)");

                string md52crcPath = Application.dataPath + "/../md52crc.txt";
                Dictionary<string, string> md52crcDic = null;
                if (File.Exists(md52crcPath))
                {
                    var md52crc = File.ReadAllText(md52crcPath);
                    md52crcDic = ToolUti.ToObj<Dictionary<string, string>>(md52crc);
                }

                foreach (var f in files)
                {
                    if (ShouldExcludeABFile(f))
                    {
                        continue;
                    }
                    //var file = File.ReadAllText(f);


                    var md5 = File2MD5(f);
                    var shortf = f.Replace(outputPath, "").Trim('\\', '/').Replace("\\", "/");
                    var crc32 = File2CRC32(f);
                    var crcUnity = "";

                    if (File.Exists(f + ".manifest"))
                    {
                        var manifestContent = File.ReadAllText(f + ".manifest");
                        var match = regexCrc.Match(manifestContent);
                        if (match.Groups.Count > 1)
                        {
                            crcUnity = match.Groups[1].ToString();
                        }
                    }
                    else
                    {
                        md52crcDic?.TryGetValue(md5, out crcUnity);
                        //"".Print("CreatePatchFile", md5, crcUnity);
                    }

                    //fileList.Add(string.Format("{0}|{1}", shortf ,md5));
                    var list = new List<string>()
                        { "current", md5, new FileInfo(f).Length + "", crc32.ToString(), crcUnity ?? "" };
                    fileDic.Add(shortf, list);
                }

                //var content = string.Join("\n", fileList.ToArray());
                var dic = new Dictionary<string, object>();
                dic["list"] = fileDic;


                //var chList = UIHelper.GetCharacterRes();
                //foreach (var f in chList)
                //{
                //    var fpath = (outputPath + "/" + f).Trim('\\', '/').Replace("\\", "/");
                //    if (File.Exists(fpath) == false) continue;
                //    var list2 = new List<string>() { f, "", new FileInfo(outputPath+"/"+f).Length + "" };
                //    sizeList.Add(list2);
                //}

                //sizeList.Sort((a, b) => a[2].Length != b[2].Length ? a[2].Length - b[2].Length : string.Compare(a[2], b[2]));

                //dic["size"] = sizeList;
                //var amount = 5 * 1024 * 1024;
                //foreach (var s1 in sizeList)
                //{
                //    var len = int.Parse(s1[2]);
                //    amount -= len;
                //    if(amount>0)
                //    {
                //        if (s1[0].EndsWith(".rendertexture")) continue;
                //        accList.Add(s1[0]);// +"|"+s1[2]);
                //    }
                //    else
                //    {
                //        break;
                //    }
                //}
                //dic["acc"] = accList;

                File.WriteAllText(string.Format("{0}/files.txt", outputPath), UIHelper.ToJson(dic));
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }
        }


        [MenuItem("AssetBundles/CreateLuaMd5File")]
        public static Dictionary<string, List<string>> CreateLuaMd5File(string outputPath, string fileName)
        {
            /// create patch file
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }

            string luaSouceFilesRootPath = "Assets/Lua";
            try
            {
                var fileDic = new Dictionary<string, List<string>>();

                var files = Directory.GetFiles(luaSouceFilesRootPath, "*.txt", SearchOption.AllDirectories);
                if (JenkinsEnv.Instance.GetInt("USE_PACKAGE_COPY", 0) != 1)
                {
                    files = LuaPackageLoader.LuaFileReplace(files);
                }

                foreach (var f in files)
                {
                    var md5 = File2MD5(f);
                    var shortf = f.Replace(outputPath, "").Trim('\\', '/').Replace("\\", "/");

                    var list = new List<string>() { md5, new FileInfo(f).Length + "" };
                    fileDic.Add(shortf, list);
                }

                //var content = string.Join("\n", fileList.ToArray());
                var dic = new Dictionary<string, object>();
                dic["list"] = fileDic;

                if (fileName == "")
                {
                    fileName = "luaPatchMd5files.txt";
                }

                File.WriteAllText(string.Format("{0}/{1}", outputPath, fileName), UIHelper.ToJson(dic));
                return fileDic;
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }

            return null;
        }


        public static int GetResouceVersion()
        {
            int resVersion = 0;
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(AssetBundlesOutputPath, platform);
            var update_info_path = outputPath + "/" + "update.json";
            string targetFileContent = "";
            if (File.Exists(update_info_path))
            {
                targetFileContent = File.ReadAllText(update_info_path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("update.json 读取失败，path:" + update_info_path);
                return resVersion;
            }

            Dictionary<string, object> oldUpdateJsion = UIHelper.ToObj<Dictionary<string, object>>(targetFileContent);
            if (oldUpdateJsion.ContainsKey("files_url"))
            {
                var files_url = oldUpdateJsion["files_url"];

                Regex driverRegex = new Regex(@".+/resource/(\d+)/files.txt");
                var matches = driverRegex.Matches(files_url.ToString());
                if (matches.Count > 0)
                {
                    var match = matches[0];
                    resVersion = int.Parse(match.Groups[1].Value);

                    Debug.Log("update.json 读取resversion:" + resVersion + " match:" + match);
                }
            }
            //因为读取的为上次打包的版本，本次打包版本需要+1

            return resVersion + 1;
        }


        public static void CopySourceLuaAbToTargetPath(string sourcePath, string targetPath)
        {
            List<string> copyList = new List<string>()
            {
                "exec0.asset", "exec1.asset", "exec2.asset", "exec3.asset", "exec4.asset", "exec0.asset.manifest",
                "exec1.asset.manifest", "exec2.asset.manifest", "exec3.asset.manifest", "exec4.asset.manifest",
                "luascript.zip"
            };
            string sourceFilePath;
            string targetFilePath;
            EditorHelp.CheckDir(targetPath);
            foreach (string name in copyList)
            {
                sourceFilePath = string.Format("{0}/{1}", sourcePath, name);
                if (!System.IO.File.Exists(sourceFilePath))
                {
                    continue;
                }

                targetFilePath = string.Format("{0}/{1}", targetPath, name);

                File.Copy(sourceFilePath, targetFilePath, true);
            }
        }

        //public static int SortChangeFiles( a)

        public static void CreateMiniPatchFile(string[] files, string outputPath)
        {
            if (files == null || files.Length == 0)
            {
                return;
            }

            /// create patch file
            try
            {
                AssetbundlesCrcManager.setEncryptSets();
                var fileList = new Dictionary<string, List<string>>();
                var regexCrc = new Regex(@"CRC: (\d+)");

                string md52crcPath = Application.dataPath + "/../md52crc.txt";
                Dictionary<string, string> md52crcDic = null;
                if (File.Exists(md52crcPath))
                {
                    var md52crc = File.ReadAllText(md52crcPath);
                    md52crcDic = ToolUti.ToObj<Dictionary<string, string>>(md52crc);
                }

                foreach (var f in files)
                {
                    if (ShouldExcludeABFile(f))
                    {
                        continue;
                    }

                    var fPath = outputPath + "/" + f;
                    var metaPath = fPath + ".manifest";
                    if (AssetBundleManager.IsEncryptType(f))
                    {
                        fPath += ".zip";
                    }


                    var md5 = File2MD5(fPath);
                    var shortf = fPath.Replace(outputPath, "").Trim('\\', '/').Replace("\\", "/");
                    var crc32 = File2CRC32(fPath);
                    var crcUnity = "";

                    if (File.Exists(metaPath))
                    {
                        var manifestContent = File.ReadAllText(metaPath);
                        var match = regexCrc.Match(manifestContent);
                        if (match.Groups.Count > 1)
                        {
                            crcUnity = match.Groups[1].ToString();
                        }
                    }
                    else
                    {
                        md52crcDic?.TryGetValue(shortf, out crcUnity);
                    }

                    if (!File.Exists(fPath))
                    {
                        Debug.LogError("can not find the file by path:" + fPath);
                        continue;
                    }

                    //fileList.Add(string.Format("{0}|{1}", shortf ,md5));
                    var list = new List<string>()
                        { "current", md5, new FileInfo(fPath).Length + "", crc32.ToString(), crcUnity ?? "" };
                    //fileDic.Add(shortf, list);


                    //var file = File.ReadAllText(f);
                    //var md5 = File2MD5(fPath);
                    //var shortf = f.Replace(outputPath, "").Trim('\\', '/').Replace("\\", "/");
                    //fileList.Add(string.Format("{0}|{1}", shortf ,md5));
                    //fileList.Add(f, new List<string>() { "current", md5 });
                    var fileName = GetEncryFileName(f);
                    fileList.Add(fileName, list);
                }

                //var content = string.Join("\n", fileList.ToArray());
                var dic = new Dictionary<string, object>();
                dic["list"] = fileList;
                File.WriteAllText("files.txt", UIHelper.ToJson(dic));
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }
        }

        static public string GetEncryFileName(string fileName)
        {
            if (AssetBundleManager.IsEncryptType(fileName) && !fileName.EndsWith(".zip"))
            {
                return fileName + ".zip";
            }

            return fileName;
        }

        //将制定文件内容转化为CRC32
        static public long File2CRC32(string path)
        {
            long res = 0;
            if (File.Exists(path))
            {
                byte[] data = File.ReadAllBytes(path);
                var crc32 = new ICSharpCode.SharpZipLib.Checksums.Crc32();
                crc32.Update(data);

                res = crc32.Value;
            }

            return res;
        }

        //将制定文件内容转化为MD5
        static public string File2MD5(string path)
        {
            string res = null;
            if (File.Exists(path))
            {
                byte[] data = File.ReadAllBytes(path);
                res = GetMD5HashFromFile(data);
            }

            return res;
        }

        public static string GetMD5HashFromFile(byte[] data)
        {
            var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
            md5.Clear();

            var destString = "";
            for (int i = 0; i < md5Data.Length; i++)
            {
                destString += string.Format("{0:x2}", md5Data[i]);
            }

            destString = destString.PadLeft(32, '0');
            return destString;
        }

        static private Hash128 File2Hash128(string path)
        {
            if (File.Exists(path))
            {
                var md5 = File2MD5(path);
                return Hash128.Parse(md5);
            }

            return default(Hash128);
        }

        public static void EncryptAssetBundles(string encryptPath)
        {
            AssetbundlesCrcManager.setEncryptSets();

            var assetBundleName = "luascript";

            //var assetBundleNames = AssetDatabase.GetAllAssetBundleNames ();
            //foreach (var assetBundleName in assetBundleNames) {
            string assetBundlePath = encryptPath + "/" + assetBundleName;
            if (assetBundleName == "luascript")
            {
                Debug.LogError(
                    "脚本加密！！！！！assetBundlePath:" + assetBundlePath + "------assetBundleName" + assetBundleName);
            }

            if (File.Exists(assetBundlePath) && AssetbundlesCrcManager.isEncryptType(assetBundleName))
            {
                string newFilePath = assetBundlePath + ".zip";
                Debug.LogError("2脚本加密！！！！！assetBundlePath:" + assetBundlePath + "assetBundleName" + assetBundleName +
                               "newFilePath" + newFilePath);
                if (File.Exists(newFilePath))
                    File.Delete(newFilePath);
                EncryptFile(assetBundlePath, assetBundleName, newFilePath);
                File.Delete(assetBundlePath);
            }
            //}
        }

        public static Dictionary<string, string> assetBundleNamesDict() //  把资源目录所有文件添加到字典assetBundleNames
        {
            Dictionary<string, string> assetBundleNames = new Dictionary<string, string>();
            foreach (string assetBundleName in AssetDatabase.GetAllAssetBundleNames())
            {
                assetBundleNames.Add(assetBundleName, assetBundleName);
            }

            return assetBundleNames;
        }

        public static void ClearAssetBundleFile(DirectoryInfo dir, Dictionary<string, string> asseBundleNamesDict,
            string platform)
        {
            DirectoryInfo[] dii = dir.GetDirectories();
            FileInfo[] fil = dir.GetFiles();
            foreach (DirectoryInfo d in dii)
            {
                ClearAssetBundleFile(d, asseBundleNamesDict, platform);
            }

            foreach (FileInfo f in fil)
            {
                string basepath = f.FullName.Replace("\\", "/");
                int index = basepath.LastIndexOf("AssetBundles/" + platform);
                index += ("AssetBundles/" + platform).Length + 1;
                string fileName = basepath.Substring(index);
                if (fileName.EndsWith(".zip"))
                    fileName = fileName.TrimEnd(".zip".ToCharArray());

                if (fileName.EndsWith(".manifest") || fileName.EndsWith(platform))
                    continue;


                if (asseBundleNamesDict.ContainsKey(fileName))
                {
                    continue;
                }
                else
                {
                    string deleteFile = f.FullName;
                    string mainfestFile = deleteFile + ".manifest";
                    if (deleteFile.EndsWith(".zip"))
                        mainfestFile = deleteFile.TrimEnd(".zip".ToCharArray()) + ".manifest";

                    Debug.Log(f.FullName);
                    File.Delete(f.FullName);
                    File.Delete(mainfestFile);
                }
            }
        }

        public static void EncryptFile(string filePathName, string assetBundleName, string newPathName)
        {
            var content = File.ReadAllBytes(filePathName);
            var encryptContent = Common.Aes.Encryptor(content, Script.MainLoop.AssetBundleEncryptKey,
                Common.AlgorithmUtils.HashUtf8MD5(assetBundleName).Substring(8, 16));
            File.WriteAllBytes(newPathName, encryptContent);
        }

        public static void DecryptAssetBundles()
        {
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            string outputPath = Path.Combine(AssetBundlesOutputPath, GetPlatformFolderForAssetBundles(buildTarget));
            if (!Directory.Exists(outputPath))
            {
                return;
            }

            DecryptAssetBundles(outputPath);
        }

        public static void DecryptAssetBundles(string decryptPath)
        {
            var assetBundleNames = AssetDatabase.GetAllAssetBundleNames();
            foreach (var assetBundleName in assetBundleNames)
            {
                string encryptBundlePath = string.Format("{0}/{1}.zip", decryptPath, assetBundleName);
                if (File.Exists(encryptBundlePath))
                {
                    string assetBundlePath = string.Format("{0}/{1}", decryptPath, assetBundleName);
                    if (File.Exists(assetBundlePath))
                        File.Delete(assetBundlePath);
                    DecryptFile(encryptBundlePath, assetBundleName, assetBundlePath);
                    File.Delete(encryptBundlePath);
                }
            }
        }

        private static void DecryptFile(string filePathName, string assetBundleName, string newPathName)
        {
            var content = File.ReadAllBytes(filePathName);
            var decryptContent = Common.Aes.Decryptor(content, Script.MainLoop.AssetBundleEncryptKey,
                Common.AlgorithmUtils.HashUtf8MD5(assetBundleName).Substring(8, 16));
            File.WriteAllBytes(newPathName, decryptContent);
        }

        public static void BuildPlayer()
        {
            var outputPath = EditorUtility.SaveFolderPanel("Choose Location of the Built Game", "", "");
            if (outputPath.Length == 0)
                return;
            BuildPlayer(EditorUserBuildSettings.activeBuildTarget, outputPath);
        }

        public static void BuildPlayerWithoutCopyAB()
        {
            var outputPath = EditorUtility.SaveFolderPanel("Choose Location of the Built Game", "", "");
            if (outputPath.Length == 0)
                return;
            BuildPlayer(EditorUserBuildSettings.activeBuildTarget, outputPath, false, false);
        }

        static bool debugging = false;

        static void DefaultSetAction()
        {
            b_strip_log = false;
            debugging = false;
            patch_version = 0;
        }

        public static bool IsEnableSwitch(Dictionary<string, object> configDic, string key)
        {
            object value;
            if (!configDic.TryGetValue(key, out value))
            {
                Debug.LogWarning("Do not find " + key);
                return false;
            }

            var boolValue = Convert.ToBoolean(value);
            Debug.LogWarning("Editor Build Config, " + key + " is " + boolValue);
            return boolValue;
        }

        public static string GetBuildConfig(Dictionary<string, object> configDic, string key)
        {
            object value;
            if (!configDic.TryGetValue(key, out value))
            {
                Debug.LogWarning("Do not find " + key);
                return string.Empty;
            }

            var strConfig = Convert.ToString(value);
            Debug.LogWarning("Editor Build Config, " + key + " is " + strConfig);
            return strConfig;
        }

        static string CasualGame_root = "Assets/CasualGame";

        //private static string minigame = string.Empty;//测试的游戏打包
        //[MenuItem("AssetBundles/BuildMiniGames")]
        static void BuildMiniGamesByBuildPackage()
        {
            //是否开启保留稳定的小游戏
            bool enableBuildPackageMiniGame = JenkinsEnv.Instance.GetBool("BuildMiniGamesByBuildPackage", false);
            Debug.Log("enableBuildPackageMiniGame state : " + enableBuildPackageMiniGame);
            if (!enableBuildPackageMiniGame) return;

            var buildTarget = EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android
                ? BuildTarget.Android
                : BuildTarget.iOS;
            var buildJsonPath = CasualGame_root + "/buildpackage.json";
            var buildJsonCfg = JenkinsEnv.ToObj<Dictionary<string, string[]>>(File.ReadAllText(buildJsonPath));
            //小游戏合集
            CasualGameBuild.Instance.AddCollectionGameRes2Key2Mark();
            CasualGameBuild.Instance.BuildCombineMark();
            var key2MarkPath = CasualGame_root + "/key2mark.json";
            var key2MarkCfg = JenkinsEnv.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2MarkPath));

            buildJsonCfg.TryGetValue("common", out var includes);
            includes = includes ?? new string[0];

            //所有的路径
            List<string> allPathList = new List<string>();
            allPathList.Clear();
            allPathList.AddRange(includes);
            string[] games = new string[0];
            if (buildTarget == BuildTarget.Android)
            {
                buildJsonCfg.TryGetValue("buildAndroid", out var buildAndroid);
                buildAndroid = buildAndroid ?? new string[0];
                allPathList.AddRange(buildAndroid);
                games = buildAndroid;
            }
            else if (buildTarget == BuildTarget.iOS)
            {
                buildJsonCfg.TryGetValue("buildIOS", out var buildIOS);
                buildIOS = buildIOS ?? new string[0];
                allPathList.AddRange(buildIOS);
                games = buildIOS;
            }
            else
            {
                Debug.LogError("no platform");
            }

            var sourceGame = games.ToList();
            //打包的游戏添加
            //if (minigame != string.Empty)
            //{
            //    if (!sourceGame.Contains(minigame))
            //    {
            //        sourceGame.Add(minigame);
            //    }
            //}
            var allGames = sourceGame.ToArray();
            for (int i = 0; i < allGames.Length; i++)
            {
                var pathName = allGames[i];
                Debug.Log("need build game: " + pathName);
                key2MarkCfg.TryGetValue(pathName, out var gameLibs);
                gameLibs = gameLibs ?? new string[0];
                for (int j = 0; j < gameLibs.Length; j++)
                {
                    var pathNameLib = gameLibs[j];
                    if (!allPathList.Contains(pathNameLib))
                    {
                        allPathList.Add(pathNameLib);
                        //Debug.Log(gameLibs + "/lib : " + pathNameLib);
                    }
                }
            }

            //for (int i = 0; i < allPathList.Count; i++)
            //{
            //    Debug.Log("filefolder : " + allPathList[i]);
            //}
            var ds = Directory.GetDirectories("Assets/CasualGame", "*", SearchOption.TopDirectoryOnly);
            for (int i = 0; i < ds.Length; i++)
            {
                var filefolder = Path.GetFullPath(ds[i]).Replace("\\", "/");
                string filefolderName = Path.GetFileNameWithoutExtension(filefolder);
                if (allPathList.Contains(filefolderName)) continue;


                "".Print("delete game name ", filefolderName);
                if (Directory.Exists(filefolder))
                {
                    Directory.Delete(filefolder, true);
                    "".Print("delete game name ", filefolderName);
                }
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        //[MenuItem("AssetBundles/BuildPlayerJenkins")]
        public static void BuildPlayerJenkins()
        {
            Debug.LogWarning("Build Player Jenkins");

            //string svn_url = JenkinsEnv.Instance.Get("SVN_URL");
            //string build_url = JenkinsEnv.Instance.Get("BUILD_URL");
            // string token= JenkinsEnv.Instance.Get("Token","cb94fce71573db80818d3c2bc90845d3bef7f3b1ea20a19e4429c1df1c4e6d7d");
            bool b_buildassetbundle = false; //是否执行了-buildassetbundle操作
            // bool dataIsVerify = TagLaygerBuildHelper.DataAnalize();
            // Debug.LogWarning($"TagLaygerBuildHelper.DataAnalize()  result={dataIsVerify}");
            // //新增检测层级跟标签
            // if (!dataIsVerify)
            // {
            //     SendToDingDingSimple(token,"标签标题",$"工程的标签或者层级被错误修改了  请检查，svnurl={svn_url},jenkinsUrl={build_url}",false);
            //     Debug.LogWarning("i send msg to dingding  please check msg in dingding ");
            // }
            //Debug.LogWarning("Build Player Jenkins");

            // BuildMiniGamesByBuildPackage();

            string _outputpath = "";
            Action<string, string> commonFunc = (string key, string val) =>
            {
                switch (key)
                {
                    case "-strip_log":
                        b_strip_log = val == "1" || val == "3";
                        break;
                    case "-outputpath":
                        _outputpath = val;
                        break;
                    case "-pack":
                        AssetBundleManager.SetEncryptSets();
                        var files = ResTag.PackTag(val);
                        string outputPath = Path.Combine(AssetBundlesOutputPath,
                            GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
                        if (!Directory.Exists(outputPath))
                            Directory.CreateDirectory(outputPath);

                        CreateMiniPatchFile(files, outputPath);
                        break;
                    case "-saveatlas":
                        //var args_save_atlas = "-saveatlas";
                        //var bSaveAtlas = dic.ContainsKey(args_save_atlas);
                        //if (bSaveAtlas)
                    {
                        UIHelper.local_atlas_tex();
                    }
                        break;
                    case "-buildassetbundle":
                        try
                        {
                            var abResult = BuildAssetBundles();
                            if (!abResult)
                            {
                                Debug.Log("build assetbundle failed!");
                                EditorApplication.Exit(1);
                            }

                            b_buildassetbundle = true;
                        }
                        catch (Exception e)
                        {
                            Debug.LogError(e);
                            EditorApplication.Exit(1);
                        }

                        break;
                    case "-cal_svn_ver":
                        var svnurl = "svn_url";
                        if (File.Exists(svnurl))
                        {
                            var info = File.ReadAllText(svnurl);
                            var lines = info.Split('\n');
                            var dicInfo = new Dictionary<string, string>();
                            foreach (var line in lines)
                            {
                                if (line.Contains(":") == false)
                                    continue;
                                var kv = line.Split(':');
                                dicInfo[kv[0].Trim()] = kv[1].Trim();
                            }

                            Debug.Log(UIHelper.ToJson(dicInfo));
                            var relativeURL = "Relative URL";
                            if (dicInfo.ContainsKey(relativeURL))
                            {
                                var v = dicInfo[relativeURL];
                                Match match = Regex.Match(v, @"\^/([^/]*)/");
                                if (match.Success)
                                {
                                    File.WriteAllText(svnurl, match.Groups[1].Value);
                                }
                            }
                        }

                        break;
                    case "-debug_unity":
                        debugging = true;
                        //if(debugging)
                        //{
                        //    var fpath = "forcerp/combiner/BConfig.cs";
                        //    var ccc = File.ReadAllText(fpath);
                        //    File.WriteAllText("Assets/Scripts/CommonTools/BConfig.cs",ccc);
                        //}
                        break;
                    case "-buildplayer":
                        if (string.IsNullOrEmpty(_outputpath))
                        {
                            Debug.LogError("empty outputpath");
                            break;
                        }

                        EditorHelp.CheckDir(_outputpath);
                        Debug.Log(string.Format("_outputpath:{0}", _outputpath));
                        try
                        {
                            var p_LProfilerIp = JenkinsEnv.Instance.GetBool("p_LProfilerIp");
                            if (p_LProfilerIp)
                            {
                                LuaVersion.ImportLuaProfiler();
                                LuaVersion.EnableLuaProfiler();
                            }
                            else
                            {
                                LuaVersion.DeleteLuaProfilerWithBuild();
                                //LuaVersion.DisableLuaProfiler();
                            }

                            var buildResult = BuildPlayer(EditorUserBuildSettings.activeBuildTarget, _outputpath,
                                false);
                            if (!buildResult)
                            {
                                Debug.Log("build apk failed!");
                                EditorApplication.Exit(1);
                            }
                        }
                        catch (Exception e)
                        {
                            //避免 jenkins 未能正确退出 unity，卡住打包流程
                            Debug.LogError(e);
                            EditorApplication.Exit(1);
                        }

                        break;
                    case "-cache_update_info":
                        CacheCurrentUpdateInfo();
                        break;
                    case "-buildplayer_no_ab":
                        if (string.IsNullOrEmpty(_outputpath))
                        {
                            Debug.LogError("empty outputpath");
                            break;
                        }

                        EditorHelp.CheckDir(_outputpath);
                        Debug.Log(string.Format("_outputpath:{0}", _outputpath));
                        AssetbundlesMenuItems.ChooseDll();
                        try
                        {
                            var p_LProfilerIp = JenkinsEnv.Instance.GetBool("p_LProfilerIp");
                            if (p_LProfilerIp)
                            {
                                LuaVersion.ImportLuaProfiler();
                                LuaVersion.EnableLuaProfiler();
                            }
                            else
                            {
                                LuaVersion.DeleteLuaProfilerWithBuild();
                                //LuaVersion.DisableLuaProfiler();
                            }

                            var buildResult = BuildPlayer(EditorUserBuildSettings.activeBuildTarget, _outputpath, false,
                                false);
                            if (!buildResult)
                            {
                                Debug.Log("build apk failed!");
                                EditorApplication.Exit(1);
                            }
                        }
                        catch (Exception e)
                        {
                            //避免 jenkins 未能正确退出 unity，卡住打包流程
                            Debug.LogError(e);
                            EditorApplication.Exit(1);
                        }

                        //BuildPlayerNoABforUpdate();
                        break;
                    case "-version":
                    case "-bundle_version":
                        PlayerSettings.bundleVersion = val;
                        break;
                    case "-patch_version":
                        try
                        {
                            patch_version = Convert.ToInt32(val);
                        }
                        catch (Exception e)
                        {
                            "".PrintError(e.ToString());
                        }

                        break;

//                     case "-backend_mono2x": // D国内版 F国外版
// #if UNITY_ANDROID && UNITY_2017_4_OR_NEWER
//                         var targetGroup =
//  EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android ? BuildTargetGroup.Android : BuildTargetGroup.iOS;
//                         // 因为国外版上google play。需要64位只能开启IL2CPP
//                         //PlayerSettings.SetScriptingBackend(targetGroup, val == "F" ? ScriptingImplementation.IL2CPP : ScriptingImplementation.Mono2x);
//                         //PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.IL2CPP);

//                         if (val == "F")
//                         {
//                             PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.IL2CPP);
//                             PlayerSettings.Android.targetArchitectures =
//  AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
//                         }
//                         else
//                         {
//                             PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.Mono2x);
//                             PlayerSettings.Android.targetArchitectures =
//  AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
//                             //PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7;
//                         }
// #endif
//                         break;
                    case "-obb":
#if UNITY_ANDROID
                        PlayerSettings.Android.useAPKExpansionFiles = val != "0";
#endif
                        break;
                    case "-aab":
                        Debug.Log("build android app bundles");
                        EditorUserBuildSettings.exportAsGoogleAndroidProject = val == "1";
                        break;
                    case "-uwa":

                        var b = val == "1";
                        if (b)
                        {
                            Debug.LogWarning("build uwa,compile scripte with Mono2x");
                            PlayerSettings.SetScriptingBackend(
                                EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android
                                    ? BuildTargetGroup.Android
                                    : BuildTargetGroup.iOS, ScriptingImplementation.Mono2x);
                            PlayerSettings.Android.forceInternetPermission = false;
                            PlayerSettings.Android.forceSDCardPermission = b;
                            PlayerSettings.strippingLevel = StrippingLevel.Disabled;
                            AssetDatabase.SaveAssets();
                            AssetDatabase.Refresh();
                        }

                        break;
                    case "-bundlebuild":
#if UNITY_ANDROID
                        PlayerSettings.Android.bundleVersionCode = Convert.ToInt32(val);
#elif UNITY_IOS
                        PlayerSettings.iOS.buildNumber = val;
#endif
                        break;
                    case "-keystorepass":
                        if (!string.IsNullOrEmpty(val))
                        {
                            PlayerSettings.Android.keystoreName = Application.dataPath + "/../KeyStore/hero.keystore";
                            PlayerSettings.Android.keystorePass = val;
                        }

                        break;
                    case "-keystoreName":
                        if (!string.IsNullOrEmpty(val))
                        {
                            PlayerSettings.Android.keystoreName = Application.dataPath + val;
                        }

                        break;
                    case "-keyaliasName":
                        if (!string.IsNullOrEmpty(val))
                        {
                            PlayerSettings.Android.keyaliasName = val;
                        }

                        break;
                    case "-keyaliaspass":
                        if (!string.IsNullOrEmpty(val))
                        {
                            PlayerSettings.Android.keyaliasName = "marvelouskey"; //"mhiwkey";
                            PlayerSettings.Android.keyaliasPass = val;
                        }

                        break;
                    case "-package_name":
                        PlayerSettings.applicationIdentifier = val;
                        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, val);

                        //Debug.Log("applicationIdentifier " + PlayerSettings.applicationIdentifier);
                        break;
                    case "-product_name":
                        PlayerSettings.productName = /*Regex.Replace(val, " *$", ""); */val.TrimEnd(new char[] { ' ' });
                        Debug.Log("set productName!" + PlayerSettings.productName);
                        break;
                    case "-binaryfiles":
                        var respath = val;
                        BinaryFilesTexts(respath);
                        break;
                    case "-update_1":
                        var path = val;
                        CopyUpdate_1(path);
                        break;
                    case "-gen_rubbish_code":
                        GenRubbishCode();
                        break;
                    case "-swap_code_block":
                        AutoBuildDLL.SwapCodeBlock();
                        break;
                    case "-patch_mark":
                        GetNewestPatchFile(val);
                        //在工作目录下使用当前最新文件版本更新 files.txt 文件。此目录下的 files.txt 文件不一定包含所有 ab 文件，可能只是小包
                        SetFileVersionForFileTxt("files.txt");
                        //在生成的 ab 资源路径下按平台使用当前最新文件版本更新 files.txt 文件
                        var filesPath = string.Format("{0}/files.txt",
                            Path.Combine(AssetBundlesOutputPath,
                                GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)));
                        SetFileVersionForFileTxt(filesPath);
                        break;
                    case "-ios_auto_sign":
                        PlayerSettings.iOS.appleEnableAutomaticSigning = Convert.ToBoolean(val);
                        Debug.Log("Set appleEnableAutomaticSigning:" + val);
                        break;
                    case "-ios_provisioning_profile":
                        PlayerSettings.iOS.iOSManualProvisioningProfileID = val;
                        PlayerSettings.iOS.iOSManualProvisioningProfileType = ProvisioningProfileType.Development;
                        Debug.Log("Set iOSManualProvisioningProfileID:" + val);
                        break;
                    case "-ios_teamID":
                        PlayerSettings.iOS.appleDeveloperTeamID = val;
                        Debug.Log("Set appleDeveloperTeamID:" + val);
                        break;
                    case "-comparePreVersion":
                        int comparePreVersion = JenkinsEnv.Instance.GetInt("comparePreVersion", -1);
                        if (comparePreVersion == 1)
                        {
                            ComparePreVersion();
                            Debug.Log("start compare  resource of preversion");
                        }
                        else
                        {
                            Debug.Log("compare  resource of preversion disable hello world");
                        }

                        break;

                    default:
                        break;
                }
            };

            //        var allRegexPattern = new[]
            //        {
            //            @"(-uwa) (\d?)",
            //            @"(-strip_log) (\d?)",
            //            @"(-outputpath) (\S+)",
            //            @"(-update_1) (\S+)",
            //            @"(-version) (\S+)",
            //            @"(-patch_version) (\d+)",
            //            @"(-keystorepass) ((?:[^-]\S*)?)", // 安卓
            //            @"(-keyaliaspass) ((?:[^-]\S*)?)", // 安卓
            //            @"(-keystoreName) ((?:[^-]\S*)?)", // 安卓
            //            @"(-keyaliasName) ((?:[^-]\S*)?)", // 安卓
            //            @"(-package_name) ([\w.]+)",
            //           "(-product_name) \"(.*)\"+",  //不使用@"(-product_name) (\S+)",否则产品名中不能出现空格
            //            @"(-bundlebuild) (\d+)",
            //            @"(-buildassetbundle) (.??)",
            //            @"(-cache_update_info) (.??)",
            //            @"(-pack) (\S+)",
            //            @"(-obb) (\d?)",
            //            @"(-aab) (\d?)", //是否导出android app bundles
            //            @"(-distrib_version) ([DF]?)",
            //            @"(-saveatlas) (.??)",
            //@"(-cal_svn_ver) (.??)",
            //            @"(-debug_unity) (.??)",
            //            @"(-buildplayer) (.??)",
            //            @"(-buildplayer_no_ab) (.??)",
            //            @"(-gen_rubbish_code) (.??)",
            //            @"(-swap_code_block) (.??)",
            //            @"(-patch_mark) (\S+)",
            //        };
            var paramList = new List<string>
            {
                "-uwa",
                "-strip_log",
                "-outputpath",
                "-update_1",
                "-bundle_version",
                "-version",
                "-patch_version",
                "-keystorepass",
                "-keyaliaspass",
                "-keystoreName",
                "-keyaliasName",
                "-package_name",
                "-product_name",
                "-ios_auto_sign",
                "-ios_provisioning_profile",
                "-ios_teamID",
                "-bundlebuild",
                "-buildassetbundle",
                "-cache_update_info",
                "-pack",
                "-obb", "-aab",
                "-distrib_version",
                "-saveatlas",
                "-cal_svn_ver",
                "-debug_unity",
                "-buildplayer",
                "-buildplayer_no_ab",
                "-gen_rubbish_code",
                "-swap_code_block",
                "-patch_mark",
                "-comparePreVersion"
            };
            // 保证命令按 paramList 顺序执行，防止 sh 中传参顺序影响执行结果
            List<KeyValue> argsOrdered = new List<KeyValue>();
            foreach (var paramName in paramList)
            {
                argsOrdered.Add(new KeyValue(paramName, null));
            }

            BuildSplashScreenCfg();
            BuildMinSdkVersion();

            string ios14EditorPrefsKey = "adjustiOS14Support";
            var isLocalEnableAdjustIOS14 = EditorPrefs.GetBool(ios14EditorPrefsKey, false);
            var isEnableAdjustIOS14 = true; //默认开启

            if (File.Exists(editor_build_config))
            {
                var strEditorBuildConfig = File.ReadAllText(editor_build_config);
                var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strEditorBuildConfig);
                isEnableAdjustIOS14 = IsEnableSwitch(editorBuildConfig, "ENABLE_ADJUST_IOS14");
            }

            if (isLocalEnableAdjustIOS14 != isEnableAdjustIOS14)
            {
                EditorPrefs.SetBool(ios14EditorPrefsKey, isEnableAdjustIOS14);
                isLocalEnableAdjustIOS14 = EditorPrefs.GetBool(ios14EditorPrefsKey, false);
            }

            Debug.Log("Adjust SDK, iOS 14 support is:" + isLocalEnableAdjustIOS14);

            string _key, _value;
            _key = _value = null;
            // commonFunc 只接收(key, value)，所以这里先只处理一个value的情况
            var args = Environment.GetCommandLineArgs();
            foreach (var param in args)
            {
                if (param.Length > 0 && param.ElementAt(0) == '-')
                {
                    if (param != _key)
                    {
                        AddCmdArgs(_key, _value, argsOrdered);
                        _key = param;
                        _value = "";
                    }
                }
                else
                {
                    _value = param;
                }

                Debug.Log("param:" + param);
            }

            AddCmdArgs(_key, _value, argsOrdered);
            foreach (var argCmd in argsOrdered)
            {
                if (argCmd.Value == null)
                {
                    continue;
                }

                try
                {
                    Debug.Log("commonFunc:" + argCmd.Key + ",value:" + argCmd.Value);
                    commonFunc(argCmd.Key, argCmd.Value);
                    Debug.Log("commonFunc-end:" + argCmd.Key);
                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                    EditorApplication.Exit(1);
                }
            }

            // 正则太难以维护，且如 product_name 参的不确定性极易导致匹配规则不合适，且每次添加参数都可能需要配置新匹配规则
            //StringBuilder builder = new StringBuilder();
            //System.Environment.GetCommandLineArgs().ForEach((str) => { builder.Append(str + " "); });
            //string fullStr = builder.ToString();

            //DefaultSetAction();
            //foreach (var regexPattern in allRegexPattern)
            //{
            //    Match match = Regex.Match(fullStr, regexPattern);
            //    if (match.Success)
            //    {
            //        Debug.Log("key:" + match.Groups[1].Value + ",value:" + match.Groups[2].Value);
            //        LogHelp.Instance.Log("commonFunc-"+ match.Groups[1].Value);

            //        commonFunc(match.Groups[1].Value, match.Groups[2].Value);
            //        LogHelp.Instance.Log("commonFunc-end-"+ match.Groups[1].Value);
            //    }
            //}

            //reset to default
            DefaultSetAction();

            if (b_buildassetbundle)
            {
                Debug.Log(
                    "执行 -buildassetbundle 可能会超时导致打包失败，这里添加当执行 -buildassetbundle 命令时，添加正常退出unity，Jenkins.sh中取消-quit参数");
                EditorApplication.Exit(0);
            }
            else
            {
                Debug.Log("no -buildassetbundle ---------");
            }
        }

        /// <summary>
        /// 打包阶段 下载并拷贝小游戏独立资源进streamingAssetsPath下
        /// </summary>
        /// <param name="dirRootOnStreamingAsset"></param>
        public static void DownloadStandAlonhTinyRes(BuildTarget buildTarget, string[] p_stand_alone_res_key,
            bool isdelete = true)
        {
            //这里可以拿到小游戏资源的目录 
            //例如打包机路径为D:\Roguelike_Projects\HServer\CasualGame_Pure_Res_105_002\TinyRes\CaualGame_Android_Assetbundles\SaveDog
            //这里会拿到url=http://172.18.2.105:8001/CasualGame_Pure_Res_105_002/TinyRes/CaualGame_Android_Assetbundles/
            // string serverUrlRoot = @"http://172.18.2.105:8001/CasualGame_Pure_Res_105_002/TinyRes/CaualGame_Android_Assetbundles/";
            //获取当前所有独立资源小游戏并下载（下载过的内容不要重复下载）
            //将资源拷贝进：dirRootOnStreamingAsset这个目录
            string serverUrlRoot = string.Empty;
            //获取server设置的url
            var root_ab_path = Path.Combine(Application.dataPath + "/../", AssetBundlesOutputPath);

            string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);

            // Setup the source folder for assetbundles.
            var root_ab_target_path = Path.Combine(root_ab_path, outputFolder);
            var updatejson_path = root_ab_target_path + "/update.json";
            string file = null;
            string streaming_ab_path;
            streaming_ab_path = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
            var ofolder = GetPlatformFolderForAssetBundles(buildTarget);
            var streaming_ab_target_path = Path.Combine(streaming_ab_path, ofolder);
            var streaming_updatejson_path = streaming_ab_target_path + "/update.json";

            if (File.Exists(updatejson_path))
            {
                file = File.ReadAllText(updatejson_path);
                var updator = (Dictionary<string, object>)MiniJSON.Json.Deserialize(file);
                if (updator.ContainsKey("p_stand_alone_server_root_url"))
                {
                    serverUrlRoot = updator["p_stand_alone_server_root_url"].ToString();
                    Debug.Log("server stand url ==" + serverUrlRoot);
                }
                else
                {
                    Debug.Log("server not set url");
                }
            }

            if (serverUrlRoot == string.Empty) //获取Jenkins设置的url
            {
                serverUrlRoot = JenkinsEnv.Instance.Get("p_stand_alone_server_root_url", string.Empty);
                Debug.Log("Jenkins stand url ==" + serverUrlRoot);
            }

            Debug.Log($"tinydebug===  DownloadStandAlonhTinyRes   urlroot=={serverUrlRoot}");

            var choose_increase_abParam = JenkinsEnv.Instance.Get("choose_increase_ab_param", "");
            //lua版本的加载要兼容能读取资源内置到apk内部
            if (!string.IsNullOrEmpty(serverUrlRoot))
            {
                CasualGameBuildNew.UpdateRes(serverUrlRoot, p_stand_alone_res_key, isdelete);
            }
            else
            {
                Debug.LogError("error  get p_stand_alone_server_root_url failed,");
                EditorApplication.Exit(1);
            }
        }

        public static void AddCmdArgs(string key, string value, List<KeyValue> cmdList)
        {
            if (string.IsNullOrEmpty(key))
            {
                return;
            }

            for (int i = 0; i < cmdList.Count; i++)
            {
                if (cmdList[i].Key == key)
                {
                    cmdList[i].Value = value;
                }
            }
        }

        //[MenuItem("AssetBundles/Set SplashScreen By Config")]
        public static void BuildSplashScreenCfg()
        {
            TextAsset textAsset = Resources.Load<TextAsset>("Logo/player_config");
            var player_config = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
            object splashScreenSet;

            var bShowSplashScreen = false;
            if (player_config.TryGetValue("SplashScreen", out splashScreenSet))
            {
                Dictionary<string, object> splashDic = splashScreenSet as Dictionary<string, object>;
                if (splashDic != null)
                {
                    object backgroundColor;
                    if (splashDic.TryGetValue("backgroundColor", out backgroundColor))
                    {
                        Color bgColor;
                        if (ColorUtility.TryParseHtmlString(backgroundColor as string, out bgColor))
                        {
                            PlayerSettings.SplashScreen.backgroundColor = bgColor;
                        }
                    }

                    object objShow;
                    if (splashDic.TryGetValue("show", out objShow))
                    {
                        bool.TryParse(objShow as string, out bShowSplashScreen);
                    }
                }
            }

            // 目前仅 iOS 使用 SplashScreen
            // Android 使用 androidSplashScreen (读写 ProjectSettings/ProjectSettings.asset 修改)，与 PlayerSettings.SplashScreen.show 只能二选一
            PlayerSettings.SplashScreen.show = bShowSplashScreen;
            Debug.Log("BuildSplashScreenCfg bShowSplashScreen:" + bShowSplashScreen);
        }

        //[MenuItem("AssetBundles/Set Android minSdkVersion")]
        [NUnit.Framework.Test]
        public static void BuildMinSdkVersion()
        {
#if UNITY_2018_1_OR_NEWER

            PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel24;
#else
            if (File.Exists(build_config))
            {
                var strBuildConfig = File.ReadAllText(build_config);
                var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strBuildConfig);
                var channel_tag = GetBuildConfig(editorBuildConfig, "CHANNEL_TAG");
                var isDomestic = GetBuildConfig(editorBuildConfig, "Q1SDK_DOMESTIC");
                Debug.Log("BuildMinSdkVersion channel_tag:" + channel_tag);
                if (!string.IsNullOrEmpty(channel_tag) && channel_tag == "com.q1.hero.huawei")
                {
                    //华为 account kit 要求 minSdkVersion = 17
                    //华为 analytics kit 要求 minSdkVersion = 19
                    //uses-sdk:minSdkVersion 17 cannot be smaller than version 19 declared in library [com.huawei.hms:hianalytics:5.0.5.300]
                    PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel19;
                }
                else if (!string.IsNullOrEmpty(isDomestic) && isDomestic == "True")
                {
                    //国内sdk oaid_sdk_1.0.23要求minSdkVersion = 21
                    PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel21;
                }
                else
                {
                    PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel16;
                }
            }
            else
            {
                Debug.LogError("BuildMinSdkVersion can not find file;" + build_config);
                PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel16;
            }
#endif
        }

        public static void BuildPlayerForIOS()
        {
            BuildPlayer(BuildTarget.iOS, "BuildIOS");
        }

        public static void BuildPlayerForAndroid()
        {
            BuildPlayer(BuildTarget.Android, "BuildAndroid");
        }

        public static bool BuildPlayer(BuildTarget buildTarget, string outputPath, bool buildab = true,
            bool copyab = true)
        {
            string[] levels = GetLevelsFromBuildSettings();
            if (levels.Length == 0)
            {
                Debug.Log("Nothing to build.");
                return false;
            }

            copyAssetBundle = copyab;

            // Build and copy AssetBundles.
            if (buildab)
            {
                var assetBundlesResult = BuildAssetBundles(buildTarget);
                if (!assetBundlesResult)
                {
                    return false;
                }
            }

            //EditorSettings.serializationMode = SerializationMode.Mixed;
            CopyAssetBundles(buildTarget);

            if (JenkinsEnv.Instance.GetBool("p_launch"))
            {
                //所有资源放到AssetBundles目录下，不再需要单独拷贝
                Launch.LaunchBuilder.DeleteLaunchFiles();
            }

            return BuildPlayerAfterRes(buildTarget, outputPath);
        }

        public static void CopyAssetBundles(BuildTarget buildTarget = BuildTarget.NoTarget)
        {
            if (buildTarget == BuildTarget.NoTarget) buildTarget = EditorUserBuildSettings.activeBuildTarget;
            string channelResToFilesDir = null;
            var copy_casualgame = JenkinsEnv.Instance.GetBool("copy_casualgame", false);
            var reskey = JenkinsEnv.Instance.Get("res_key", "");
            var isCombine = reskey.StartsWith("Combine");
            if (copyAssetBundle)
            {
                LogHelp.Instance.Log("CopyAssetBundlesTo-start");
                CopyAssetBundlesTo(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath), buildTarget);
                LogHelp.Instance.Log("CopyAssetBundlesTo-end");
            }
            else
            {
                if (copy_casualgame || isCombine)
                {
                    LogHelp.Instance.Log("Copy casualgame To-start");
                    var outputFolder = Path.Combine(GetPlatformFolderForAssetBundles(buildTarget), "casualgame");
                    var source = Path.Combine(Path.Combine(System.Environment.CurrentDirectory, AssetBundlesOutputPath),
                        outputFolder);
                    var destination =
                        System.IO.Path.Combine(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                            outputFolder);
                    if (System.IO.Directory.Exists(destination))
                        FileUtil.DeleteFileOrDirectory(destination);
                    CopyDirectoryByFilter(source, destination, new string[] { ".manifest", ".DS_Store" });


                    CasualGameBuild.Instance.InsertCasualGameRes2files(destination);
                    LogHelp.Instance.Log("Copy casualgame To-end");
                }

                LogHelp.Instance.Log("Copy casualchannel To-start");
                //复制多语言资源到首包
                channelResToFilesDir = CopyResToStreaming(buildTarget, CasualChannelBuild.bundleDir);
                LogHelp.Instance.Log("Copy casualchannel To-end");

                List<string> list1 = new List<string>
                {
                    Application.dataPath + "/UI/CommonBg/updatehero1.png",
                    Application.dataPath + "/CasualGame/collectionMark.json"
                };
                List<string> list2 = new List<string>
                {
                    Application.streamingAssetsPath + "/updatehero1.png",
                    Application.streamingAssetsPath + "/collectionMark.json"
                };
                if (buildTarget == BuildTarget.Android)
                {
                    list1.AddRange(new string[] { Application.dataPath + "/api_router_def.json" });
                    list2.AddRange(new string[] { Application.streamingAssetsPath + "/api_router_def.json" });
                    if (File.Exists(Application.dataPath + "/channel.json"))
                    {
                        list1.AddRange(new string[] { Application.dataPath + "/channel.json" });
                        list2.AddRange(new string[]
                        {
                            Path.Combine(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                                GetPlatformFolderForAssetBundles(buildTarget)) + "/channel.json"
                        });
                    }
                }
                else if (buildTarget == BuildTarget.iOS)
                {
                    //目前安卓平板没有用updateheroWidth.png，只有IOS ipad会用宽屏updateheroWidth.png
                    if (File.Exists(Application.dataPath + "/UI/CommonBg/updateheroWidth.png"))
                    {
                        list1.AddRange(new string[] { Application.dataPath + "/UI/CommonBg/updateheroWidth.png" });
                        list2.AddRange(new string[] { Application.streamingAssetsPath + "/updateheroWidth.png" });
                        LogHelp.Instance.Log("拷贝updateheroWidth:dataPath:" + Application.dataPath +
                                             "/UI/CommonBg/updateheroWidth.png" + ",streamingAssetsPath:" +
                                             Application.streamingAssetsPath + "/updateheroWidth.png");
                    }
                }

                string[] copyFor = list1.ToArray();
                string[] copyTo = list2.ToArray();
                for (int i = 0; i < copyFor.Length; i++)
                {
                    if (File.Exists(copyFor[i])) File.Copy(copyFor[i], copyTo[i], true);
                }

                LogHelp.Instance.Log("abPath:\n" +
                                     Path.Combine(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                                         GetPlatformFolderForAssetBundles(buildTarget)));
                CopyUpdateInfo();
            }

            var patch_mark = JenkinsEnv.Instance.Get("patch_mark");
            var streaming_ab_path = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
            string opFolder = GetPlatformFolderForAssetBundles(buildTarget);
            var streaming_ab_target_path = Path.Combine(streaming_ab_path, opFolder);
            var files2Path = string.Format("{0}/files2.txt", streaming_ab_target_path);
            CopyFiles2Txt2Local(patch_mark, files2Path);
            CopyFilesTxt2Local();
            LogHelp.Instance.Log("Copy2Package-start");
            CasualGameBuild.Instance.Copy2Package();
            LogHelp.Instance.Log("Copy2Package-end");

            LogHelp.Instance.Log("Channel Copy2Package-start");
            if (channelResToFilesDir != null)
            {
                List<string> fs = CasualChannelBuild.Instance.InsertCasualChannelRes2files(channelResToFilesDir);
                LogHelp.Instance.Log(channelResToFilesDir + ",channelRes:\n" + string.Join("\n", fs));
            }

            CasualChannelBuild.Instance.Copy2Package();
            LogHelp.Instance.Log("Channel Copy2Package-end");

            var p_stand_alone_res_key = JenkinsEnv.Instance.Get("p_stand_alone_res_key", string.Empty);
            var collection_reskey = JenkinsEnv.Instance.Get("collection_res_key", "");


            //支持多个独立热更小游戏            
            var p_muti_stand_alone_res_key = JenkinsEnv.Instance.Get("p_muti_stand_alone_res_key", string.Empty);

            if (p_muti_stand_alone_res_key != string.Empty)
            {
                if (copy_casualgame)
                {
                    //走独立热更时 删除老框架内置小游戏资源
                    var outputFolder = Path.Combine(GetPlatformFolderForAssetBundles(buildTarget), "casualgame");
                    var destination = System.IO.Path.Combine(
                        Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath), outputFolder);

                    string[] reskeyList = p_muti_stand_alone_res_key.Split(';');

                    //reskey为Tower时不删除，因为暂时士兵突击依赖小游戏的独立库，需要加载tower_exec
                    if (System.IO.Directory.Exists(destination) && reskey != "Tower")
                        FileUtil.DeleteFileOrDirectory(destination);
                    DownloadStandAlonhTinyRes(buildTarget, reskeyList);
                }
                else
                {
                    CasualGameBuildNew.DelteRes();
                }
            }
            else
            {
                //小游戏合集,//如果小游戏是Combine开头的小游戏,需要其中的小游戏是不是独立热更小游戏,是则拷贝资源到包内
                if (isCombine)
                {
                    CasualGameBuildNew.DelteRes();
                    var combineMark = CasualGameBuild.Instance.GetCombineMark();
                    List<string> standAloneGamesInCombineMark = new List<string>(combineMark[reskey].Length);
                    foreach (var key in combineMark[reskey])
                    {
                        if (CheckReskeyIsSupportStandAlong(key))
                        {
                            LogHelp.Instance.Log("IPMiniGame Copy stand_alone_res to streaming:" + key);
                            standAloneGamesInCombineMark.Add(key);
                        }
                        else
                        {
                            LogHelp.Instance.Log("IPMiniGame NormalReskey:" + key);
                        }
                    }

                    DownloadStandAlonhTinyRes(buildTarget, standAloneGamesInCombineMark.ToArray());
                }
                else if (!string.IsNullOrEmpty(collection_reskey))
                {
                    CasualGameBuild.Instance.AddCollectionGameRes2Key2Mark();
                    Dictionary<string, List<string>> standAloneGamesInPackageDic =
                        CasualGameBuild.Instance.GetCollectionStandAloneInPackage();
                    if (standAloneGamesInPackageDic.ContainsKey(collection_reskey))
                    {
                        if (standAloneGamesInPackageDic[collection_reskey].Count > 0)
                        {
                            DownloadStandAlonhTinyRes(buildTarget,
                                standAloneGamesInPackageDic[collection_reskey].ToArray());
                        }
                    }
                    else
                    {
                        CasualGameBuildNew.DelteRes();
                    }
                }
                else
                {
                    if (copy_casualgame && p_stand_alone_res_key != string.Empty)
                    {
                        //走独立热更时 删除老框架内置小游戏资源
                        var outputFolder = Path.Combine(GetPlatformFolderForAssetBundles(buildTarget), "casualgame");
                        var destination = System.IO.Path.Combine(
                            Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath), outputFolder);

                        string[] reskeyList = p_stand_alone_res_key.Split(';');

                        //reskey为Tower时不删除，因为暂时士兵突击依赖小游戏的独立库，需要加载tower_exec
                        if (System.IO.Directory.Exists(destination) && reskey != "Tower")
                            FileUtil.DeleteFileOrDirectory(destination);
                        DownloadStandAlonhTinyRes(buildTarget, reskeyList);
                    }
                    else
                    {
                        CasualGameBuildNew.DelteRes();
                    }
                }
            }


            if (string.IsNullOrEmpty(p_stand_alone_res_key) &&
                string.IsNullOrEmpty(JenkinsEnv.Instance.Get("res_key", "")) &&
                string.IsNullOrEmpty(collection_reskey) &&
                string.IsNullOrEmpty(p_muti_stand_alone_res_key))
            {
                "".Print("____打包含所有小游戏的母包(不包含独立热更小游戏)____");
                //生成小游戏合集分包删除列表（需要放在DownloadStandAlonhTinyRes接口调用之后）
                CasualGameBuild.Instance.GetCollectionExcludeConfig();
            }
            else
            {
                "".Print("不是打包含所有小游戏的母包");
            }

            DownLoadStandAloneSupportGameToStreamAssets();

            //不知道为什么包内现在有Android2目录，以及old.json,TMP/AssetBuilder
            DeleteUnUsedFiles();
        }


        private static List<string> unUsedFiles = new List<string>()
        {
            "Android2",
            "old.json",
            "TMP/AssetBuilder"
        };

        /// <summary>
        /// 删除包内无用文件
        /// </summary>
        public static void DeleteUnUsedFiles()
        {
            string assetBundlesPath = Path.Combine(Application.streamingAssetsPath, "AssetBundles");
            //遍历unUsedFiles,判断是文件夹还是文件，删除
            foreach (var file in unUsedFiles)
            {
                string filePath = Path.Combine(assetBundlesPath, file);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                else if (Directory.Exists(filePath))
                {
                    Directory.Delete(filePath, true);
                }
            }
        }

        public static bool CheckReskeyIsSupportStandAlong(string resKey)
        {
            if (DownLoadStandAloneSupportGameToStreamAssets())
            {
                var updateSrcFilePath = Path.Combine(Application.streamingAssetsPath, "AssetBundles", GetManifestName(),
                    "supportgame.csv");
                string csvStr = File.ReadAllText(updateSrcFilePath);
                DataTable dt = ParseCSV(csvStr);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if ((string)dt.Rows[i][0] == resKey && (string)dt.Rows[i][2] == "1")
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public static DataTable ParseCSV(string csvText, char delimiter = ',', bool hasHeader = true)
        {
            DataTable dataTable = new DataTable();

            // 将csv文本拆分成行
            string[] csvRows = csvText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            // 如果CSV包含头部，则创建列
            if (hasHeader && csvRows.Length > 0)
            {
                string[] headers = csvRows[0].Split(delimiter);
                foreach (string header in headers)
                {
                    // 确认列名不存在才添加
                    if (!dataTable.Columns.Contains(header))
                    {
                        dataTable.Columns.Add(header);
                    }
                    else
                    {
                        // 如果列名重复，则加入"_1"等后缀
                        int i = 1;
                        while (dataTable.Columns.Contains(header + "_" + i))
                        {
                            i++;
                        }

                        dataTable.Columns.Add(header + "_" + i);
                    }
                }
            }

            // 创建数据行
            for (int i = hasHeader ? 1 : 0; i < csvRows.Length; i++)
            {
                string[] fields = csvRows[i].Split(delimiter);
                DataRow dataRow = dataTable.NewRow();
                for (int j = 0; j < fields.Length; j++)
                {
                    if (j < dataTable.Columns.Count)
                    {
                        dataRow[j] = fields[j];
                    }
                }

                dataTable.Rows.Add(dataRow);
            }

            return dataTable;
        }

        public static bool DownLoadStandAloneSupportGameToStreamAssets() //下载本项目支持的独立热更小游戏csv配置表到包体
        {
            string serverUrlRoot = JenkinsEnv.Instance.Get("p_stand_alone_server_root_url", string.Empty);
            //serverUrlRoot = "http://172.18.0.105:8001/CasualGame_Pure_Res_105_002/TinyRes/";
            if (!string.IsNullOrEmpty(serverUrlRoot))
            {
                var updateTarFileUrl = Path.Combine(serverUrlRoot, "supportgame.csv");
                var updateSrcFilePath = Path.Combine(Application.streamingAssetsPath, "AssetBundles", GetManifestName(),
                    "supportgame.csv");
                if (File.Exists(updateSrcFilePath))
                {
                    File.Delete(updateSrcFilePath);
                }

                if (!ToolUti.DownloadFileW(updateTarFileUrl, updateSrcFilePath))
                {
                    Debug.LogError("DownLoadStandAlonesupportgame.csvToStreamAssets failed,");
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                Debug.LogError("error  get p_stand_alone_server_root_url failed,");
                return false;
            }
        }

        public static string GetManifestName()
        {
            var platform =
                War.Base.BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            return "T" + platform;
        }

        /// <summary>
        /// 复制目录的AssetBundle资源到StreamingAsset 安装包内
        /// </summary>
        /// <param name="buildTarget">目标硬件平台</param>
        /// <param name="dir">目标目录</param>
        /// <returns>复制目标绝对路径</returns>
        public static string CopyResToStreaming(BuildTarget buildTarget, string dir)
        {
            var outputFolder = Path.Combine(GetPlatformFolderForAssetBundles(buildTarget), dir);
            var source = Path.Combine(Path.Combine(System.Environment.CurrentDirectory, AssetBundlesOutputPath),
                outputFolder);
            var destination =
                System.IO.Path.Combine(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                    outputFolder);
            if (System.IO.Directory.Exists(destination))
                FileUtil.DeleteFileOrDirectory(destination);
            CopyDirectoryByFilter(source, destination, new string[] { ".manifest", ".DS_Store" });
            return destination;
        }
        public static bool IsDefaultValue(object value)
        {
            if (value == null) return true;

            var type = value.GetType();

            if (type.IsValueType)
            {
                // 产生该值类型的默认实例
                object defaultValue = Activator.CreateInstance(type);
                return value.Equals(defaultValue);
            }
            else if (type == typeof(string))
            {
                return string.IsNullOrEmpty((string)value);
            }

            // 其他引用类型，没法确定默认值，只判null过了
            return false;
        }
        static V AssignValueOrNull<K, V>(Dictionary<K, V> dict, K key, V value)
        {
            // 判断value是否等于默认值
            if (IsDefaultValue(value))
            {
                // 是默认值，返回null（引用类型才可返回null，值类型返回默认）
                "".Print("AssignValueOrNull: value is default, return null",key,value);;
                return default(V);
            }
            else
            {
                dict[key] = value;
                "".Print(key,value.ToString());
                return value;
            }
        }
        
        static bool BuildPlayerAfterRes(BuildTarget buildTarget, string outputPath, string[] levels = null)
        {
            string targetName = GetBuildTargetName(buildTarget);
            if (targetName == null)
                return false;

            if (levels == null)
            {
                levels = GetLevelsFromBuildSettings();
            }

            if (levels.Length == 0)
            {
                Debug.Log("Nothing to build.");
                return false;
            }

            LogHelp.Instance.Log("setCompanyName-start");
            SetCompanyName();
            LogHelp.Instance.Log("setCompanyName-end");
            LogHelp.Instance.Log("videoDesPath-start");

            try
            {
                var videoDesPath = Application.streamingAssetsPath + "/Video/v0.webm";
                EditorHelp.CheckDir(videoDesPath);
                if (File.Exists(videoDesPath))
                {
                    File.Delete(videoDesPath);
                }

                System.IO.File.Copy(Application.dataPath + "/Video/v0.webm", videoDesPath);
            }
            catch (Exception e)
            {
                Debug.Log("error:" + e.ToString());
            }

            LogHelp.Instance.Log("videoDesPath-end");

            //var stream_ab_path = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
            var root_ab_path = Path.Combine(Application.dataPath + "/../", AssetBundlesOutputPath);

            string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);

            // Setup the source folder for assetbundles.
            var root_ab_target_path = Path.Combine(root_ab_path, outputFolder);
            var updatejson_path = root_ab_target_path + "/update.json";
            string file = null;
            string streaming_ab_path;
            streaming_ab_path = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
            var ofolder = GetPlatformFolderForAssetBundles(buildTarget);
            // Setup the source folder for assetbundles.
            var streaming_ab_target_path = Path.Combine(streaming_ab_path, ofolder);
            if (File.Exists(updatejson_path))
            {
                file = File.ReadAllText(updatejson_path);
                if (patch_version > 0)
                {
                    //var updatejson_path = stream_ab_target_path + "/update.json";
                    //var file = File.ReadAllText(updatejson_path);
                    var startind = file.IndexOf("\"version\":");
                    var end = file.IndexOf("\n", startind);
                    var rep_str = file.Substring(startind, end - startind);
                    var min = Mathf.Min(startind, end, end - startind);
                    if (min >= 0)
                    {
                        file = file.Replace(rep_str, string.Format("\"version\":{0}", patch_version));
                        File.WriteAllText(updatejson_path, file);
                        Debug.LogWarning("patch_version:" + file);
                    }
                }

                var updator = (Dictionary<string, object>)MiniJSON.Json.Deserialize(file);
                updator["wholepackage"] =
                    JenkinsEnv.Instance.GetBool("copy_ab_to_streamingassets", false) ? "true" : "false";
                if (updator["wholepackage"].ToString() == "true")
                    updator["wholepackage"] =
                        JenkinsEnv.Instance.GetBool("close_wholepackage", false) ? "false" : "true";
                updator["res_key"] = JenkinsEnv.Instance.Get("res_key", "");
                if (!updator.ContainsKey("p_stand_alone_server_root_url"))
                    updator["p_stand_alone_server_root_url"] =
                        JenkinsEnv.Instance.Get("p_stand_alone_server_root_url", "");
                updator["p_stand_alone_res_key"] = JenkinsEnv.Instance.Get("p_stand_alone_res_key", "");
                updator["collection_res_key"] = JenkinsEnv.Instance.Get("collection_res_key", "");
                var files = MiniJSON.Json.Serialize(updator);
                File.WriteAllText(updatejson_path, files);
                Debug.LogWarning("updatejson_path:" + files);

                var streaming_updatejson_path = streaming_ab_target_path + "/update.json";

                "".Print("Copy update.json", updatejson_path, streaming_updatejson_path);
                File.Copy(updatejson_path, streaming_updatejson_path, true);
            }


            LogHelp.Instance.Log("patch_version-end");

            if (JenkinsEnv.Instance.GetBool("load_local_server_list", true))
                SaveLocalServerData.UpdateLocalServerData();
            LogHelp.Instance.Log("UpdateLocalServerData-end");
            var packageJsonPath = root_ab_target_path + "/package.json";

            var package = new Dictionary<string, object>();
            package["useMiniGameGuide"] = JenkinsEnv.Instance.GetBool("useMiniGameGuide", true);
            package["openMiniGame"] = JenkinsEnv.Instance.GetBool("openMiniGame", true);
            package["openGameLobby"] = JenkinsEnv.Instance.GetBool("openGameLobby", false);
            package["use_specific_updatejson"] = JenkinsEnv.Instance.GetBool("use_specific_updatejson", false);
            package["specific_updatejson"] = JenkinsEnv.Instance.Get("specific_updatejson", "");
            package["table_mark"] = JenkinsEnv.Instance.Get("table_mark", GetTableMarkStr());
            package["bSockPackage"] = JenkinsEnv.Instance.GetBool("bSockPackage", false);
            package["table"] = JenkinsEnv.Instance.Get("mult_casualgame", "");
            package["enableEncryptionNet"] = JenkinsEnv.Instance.GetBool("enableEncryptionNet", true);
            package["enableNewRoleRewards"] = JenkinsEnv.Instance.GetBool("enableNewRoleRewards", false);
            package["enableGarbleAbPath"] = JenkinsEnv.Instance.GetBool("enableGarbleAbPath", false);
            package["currency_type"] = JenkinsEnv.Instance.Get("currency_type", "");
            package["curExternalVersion"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            //此开关注意区分平台，H5不需要使用本地update.json的功能
            AssignValueOrNull(package, "use_local_update_json_new", JenkinsEnv.Instance.GetBool("use_local_update_json_new", false));
            AssignValueOrNull(package, "is_open_old_res_version", JenkinsEnv.Instance.GetBool("is_save_hash_remote_virtual", false));
            AssignValueOrNull(package, "is_save_hash_remote_virtual", JenkinsEnv.Instance.GetBool("is_save_hash_remote_virtual", false));
            package["p_copy_streamingasset_out"] = JenkinsEnv.Instance.GetBool("p_copy_streamingasset_out", true);
            var stripLog = JenkinsEnv.Instance.Get("strip_log", "");
            package["b_sw_print_on"] = stripLog != "1" && stripLog != "2";
            var package_dll_ver = JenkinsEnv.Instance.Get("package_dll_ver", "0");
            package["package_dll_ver"] = package_dll_ver;
            Debug.Log($"package_dll_ver: {package_dll_ver}");

            //pacakgeJson保存包内的小游戏列表,用逗号分割
            // var reskey= JenkinsEnv.Instance.Get("res_key", "");
            // var isCombine = reskey.StartsWith("Combine");
            // if (isCombine)
            // {
            //     var combineMark = CasualGameBuild.Instance.GetCombineMark();
            //     if (combineMark.ContainsKey(reskey))
            //     {
            //         var combineReskeyMark= String.Join(",",combineMark[reskey]);
            //         package["combineReskeyMark"] = combineReskeyMark;
            //         LogHelp.Instance.Log("combineReskeyMark:"+combineReskeyMark);
            //     }
            // }

            package["load_local_server_list"] = bExistLocalServerList.ToString();

            foreach (var configKey in JenkinsEnv.Instance.config.Keys)
            {
                if (configKey.ToLower().StartsWith("p_"))
                {
                    package[configKey] = JenkinsEnv.Instance.Get(configKey);
                }
            }

            try
            {
                var packageSerialize = MiniJSON.Json.Serialize(package);
                File.WriteAllText(packageJsonPath, packageSerialize);
                Debug.Log("package.json path: " + packageJsonPath);
                Debug.Log("package value: " + packageSerialize);

                var streaming_package_path = streaming_ab_target_path + "/package.json";

                File.Copy(packageJsonPath, streaming_package_path, true);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            LogHelp.Instance.Log("package-version-end");

            //try
            //{
            //    var files_path = stream_ab_target_path + "/files.txt";
            //    binaryFilesText(files_path);
            //}
            //catch (Exception e)
            //{
            //    Debug.Log("files.txt => files.txt.bytes error:"+e.ToString());
            //}
            var cacheMode = EditorSettings.spritePackerMode;
            //			PathResolverInjector.Inject ();
            //			string wwiseProjFile = Path.Combine (Application.dataPath, WwiseSetupWizard.Settings.WwiseProjectPath).Replace ('\\', '/');
            //			string wwiseProjectFolder = wwiseProjFile.Remove (wwiseProjFile.LastIndexOf ('/'));
            //			string wwisePlatformString = UnityToWwisePlatformString (buildTarget.ToString ());
            //			string sourceSoundBankFolder = Path.Combine (wwiseProjectFolder, AkBasePathGetter.GetPlatformBasePath ());
            //			//Soundbank must be inside the StreamingAssets folder
            //			string destinationSoundBankFolder = Path.Combine (Application.dataPath + Path.DirectorySeparatorChar + "StreamingAssets", Path.Combine (WwiseSetupWizard.Settings.SoundbankPath, wwisePlatformString));
            //			//Copy the soundbank from the Wwise project to the unity project (Inside the StreamingAssets folder as defined in Window->Wwise Settings)
            //			if (!AkUtilities.DirectoryCopy (sourceSoundBankFolder, destinationSoundBankFolder, true)) {
            //				UnityEngine.Debug.LogError ("WwiseUnity: The soundbank folder for the " + wwisePlatformString + " platform doesn't exist. Make sure it was generated in your Wwise project");
            //			}
            BuildOptions option = EditorUserBuildSettings.development ? BuildOptions.Development : BuildOptions.None;
            UWAGameBuild.Instance.SetUWADefineSymbols();

            debugging = JenkinsEnv.Instance.GetBool("unity_dev", false);
            "".Print("debugging", JenkinsEnv.Instance.Get("debug_unity"), debugging);

            if (debugging)
            {
                //option = BuildOptions.Development | BuildOptions.ConnectWithProfiler | BuildOptions.AllowDebugging;
                option = BuildOptions.Development | BuildOptions.AllowDebugging;
                //option |= BuildOptions.EnableDeepProfilingSupport;
                PlayerSettings.enableInternalProfiler = true;

                EditorUserBuildSettings.development = true;
                EditorUserBuildSettings.connectProfiler = true;
                EditorUserBuildSettings.buildWithDeepProfilingSupport = false;//true;

                // var targetGroup = EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android
                //     ? BuildTargetGroup.Android
                //     : BuildTargetGroup.iOS;
                //Debug.LogWarning("build script with Mono2x");
                //PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.Mono2x);
            }
            else
            {
                option = BuildOptions.None;
                EditorUserBuildSettings.development = false;
                EditorUserBuildSettings.connectProfiler = false;
                // var targetGroup = EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android
                //     ? BuildTargetGroup.Android
                //     : BuildTargetGroup.iOS;
                // Debug.LogWarning("build script with IL2CPP");
                // PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.IL2CPP);
            }


            if (EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android)
            {
                // 海外升级到api31
                var strBuildConfig = File.ReadAllText(build_config);
                var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strBuildConfig);
                var isDomestic = GetBuildConfig(editorBuildConfig, "Q1SDK_DOMESTIC");
                if (isDomestic == "True")
                {
                    PlayerSettings.Android.targetSdkVersion = (AndroidSdkVersions)30;
                }
                else
                {
                    PlayerSettings.Android.targetSdkVersion = (AndroidSdkVersions)35;
                    PlayerSettings.Android.minifyWithR8 = true;
                }
#if UNITY_ANDROID
                //开启原因：海外一些三星手机连接蓝牙设备后切到后台切回前台会发生崩溃，开启后可以解决这个问题
                {
                    PlayerSettings.muteOtherAudioSources = true;
                    Debug.Log("PlayerSettings.muteOtherAudioSources:" + PlayerSettings.muteOtherAudioSources);
                }
#endif

                bool enable = JenkinsEnv.Instance.GetBool("p_EnableHybridCLR", false);
                if (enable)
                {
                    PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Android, ApiCompatibilityLevel.NET_4_6);
                    AssetDatabase.Refresh();

                    var acl = PlayerSettings.GetApiCompatibilityLevel(BuildTargetGroup.Android);
                    Debug.Log($"ApiCompatibilityLevel->{acl}");
                }
            }
#if UNITY_IOS
            PlayerSettings.iOS.targetOSVersionString = "13.0";
#endif

            //是否使用mono打包
            bool backend_mono = JenkinsEnv.Instance.GetBool("backend_mono", false);
            var targetGroup = EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android
                ? BuildTargetGroup.Android
                : BuildTargetGroup.iOS;
            if (backend_mono)
            {
                PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.Mono2x);
                PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7;
            }
            else
            {
                PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.IL2CPP);
                PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
            }

            //EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
            //         if (buildTarget == BuildTarget.Android) {
            //	if (EditorUserBuildSettings.androidBuildSystem == AndroidBuildSystem.ADT) {
            //		option |= BuildOptions.AcceptExternalModificationsToPlayer;
            //		targetName = "";
            //	}
            //}
            LogHelp.Instance.Log("BuildOptions-end");

            bool enableEncrypt = JenkinsEnv.Instance.GetBool("encrypt_open", true);
            if (enableEncrypt)
            {
                if (JenkinsEnv.Instance.GetBool("enableXorABResource", false))
                {
                    // 异或方式混淆 AB 资源
                    XorAbs();
                    Debug.Log("BuildAssetBundles XorAbs");
                }
                else
                {
                    //偏移方式混淆 AB 资源
                    if (CheckChannelInABResource())
                    {
                        OffsetAbs();
                        Debug.Log("BuildAssetBundles OffsetAbs");
                    }
                    else
                    {
                        Debug.Log("BuildAssetBundles encrypt ab is not");
                    }
                }

                LogHelp.Instance.Log("XorAbs-end");
            }

            var path = Application.dataPath + "/Resources/garbleABPath.asset";
            if (File.Exists(path))
            {
                Debug.Log("zzd_______garbleABPath.asset exist   " + path);
                FileUtil.DeleteFileOrDirectory(path);
            }

            Debug.Log($"enableGarbleAbPath:{JenkinsEnv.Instance.GetBool("enableGarbleAbPath", false)}");
            if (JenkinsEnv.Instance.GetBool("enableGarbleAbPath", false))
            {
                GarbleAssetBundlesPath.GarbleAbPath();
                LogHelp.Instance.Log("GarbleAbPath-end");

                string platformFolder = GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
                string abPath = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
                string platformPath = Path.Combine(abPath, platformFolder);

                // 改成配置
                var rk1 = AssetDatabase.LoadAssetAtPath<ABPathKey>("Assets/Resources/garbleABPath.asset");
                if (rk1 != null)
                {
                    // 对json进行二进制转换
                    string updateJson = rk1.Get("update.json");
                    if (string.IsNullOrEmpty(updateJson) == false)
                    {
                        JsonFileEncryptor(Path.Combine(platformPath, updateJson),
                            Common.AlgorithmUtils.HashUtf8MD5(updateJson).Substring(8, 16));
                    }

                    string packageJson = rk1.Get("package.json");
                    if (string.IsNullOrEmpty(updateJson) == false)
                    {
                        JsonFileEncryptor(Path.Combine(platformPath, packageJson),
                            Common.AlgorithmUtils.HashUtf8MD5(packageJson).Substring(8, 16));
                    }

                    string lslJson = rk1.Get("local_server_list.json");
                    if (string.IsNullOrEmpty(lslJson) == false)
                    {
                        JsonFileEncryptor(Path.Combine(Application.streamingAssetsPath, lslJson),
                            Common.AlgorithmUtils.HashUtf8MD5(lslJson).Substring(8, 16));
                    }
                }

                // 安卓下会生成1update.json文件需要删除
                string _1update = Path.Combine(platformPath, "1update.json");
                if (File.Exists(_1update))
                {
                    File.Delete(_1update);
                    Debug.Log("delete file. Path:" + _1update);
                }
                else
                {
                    Debug.Log("not find need delete file. Path:" + _1update);
                }

                DeleteNotUseFileInGarbleModle();
            }

            ManageResSize();
            LogHelp.Instance.Log("ManageResSize-end");

            //GenRubbishCode();

            if (JenkinsEnv.Instance.GetBool("full_package", false))
            {
                //华佗热更同步，目前不需要RemoveTinyGameRes，先屏蔽
                //if (!JenkinsEnv.Instance.GetBool("p_launch", false)) RemoverForTinyGame.RemoveTinyGameResBeforeBuildAPK();
            }


            EditorUserBuildSettings.androidETC2Fallback = AndroidETC2Fallback.Quality32BitDownscaled;
            //PlayerSettings.Android.minSdkVersion =   AndroidSdkVersions.AndroidApiLevel16;
            BuildMinSdkVersion();
            string gradleDirPath = outputPath + "/gradleProject";
            if (EditorUserBuildSettings.exportAsGoogleAndroidProject)
            {
#if UNITY_ANDROID
                option |= BuildOptions.AcceptExternalModificationsToPlayer;
#endif
                //string gradleDirPath = outputPath + "/" + Path.GetFileName(targetName);
                if (!Directory.Exists(gradleDirPath))
                {
                    Directory.CreateDirectory(gradleDirPath);
                }

                Debug.Log("android gradle project path:" + gradleDirPath);

                EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
                var error = BuildPipeline.BuildPlayer(levels, gradleDirPath, buildTarget, option);
#if UNITY_2018_1_OR_NEWER
#if UNITY_2022_1_OR_NEWER
                if (error.GetFiles() == null || error.GetFiles().Length == 0)
#else
                if (error.files == null || error.files.Length == 0)
#endif
                {
                    Debug.LogError("Build player after res error");
                }
#if UNITY_2022_1_OR_NEWER
                Debug.LogWarning("Build player after res files length: " + error.GetFiles().Length);
#else
                Debug.LogWarning("Build player after res files length: " + error.files.Length);
#endif
                if (error.summary.result != UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.LogError("BuildPlayer failed errors:" + error.summary.totalErrors);
                    return false;
                }
#else
                if(!string.IsNullOrEmpty(error))
                {
                    Debug.LogError("BuildPlayer failed:" + error);
                    return false;
                }
#endif
                LogHelp.Instance.Log("BuildPlayer-end");
                EditorSettings.spritePackerMode = cacheMode;
                ModifyAndroidManifest(gradleDirPath);

                AutoCopySymbolsPostprocessor.OnPostprocessBuild(buildTarget, outputPath);
                LogHelp.Instance.Log("OnPostprocessBuild-end");
            }
            else
            {
                EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
                Debug.Log("buildPlayerPath:" + outputPath + targetName);
                var error = BuildPipeline.BuildPlayer(levels, outputPath + targetName, buildTarget, option);
#if UNITY_2018_1_OR_NEWER
#if UNITY_2022_1_OR_NEWER
                if (error.GetFiles() == null || error.GetFiles().Length == 0)
#else
                if (error.files == null || error.files.Length == 0)
#endif
                {
                    Debug.LogError("Build player after res error");
                }
#if UNITY_2022_1_OR_NEWER
                Debug.LogWarning("Build player after res files length: " + error.GetFiles().Length);
#else
                Debug.LogWarning("Build player after res files length: " + error.files.Length);
#endif
#else
                if (!string.IsNullOrEmpty(error))
                {
                    Debug.LogError("BuildPlayer failed:" + error);
                    return false;
                }
#endif
                LogHelp.Instance.Log("BuildPlayer-end");
                EditorSettings.spritePackerMode = cacheMode;
                AutoCopySymbolsPostprocessor.OnPostprocessBuild(buildTarget, outputPath);
                LogHelp.Instance.Log("OnPostprocessBuild-end");
            }

            return true;
        }


        // 去掉默认的主体域名设置，改为由package内容生成。
        // 只有在Jenkins打包设置日志加密选项p_encryptLogFile为true时才生效（防止正常包修改后出问题）
        //[MenuItem("Test/SetCompanyName")]
        public static void SetCompanyName()
        {
            if (!JenkinsEnv.Instance.GetBool("p_encryptLogFile"))
            {
                Debug.Log("don't SetCompanyName because p_encryptLogFile is false");
                return;
            }

            Debug.Log("set companyName start");
            TextAsset textAsset = Resources.Load<TextAsset>("game_config");
            var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
            string channelId = Convert.ToString(gameConfig["CHANNEL_ID"]);
            string channel_tag = Convert.ToString(gameConfig["CHANNEL_TAG"]);
            Debug.LogWarning("SetCompanyName Channel Id:" + channelId);
            string dis_channels_path = Application.dataPath + "/../dis_channels/" + channelId + ".config";
            string package_config_path = Application.dataPath + "../channel_res/" + channel_tag + "/package_config.txt";
            if (File.Exists(package_config_path))
            {
                dis_channels_path = package_config_path;
            }

            if (File.Exists(dis_channels_path))
            {
                string[] lines = File.ReadAllLines(dis_channels_path);
                var package = Array.Find(lines, line => line.Contains("package:"));
                if (!string.IsNullOrEmpty(package))
                {
                    package = package.Replace("package:", "");
                    var ss = package.Split('.');
                    if (ss.Length > 1)
                    {
                        PlayerSettings.companyName = ss[1] + ".com";
                        Debug.Log($"set companyName:{PlayerSettings.companyName}");
                        return;
                    }
                }
            }

            Debug.Log("set companyName failed");
        }

        // 修改androidManifest中 launchActivity launchMode为singleTop
        private static void ModifyAndroidManifest(string gradleDirPath)
        {
            if (!Directory.Exists(gradleDirPath))
            {
                Debug.LogError("ModifyAndroidManifest gradlePath can not find:" + gradleDirPath);
                return;
            }

            string manifestPath = gradleDirPath + "/unityLibrary/src/main/AndroidManifest.xml";
            if (!File.Exists(manifestPath))
            {
                Debug.LogError("ModifyAndroidManifest manifestPath can not find:" + manifestPath);
                return;
            }

            Debug.LogWarning("ModifyAndroidManifest manifestPath: " + manifestPath);
            var mrContent = File.ReadAllLines(manifestPath);
            var mark = "android:name=\"com.q1.ext.LancherActivity\"";
            var ind = new List<string>(mrContent).FindIndex((l) => l.Contains(mark));
            if (ind > -1)
            {
                var newline =
                    "<activity android:name=\"com.q1.ext.LancherActivity\" android:screenOrientation=\"sensorPortrait\" android:launchMode=\"singleTop\" android:configChanges=\"mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density\" android:hardwareAccelerated=\"false\">";

                mrContent[ind] = newline;
                var c = string.Join("\n", mrContent);

                File.WriteAllText(manifestPath, c);

                Debug.LogWarning("ModifyAndroidManifest manifestPath modify: " + manifestPath);
            }
        }

        // 混淆时，删除不需要的文件
        private static void DeleteNotUseFileInGarbleModle()
        {
            string key2excludeJsonPath = Application.streamingAssetsPath + "/key2exclude.json";

            if (File.Exists(key2excludeJsonPath))
            {
                File.Delete(key2excludeJsonPath);
                Debug.Log("delete file. Path:" + key2excludeJsonPath);
            }
            else
            {
                Debug.Log("not find need delete file. Path:" + key2excludeJsonPath);
            }

            string packPath = Application.streamingAssetsPath + "/Pack";
            if (Directory.Exists(packPath))
            {
                Directory.Delete(packPath, true);
                Debug.Log("delete Directory. Path:" + packPath);
            }
            else
            {
                Debug.Log("not find need delete Directory. Path:" + packPath);
            }

            string videoPath = Application.streamingAssetsPath + "/Video";
            if (Directory.Exists(videoPath))
            {
                Directory.Delete(videoPath, true);
                Debug.Log("delete Directory. Path:" + videoPath);
            }
            else
            {
                Debug.Log("not find need delete Directory. Path:" + videoPath);
            }
        }

        // Json文件做AES加密
        private static void JsonFileEncryptor(string filePath, string iv)
        {
            if (File.Exists(filePath))
            {
                try
                {
                    var fileContent = File.ReadAllText(filePath);
                    var bytes = Encoding.Unicode.GetBytes(fileContent);

                    File.WriteAllBytes(filePath, Aes.Encryptor(bytes, AssetBundleManager.UpdateConfigEncryptKey, iv));

                    Debug.Log("changt to binary done. path:" + filePath);
                }
                catch (Exception e)
                {
                    Debug.LogError("Change error:" + e);
                }
            }
            else
            {
                Debug.Log($"not find file: {filePath}");
            }
        }

        private static void ManageResSize()
        {
            var maxSize = JenkinsEnv.Instance.GetInt("ressize_maxsize");
            if (maxSize <= 0)
            {
                return;
            }

            ResSizeManager.maxSize = maxSize;
            ResSizeManager.rootPath = Path.Combine(AssetBundlesOutputPath,
                GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
            //var firstFileLocalPath = JenkinsEnv.Instance.GetInt("resSize_maxSize");
            //ResSizeManager.firstFileLocalPath = firstFileLocalPath;

            ResSizeManager.ResSizeManagerTool();
            AssetDatabase.Refresh();
        }

        private static void GenRubbishCode()
        {
            if (Directory.Exists(RubbishCodeTool.rootPath))
            {
                Directory.Delete(RubbishCodeTool.rootPath, true);
            }

            var rnum = JenkinsEnv.Instance.GetInt("rubbish_num");
            if (rnum > 0)
            {
                RubbishCodeTool.minFileCount = rnum;
                RubbishCodeTool.maxFileCount = rnum;
                RubbishCodeTool.StartCreateRubiishCode();
                AssetDatabase.Refresh();
            }
            else
            {
                Debug.LogWarning("GenRubbishCode false");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="abDataCfg"></param>
        /// <param name="channelTag"></param>
        /// <returns>1: 参数配置错误, 2: 数据类型不支持</returns>
        private static object ParseOffsetCfg(editor.tools.asset.DataSetAsset abDataCfg, string channelTag,
            string filedName, object defaultValue, string ab_config_path, out int result, RuntimePlatform platform)
        {
            result = 0;
            object parsedValue = defaultValue;

            var filedValue =
                abDataCfg.Get(
                    new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = channelTag },
                    filedName, platform);
            if (string.IsNullOrEmpty(filedValue))
            {
                Debug.LogError(
                    $"Enable mix asset bundle resource, {filedName} can not be empty. Please add {channelTag} config to {ab_config_path}");
                result = 1;
                return parsedValue;
            }

            Debug.Log($"{filedName}:{filedValue}");

            if (parsedValue is int)
            {
                if (!int.TryParse(filedValue, out int fileValue))
                {
                    Debug.LogError($"Enable mix asset bundle resource, {filedName} is not a valid int");
                    result = 1;
                }
                else
                {
                    parsedValue = fileValue;
                }
            }
            else if (parsedValue is ulong)
            {
                if (!ulong.TryParse(filedValue, out ulong fileValue))
                {
                    Debug.LogError($"Enable mix asset bundle resource, {filedName} is not a valid ulong");
                    result = 1;
                }
                else
                {
                    parsedValue = fileValue;
                }
            }
            else if (parsedValue is string)
            {
                parsedValue = filedValue;
            }
            else
            {
                result = 2;
                Debug.LogError($"ParseOffsetCfg, 不支持解析 {filedName} 数据类型");
            }

            return parsedValue;
        }

        // 创建包含随机字节值的字节数组
        private static byte[] GenerateOffsetBuffer(int length)
        {
            byte[] buffer = new byte[length];
            // uses the system clock to generate its seed value
            System.Random rand = new System.Random();
            rand.NextBytes(buffer);
            return buffer;
        }

        public static bool CheckChannelInABResource()
        {
            //检测当前包名是否存在于ab_resource_config.assets文件中
            bool isExist = false;
            var abOffsetCfg = ABOffset.ParseOffsetConfig();
            if (abOffsetCfg != null)
            {
                Debug.LogError("abOffsetCfg is not empty");
                isExist = true;
            }

            return isExist;
        }

        public class ABOffset
        {
            public int baseOffset;
            public int nameMod;

            public static ABOffset ParseOffsetConfig()
            {
                var strBuildConfig = File.ReadAllText(build_config);
                var buildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strBuildConfig);
                var channelTag = GetBuildConfig(buildConfig, "CHANNEL_TAG");
                if (string.IsNullOrEmpty(channelTag))
                {
                    Debug.LogError($"Can not find CHANNEL_TAG in {build_config}");
                    return null;
                }

                BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
                RuntimePlatform platform = RuntimePlatform.IPhonePlayer;
                if (buildTarget == BuildTarget.Android)
                {
                    platform = RuntimePlatform.Android;
                }

                var ab_config_path = "Assets/Scripts/Editor/AssetTools/ab_resource_config.asset";
                var abDataCfg = AssetDatabase.LoadAssetAtPath<editor.tools.asset.DataSetAsset>(ab_config_path);
                var baseOffset = ParseOffsetCfg(abDataCfg, channelTag, "abBaseOffset", 0, ab_config_path,
                    out int abParseResult, platform);
                if (abParseResult != 0)
                {
                    return null;
                }

                var nameMod = ParseOffsetCfg(abDataCfg, channelTag, "abNameMod", 0, ab_config_path, out abParseResult,
                    platform);
                if (abParseResult != 0)
                {
                    return null;
                }

                return new ABOffset()
                {
                    baseOffset = (int)baseOffset,
                    nameMod = (int)nameMod
                };
            }
        }

        [MenuItem("AssetBundles/Encrypt/OffsetAbs")]
        private static void TestOffsetAbs()
        {
            OffsetAbs();
        }


        [MenuItem("AssetBundles/Encrypt/OffsetAbs")]
        public static void OffsetAbs(string targetAssetBundlsPath = null)
        {
            try
            {
                var abOffsetCfg = ABOffset.ParseOffsetConfig();
                if (abOffsetCfg == null)
                {
                    Debug.LogError("abOffsetCfg is empty");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var path = "Assets/Resources/bconfig.asset";
                if (!File.Exists(path))
                {
                    Debug.LogError($"Can not find file:{path}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var keyCollection = AssetDatabase.LoadAssetAtPath<ResKey>(path);
                if (!keyCollection)
                {
                    Debug.LogError($"Can not load file:{path}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var sw = new System.Diagnostics.Stopwatch();
                sw.Start();

                AssetDatabase.StartAssetEditing();

                ResKeyTool.AddKey(keyCollection, "abBaseOffset", abOffsetCfg.baseOffset.ToString());
                ResKeyTool.AddKey(keyCollection, "abNameMod", abOffsetCfg.nameMod.ToString());

                int totalOffsetLen = abOffsetCfg.baseOffset + abOffsetCfg.nameMod;
                System.Random rand = new System.Random();
                var randomBytes = GenerateOffsetBuffer(totalOffsetLen);

                var destinationAssetPath = targetAssetBundlsPath;
                if (string.IsNullOrEmpty(targetAssetBundlsPath))
                {
                    BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
                    string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);

                    // Setup the destination folder for assetbundles.
                    var platformStreamingAssetPath =
                        Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
                    destinationAssetPath = Path.Combine(platformStreamingAssetPath, outputFolder);
                }

                var filesPath = Path.Combine(destinationAssetPath, "files.txt");
                Debug.Log($"destination file Path:{filesPath}");

                if (!File.Exists(filesPath))
                {
                    Debug.LogError($"Can not find {filesPath}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var json = File.ReadAllText(filesPath);
                var hc = hashCheck.Parse(json);
                if (hc == null)
                {
                    Debug.LogError($"Parse {filesPath} failed, invalid json file.");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                float abCount = hc.list.Keys.Count;
                var modifiedCount = 0;
                AssetbundlesCrcManager.setEncryptSets();

                Action<string> progress = (abPath) =>
                {
                    modifiedCount++;
                    EditorUtility.DisplayProgressBar("Offset assetbundle", abPath, modifiedCount / abCount);
                };
                // abNamePath 按路径声明
                foreach (var abRelativePath in hc.list.Keys)
                {
                    OffsetFile(abOffsetCfg, rand, randomBytes, destinationAssetPath, abRelativePath, hc, progress);
                }

                sw.Stop();
                Debug.LogWarning("OffsetAbs cost:" + sw.ElapsedMilliseconds + " ms,num:" + abCount);

                AssetDatabase.StopAssetEditing();

                EditorUtility.SetDirty(keyCollection);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            catch (Exception e)
            {
                Debug.LogError("OffsetAbs:" + e);
#if (!DEBUG_AB_OFFSET)
                EditorApplication.Exit(1);
#endif
                return;
            }
        }

        private static bool OffsetFile(ABOffset abOffsetCfg, System.Random rand, byte[] randomBytes,
            string destinationAssetPath, string abRelativePath, hashCheck hc, Action<string> onProgressPercentChanged)
        {
            // [] 操作符重载，取 md5
            var md5 = hc[abRelativePath];
            var fullPath = destinationAssetPath + "/" + abRelativePath;
            // abRelativePath 是文件名( luascript.zip )，而不是 ab 名，（e.g. luascript），不适用 AssetBundleManager.IsEncryptType 的 EndWith 匹配规则
            //bool bEnc = AssetBundleManager.IsEncryptType(abRelativePath);

            var bResult = true;
            bool bEnc = AssetbundlesCrcManager.isEncryptType(abRelativePath);
            if (bEnc)
            {
                // 加密文件，如 luascript ，仍然走 XOR 方式，运行时先走 EncryptResourceWorker 解混淆，再解密
                var m_intBitX = Common.ResKey.Instance.GetInt("worker");

                try
                {
                    if (m_intBitX == 0)
                    {
                        bResult = false;
                    }
                    else
                    {
                        bResult = XorFile(m_intBitX, destinationAssetPath, hc, abRelativePath);
                    }

                    onProgressPercentChanged(abRelativePath);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex);
                }

                return bResult;
            }

            // 偏移方式混淆资源
            try
            {
                var cmd5 = File2MD5(fullPath);
                if (md5 == cmd5)
                {
                    //Debug.Log($"Offset {fullPath}");
                    var content = File.ReadAllBytes(fullPath);
                    var assetSize = content.Length;
                    var offset =
                        AssetBundleManager.GetABOffset(abOffsetCfg.baseOffset, abOffsetCfg.nameMod, abRelativePath);

                    int filelen = offset * 2 + assetSize;
                    byte[] buffer = new byte[filelen];
                    // 在文件头添加随机数据
                    rand.NextBytes(randomBytes);
                    Array.Copy(randomBytes, buffer, offset);
                    // 拷贝文件数据
                    Array.Copy(content, 0, buffer, offset, assetSize);
                    // 在文件尾添加随机数据
                    rand.NextBytes(randomBytes);
                    Array.Copy(randomBytes, 0, buffer, filelen - offset, offset);

                    File.WriteAllBytes(fullPath, buffer);
                    bResult = true;
                }
                else
                {
                    Debug.LogError(string.Format("CMP diff:{0} | {1}| {2}", abRelativePath, md5, cmd5));
                    bResult = false;
                }

                onProgressPercentChanged(abRelativePath);
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
            }

            return bResult;
        }

        /// <summary>
        /// 测试加载偏移后的 AB 资源
        /// </summary>
        [NUnit.Framework.Test]
        public static void TestLoadOffsetAB()
        {
            var abOffsetCfg = ABOffset.ParseOffsetConfig();
            if (abOffsetCfg == null)
            {
                return;
            }

            BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
            string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);
            // Setup the destination folder for assetbundles.
            var platformStreamingAssetPath = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
            var destinationAssetPath = Path.Combine(platformStreamingAssetPath, outputFolder);
            var testABPath = Path.Combine(destinationAssetPath, "exec0.asset");
            var offset = AssetBundleManager.GetABOffset(abOffsetCfg.baseOffset, abOffsetCfg.nameMod, "exec0.asset");

            var content = File.ReadAllBytes(testABPath);
            byte[] buffer = new byte[content.Length - offset];
            Array.Copy(content, offset, buffer, 0, content.Length - offset);
            //还原后的 atlas.unmix 文件应与原 atlas 文件完全一致
            File.WriteAllBytes(testABPath + ".unmix", buffer);

            //var ab = AssetBundle.LoadFromMemory(buffer);

            // ab 应能加载成功
            var ab = AssetBundle.LoadFromFile(testABPath, 0, (ulong)offset);
            ab.Unload(true);
            ab = null;
            Resources.UnloadUnusedAssets();
        }

        [NUnit.Framework.Test]
        public static void TestOffsetABOnGradleProject()
        {
            var targetAssetBundlsPath =
                "G:/gradleProjects/zHero_android_MobHeroes/gradleProject/unityLibrary/src/main/assets/AssetBundles/Android/";
            OffsetAbs(targetAssetBundlsPath);
        }

        [NUnit.Framework.Test]
        public static void TestLoadOffsetABFromeGradleProject()
        {
            var targetAssetBundlsPath =
                "G:/gradleProjects/zHero_android_MobHeroes/gradleProject/unityLibrary/src/main/assets/AssetBundles/Android/";

            var abOffsetCfg = ABOffset.ParseOffsetConfig();
            if (abOffsetCfg == null)
            {
                return;
            }

            var destinationAssetPath = targetAssetBundlsPath;
            var testABPath = Path.Combine(destinationAssetPath, "exec0.asset");
            var offset = AssetBundleManager.GetABOffset(abOffsetCfg.baseOffset, abOffsetCfg.nameMod, "exec0.asset");

            var content = File.ReadAllBytes(testABPath);
            byte[] buffer = new byte[content.Length - offset];
            Array.Copy(content, offset, buffer, 0, content.Length - offset);
            //还原后的 atlas.unmix 文件应与原 atlas 文件完全一致
            File.WriteAllBytes(testABPath + ".unmix", buffer);

            //var ab = AssetBundle.LoadFromMemory(buffer);

            // ab 应能加载成功
            var ab = AssetBundle.LoadFromFile(testABPath, 0, (ulong)offset);
            ab.Unload(true);
            ab = null;
            Resources.UnloadUnusedAssets();
        }

        [NUnit.Framework.Test]
        public static void TestXORABOnGradleProject()
        {
            var targetAssetBundlsPath =
                "G:/gradleProjects/zHero_android_MobHeroes/gradleProject/unityLibrary/src/main/assets/AssetBundles/Android/";
            XorAbs(targetAssetBundlsPath);
        }

        [NUnit.Framework.Test]
        public static void TestEncryptAB()
        {
            try
            {
                EncryptAB();
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public static void TestAssetLoadCfgLoad()
        {
            AssetLoadCfg.LoadChannelCfg.LoadConfig((channelCfg) =>
            {
                var workparam = AssetLoadCfg.LoadChannelCfg.TryGetResKeyInt("worker");
                var abBaseOffset = AssetLoadCfg.LoadChannelCfg.TryGetResKeyInt("abBaseOffset");
                var abNameMod = AssetLoadCfg.LoadChannelCfg.TryGetResKeyInt("abNameMod");
                Debug.LogWarning($"worker:{workparam},baseOffset:{abBaseOffset},abNameMode:{abNameMod}");
                abNameMod = 1;
            });
        }

        [MenuItem("Assets/UI/XorAbs")]
        /// 加密包内 ab 资源
        private static void XorAbs(string targetAssetBundlsPath = null)
        {
            Debug.LogError("Error!!!Do not use Xor!!!!!!!!!!,检查bconfig.asset");
            EditorApplication.Exit(1);
            //因为异或性能问题,所以不执行使用异或混淆
            return;
            try
            {
                var m_intBitX = Common.ResKey.Instance.GetInt("worker");
                if (m_intBitX == 0) return;

                BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
                // if (buildTarget != BuildTarget.Android) return;
                Debug.Log(string.Format("XorAbs:{0}", m_intBitX));

                var destination = targetAssetBundlsPath;
                if (string.IsNullOrEmpty(targetAssetBundlsPath))
                {
                    string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);
                    // Setup the destination folder for assetbundles.
                    var androiddatapath = Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath);
                    destination = System.IO.Path.Combine(androiddatapath, outputFolder);
                }

                var fileTxt = Directory.GetFiles(destination, "files.txt", SearchOption.TopDirectoryOnly);
                if (fileTxt.Length > 0)
                {
                    var filePath = fileTxt[0];
                    var json = File.ReadAllText(filePath);
                    var hc = hashCheck.Parse(json);
                    foreach (var f in hc.list.Keys)
                    {
                        XorFile(m_intBitX, destination, hc, f);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError("xorabs:" + e.ToString());
            }
        }

        private static bool XorFile(int m_intBitX, string abRootFolder, hashCheck hc, string abPath)
        {
            Debug.LogError("Error!!!Do not use Xor!!!!!!!!!!,检查bconfig.asset");
            EditorApplication.Exit(1);
            //因为异或性能问题,所以不执行使用异或混淆
            return false;
            var md5 = hc[abPath];
            var abFilePath = abRootFolder + "/" + abPath;
            var cmd5 = File2MD5(abFilePath);
            //Debug.Log(string.Format("CMP:{0} | {1}| {2}", f, md5, cmd5));
            if (md5 == cmd5)
            {
                Debug.Log($"XOR:{abFilePath}");
                var content = File.ReadAllBytes(abFilePath);

                var assetSize = content.Length;

                for (int i = 0; i < assetSize; i += m_intBitX)
                {
                    content[i] ^= 0xFF;
                }

                File.WriteAllBytes(abFilePath, content);
                return true;
            }
            else
            {
                Debug.LogError(string.Format("CMP diff:{0} | {1}| {2}", abPath, md5, cmd5));
                return false;
            }
        }

        public static void EncryptAB()
        {
            Debug.LogWarning("Encrypt AssetBundles");

            var enableMixABResource = JenkinsEnv.Instance.GetBool("enableMixABResource");
            var gradle_project_path = JenkinsEnv.Instance.Get("platform_gradle_project_path");

            Debug.LogWarning(
                $"Encrypt, enableMixABResource:{enableMixABResource}, gradle_project_path:{gradle_project_path}");
            if (string.IsNullOrEmpty(gradle_project_path))
            {
                Debug.LogError(
                    $"Encrypt, the gradle project path parameter [gradle_project_path] in jenkins job is empty, can not encrypt assetbundles");
#if (!DEBUG_AB_OFFSET)
                EditorApplication.Exit(1);
#endif
                return;
            }

            var mainAbPath = $"{gradle_project_path}/gradleProject/unityLibrary/src/main/assets/AssetBundles/";
            //string installAbPath = null;

            Debug.LogWarning($"Encrypt, the main assets path:{mainAbPath}");
            if (!Directory.Exists(mainAbPath))
            {
                Debug.LogError($"Encrpty, can not find gradle project assetbundles path:{mainAbPath}");
#if (!DEBUG_AB_OFFSET)
                EditorApplication.Exit(1);
#endif
                return;
            }

            //            var build_aab_asset_pack = JenkinsEnv.Instance.GetBool("build_aab_asset_pack");
            //            if (build_aab_asset_pack)
            //            {
            //                Debug.LogWarning($"Encrypt, build_aab_asset_pack:{build_aab_asset_pack}, use Play Asset Delivery intall-time pack");

            //                installAbPath = $"{gradle_project_path}/gradleProject/install_time_pack/src/main/assets/AssetBundles/";
            //                Debug.LogWarning($"Encrypt, the install assets path:{installAbPath}");
            //                if (!Directory.Exists(installAbPath))
            //                {
            //                    Debug.LogError($"Encrpty, can not find gradle project assetbundles path:{installAbPath}");
            //#if (!DEBUG_AB_OFFSET)
            //                    EditorApplication.Exit(1);
            //#endif
            //                    return;
            //                }
            //            }

            ABOffset abOffsetCfg = null;
            if (enableMixABResource)
            {
                abOffsetCfg = ABOffset.ParseOffsetConfig();
                if (abOffsetCfg == null)
                {
                    Debug.LogError("abOffsetCfg is empty");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var platformMainAbPath = $"{mainAbPath}/Android";
                if (Directory.Exists(platformMainAbPath))
                {
                    //若不存在，则是被拆分成 install_time_pack 包
                    OffsetAbs(platformMainAbPath);
                }

                //if(build_aab_asset_pack)
                //{
                //    OffsetAbs($"{installAbPath}/Android");
                //}
            }

            var channelJsonPath = gradle_project_path + "/config/channel.json";
            var channelContent = File.ReadAllText(channelJsonPath);
            var channelDic = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(channelContent);

            if (enableMixABResource)
            {
                channelDic["abBaseOffset"] = abOffsetCfg.baseOffset;
                channelDic["abNameMod"] = abOffsetCfg.nameMod;
            }
            else
            {
                // 从 ab_path_key.json 获取 worker xor 配置
                var abPathKeyPath = gradle_project_path + "/config/ab_path_key.json";
                var abPathKeyContent = File.ReadAllText(abPathKeyPath);
                var abPathKeyDic =
                    Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(abPathKeyContent);

                if (!abPathKeyDic.TryGetValue("worker", out object workerObj))
                {
                    Debug.LogError($"Encrpty, can not find [worker] key in :{channelJsonPath}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                Debug.LogWarning($"workker type:{workerObj.GetType()}");
                var worker = (int)(long)workerObj;

                // 更新本地 worker 配置
                var path = "Assets/Resources/bconfig.asset";
                if (!File.Exists(path))
                {
                    Debug.LogError($"Can not find file:{path}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                var keyCollection = AssetDatabase.LoadAssetAtPath<ResKey>(path);
                if (!keyCollection)
                {
                    Debug.LogError($"Can not load file:{path}");
#if (!DEBUG_AB_OFFSET)
                    EditorApplication.Exit(1);
#endif
                    return;
                }

                ResKeyTool.AddKey(keyCollection, "worker", worker.ToString());

                // 对资源加密
                XorAbs(mainAbPath);

                //if (build_aab_asset_pack)
                //{
                //    XorAbs(installAbPath);
                //}

                channelDic["worker"] = worker;
            }

            // 保存 channel.json 加密配置
            var strChannel =
                Newtonsoft.Json.JsonConvert.SerializeObject(channelDic, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(channelJsonPath, strChannel);
        }

        [MenuItem("AssetBundles/TestDiffStr")]
        private static void TestDiffStr()
        {
            var o1 = "";
            var o2 = "";
            GetDiffStr(
                "http://***********:8001/rogue_t5_trunk/Android/update.json",
                "http://183.60.125.147:20026/rogue_t5_trunk/Android/update.json", out o1, out o2);

            Debug.Log(o1);
            Debug.Log(o2);
        }

        private static void GetDiffStr(string v1, string v2, out string o1, out string o2)
        {
            var len = Mathf.Min(v1.Length, v2.Length);
            var start = 0;
            var end = 0;

            for (int i = 0; i < len; i++)
            {
                if (v1[i] == v2[i]) continue;
                start = i;
                break;
            }

            for (int i = 1; i <= len; i++)
            {
                if (v1[v1.Length - i] == v2[v2.Length - i]) continue;
                end = i;
                break;
            }

            o1 = v1.Substring(start, v1.Length - end - start + 1);
            o2 = v2.Substring(start, v2.Length - end - start + 1);

            //Debug.Log(start);
            //Debug.Log(end);
            //Debug.Log(len);
        }

        private static void CopyUpdate_1(string path)
        {
            try
            {
                var update_str = File.ReadAllText(path);
                var dic = (Dictionary<string, object>)MiniJSON.Json.Deserialize(update_str);
                if (dic.ContainsKey("update_urls"))
                {
                    var updateurls = (List<object>)dic["update_urls"];
                    var v1 = updateurls[0].ToString().Replace("update.json", "").Replace("update_1.json", "");
                    var v2 = updateurls[1].ToString().Replace("update.json", "").Replace("update_1.json", "");

                    if (v1 == v2)
                    {
                        // 防止 GetDiffStr 中 end < start
                        File.WriteAllText(path.Replace("update.json", "update_1.json"), update_str);
                        return;
                    }

                    var o1 = "";
                    var o2 = "";
                    GetDiffStr(v1, v2, out o1, out o2);

                    var new_update_str = update_str.Replace(o1, o2);
                    var new_dic = (Dictionary<string, object>)MiniJSON.Json.Deserialize(new_update_str);
                    new_dic["update_urls"] = updateurls;
                    var final_update_str = UIHelper.ToJson(new_dic);

                    File.WriteAllText(path.Replace("update.json", "update_1.json"), final_update_str);
                }
            }
            catch (Exception e)
            {
                Debug.Log("CopyUpdate_1 error:" + e.ToString());
            }
        }

        private static void BinaryFilesTexts(string folder)
        {
            var files = Directory.GetFiles(folder, "files.txt", SearchOption.AllDirectories);
            foreach (var f in files)
            {
                if (File.Exists(f.Append(".bytes"))) continue;
                binaryFilesText(f);
            }
        }

        private static void binaryFilesText(string files_path)
        {
            var filesStr = File.ReadAllText(files_path);
            var hc = hashCheck.Parse(filesStr);
            var bs = hc.ToBytes();
            File.WriteAllBytes(files_path + ".bytes", bs);
        }
#if UNITY_2017_4_OR_NEWER
        const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSX;
#else
        const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSXUniversal;
#endif
        public static string GetBuildTargetName(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return string.Format("/{0}_{1}.apk", PlayerSettings.productName.Replace(' ', '_'),
                        Application.version);
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return string.Format("/{0}_{1}.exe", PlayerSettings.productName.Replace(' ', '_'),
                        Application.version);
                case BuildTarget.iOS:
                    return "";
                case BuildTarget.StandaloneOSXIntel:
                case BuildTarget.StandaloneOSXIntel64:
                case StandaloneOSXUniversal:
                    return string.Format("/{0}_{1}.app", PlayerSettings.productName.Replace(' ', '_'),
                        Application.version);
                // Add more build targets for your own.
                default:
                    Debug.Log("Target not implemented.");
                    return null;
            }
        }

        public static int patch_version = 0;

        static int version_of_check_lua_patch
        {
            get { return JenkinsEnv.Instance.GetInt("version_of_check_lua_patch", 0); }
        }

        static void CopyAssetBundlesTo(string outputPath, BuildTarget buildTarget)
        {
            // Clear streaming assets folder.
            FileUtil.DeleteFileOrDirectory(Application.streamingAssetsPath);
            Directory.CreateDirectory(outputPath);

            //EditorHelp.CheckDir(Application.streamingAssetsPath + "/Video/v0.webm");
            //System.IO.File.Copy(Application.dataPath + "/Video/v0.webm", Application.streamingAssetsPath + "/Video/v0.webm",true);

            System.IO.File.Copy(Application.dataPath + "/UI/CommonBg/updatehero1.png",
                Application.streamingAssetsPath + "/updatehero1.png", true);
            // System.IO.File.Copy(Application.dataPath + "/CasualGame/collectionMark.json",
            //     Application.streamingAssetsPath + "/collectionMark.json", true);
            if (buildTarget == BuildTarget.Android)
            {
                System.IO.File.Copy(Application.dataPath + "/api_router_def.json",
                    Application.streamingAssetsPath + "/api_router_def.json", true);
            }

            string outputFolder = GetPlatformFolderForAssetBundles(buildTarget);

            // Setup the source folder for assetbundles.
            var source = Path.Combine(Path.Combine(System.Environment.CurrentDirectory, AssetBundlesOutputPath),
                outputFolder);
            if (!System.IO.Directory.Exists(source))
                Debug.Log("No assetBundle output folder, try to build the assetBundles first.");

            // Setup the destination folder for assetbundles.
            var destination = System.IO.Path.Combine(outputPath, outputFolder);
            if (System.IO.Directory.Exists(destination))
                FileUtil.DeleteFileOrDirectory(destination);

            //FileUtil.CopyFileOrDirectory (source, destination);
            CopyDirectoryByFilter(source, destination, new string[] { ".manifest", ".DS_Store" });
            LogHelp.Instance.Log("source:" + source + ",destination:" + destination);
            if (buildTarget == BuildTarget.Android)
            {
                if (File.Exists(Application.dataPath + "/channel.json"))
                {
                    System.IO.File.Copy(Application.dataPath + "/channel.json",
                        Path.Combine(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                            GetPlatformFolderForAssetBundles(buildTarget)) + "/channel.json", true);
                    LogHelp.Instance.Log("abPath:\n" +
                                         Path.Combine(
                                             Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath),
                                             GetPlatformFolderForAssetBundles(buildTarget)));
                }
            }
            else if (buildTarget == BuildTarget.iOS)
            {
                //目前安卓平板没有用updateheroWidth.png，只有IOS ipad会用宽屏updateheroWidth.png
                if (File.Exists(Application.dataPath + "/UI/CommonBg/updateheroWidth.png"))
                {
                    System.IO.File.Copy(Application.dataPath + "/UI/CommonBg/updateheroWidth.png",
                        Application.streamingAssetsPath + "/updateheroWidth.png", true);
                    LogHelp.Instance.Log("拷贝updateheroWidth:dataPath:" + Application.dataPath +
                                         "/UI/CommonBg/updateheroWidth.png" + ",streamingAssetsPath:" +
                                         Application.streamingAssetsPath + "/updateheroWidth.png");
                }
            }
        }

        static void CopyDirectoryByFilter(string sourcePath, string toSource, string[] filter)
        {
            if (!Directory.Exists(sourcePath))
                return;
            if (!Directory.Exists(toSource))
                Directory.CreateDirectory(toSource);
            sourcePath = sourcePath.EndsWith(@"/") ? sourcePath : sourcePath + @"/";
            toSource = toSource.EndsWith(@"/") ? toSource : toSource + @"/";
            foreach (string fls in Directory.GetFiles(sourcePath))
            {
                FileInfo flinfo = new FileInfo(fls);
                bool isFilter = false;
                for (int i = 0; i < filter.Length; ++i)
                {
                    if (flinfo.Name.EndsWith(filter[i]))
                    {
                        isFilter = true;
                    }
                }

                if (!isFilter)
                    flinfo.CopyTo(toSource + flinfo.Name);
            }

            foreach (string drs in Directory.GetDirectories(sourcePath))
            {
                DirectoryInfo drinfo = new DirectoryInfo(drs);
                CopyDirectoryByFilter(drs, toSource + drinfo.Name, filter);
            }
        }

        static string[] GetLevelsFromBuildSettings()
        {
            List<string> levels = new List<string>();
            for (int i = 0; i < EditorBuildSettings.scenes.Length; ++i)
            {
                if (EditorBuildSettings.scenes[i].enabled)
                    levels.Add(EditorBuildSettings.scenes[i].path);
            }

            return levels.ToArray();
        }

        public static string GetPlatformFolderForAssetBundles(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return "Android";
                case BuildTarget.iOS:
                    return "iOS";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return "Windows";
                case BuildTarget.StandaloneOSXIntel:
                case BuildTarget.StandaloneOSXIntel64:
                case StandaloneOSXUniversal:
                    return "OSX";
                // Add more build targets for your own.
                // If you add more targets, don't forget to add the same platforms to GetPlatformFolderForAssetBundles(RuntimePlatform) function.
                default:
                    return null;
            }
        }

        private static string UnityToWwisePlatformString(string unityPlatormString)
        {
            if (unityPlatormString == BuildTarget.StandaloneWindows.ToString()
                ||
                unityPlatormString == BuildTarget.StandaloneWindows64.ToString())
                return "Windows";
            else if (unityPlatormString == BuildTarget.StandaloneOSXIntel.ToString()
                     ||
                     unityPlatormString == BuildTarget.StandaloneOSXIntel64.ToString()
                     ||
                     unityPlatormString == StandaloneOSXUniversal.ToString())
                return "Mac";
            else if (unityPlatormString == BuildTarget.iOS.ToString())
                return "iOS";

            return unityPlatormString;
        }


        [MenuItem("AssetBundles/BuildPlayerNoABforUpdate")]
        public static void BuildPlayerNoABforUpdate()
        {
            LogHelp.Instance.Log("BuildPlayerNoABUpdate_start");
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(AssetBundlesOutputPath, platform);
            string[] levels = new string[]
            {
                "Assets/Scene/update.unity",
                "Assets/Scene/start.unity",
            };
            if (levels.Length == 0)
            {
                Debug.Log("Nothing to build.");
                return;
            }

            var apkPath = "apk/";
            EditorHelp.CheckDir(apkPath);

            CopyAssetBundlesTo(Path.Combine(Application.streamingAssetsPath, AssetBundlesOutputPath), buildTarget);

            BuildPlayerAfterRes(buildTarget, apkPath, levels);

            LogHelp.Instance.Log("BuildPlayerNoABUpdate_end");
        }

        static void CacheCurrentUpdateInfo()
        {
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(AssetBundlesOutputPath, platform);
            var update_info_path = outputPath + "/" + "update.json";
            var update_info_text = File.ReadAllText(update_info_path);
            //			var update_info = (Dictionary<string, object>)MiniJSON.Json.Deserialize(update_info_text)(update_info_text);
            var updatejson = AssetBundlesOutputPath + "/update.json";
            EditorHelp.CheckDir(updatejson);
            File.WriteAllText(updatejson, update_info_text);
            Debug.Log("saved:" + updatejson);
        }

        static void CopyUpdateInfo()
        {
            return;
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(AssetBundlesOutputPath, platform);
            var update_info_path = outputPath + "/" + "update.json";
            var update_info_text = File.ReadAllText(update_info_path);

            string outputPath2 = Path.Combine(Application.streamingAssetsPath, platform);
            var update_info_path2 = outputPath2 + "/" + "update.json";

            EditorHelp.CheckDir(update_info_path2);
            File.WriteAllText(update_info_path2, update_info_text);
            Debug.Log("copy update.json:" + update_info_path2);
        }

        [MenuItem("AssetBundles/BuildPlayerWithCacheAB")]
        public static void BuildPlayerWithCacheAB()
        {
            LogHelp.Instance.Log("BuildPlayerNoAB_start");
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;

            var apkPath = "apk/";
            EditorHelp.CheckDir(apkPath);

            BuildPlayer(buildTarget, apkPath, false);

            LogHelp.Instance.Log("BuildPlayerNoAB_end");
        }

        static void CopyABFromAbPath(BuildTarget buildTarget)
        {
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(AssetBundlesOutputPath, platform);
            if (Directory.Exists(outputPath) == false)
            {
                return;
            }

            var streamTarget = Application.streamingAssetsPath + "/" + outputPath;
            if (Directory.Exists(streamTarget))
            {
                var fs = Directory.GetFiles(streamTarget, "*.meta", SearchOption.AllDirectories);
                var count = fs.Length;
                for (int i = 0; i < count; i++)
                {
                    var f = fs[i];
                    bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildPlayerNoAB..cache->meta", f,
                        (float)i / (float)count);
                    if (isCancel)
                    {
                        break;
                    }

                    var target = f.Replace(streamTarget, outputPath);
                    EditorHelp.CheckDir(target);
                    EditorHelp.CopyFile(f, target, true, false);
                }

                EditorUtility.ClearProgressBar();
                Directory.Delete(streamTarget, true);
            }

            var files = Directory.GetFiles(outputPath, "*", SearchOption.AllDirectories);
            var count2 = files.Length;
            for (int i = 0; i < count2; i++)
            {
                var f = files[i];
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildPlayerNoAB..move->streamingAssetsPath",
                    f, (float)i / (float)count2);
                if (isCancel)
                {
                    break;
                }

                var target = f.Replace(outputPath, streamTarget);
                EditorHelp.CheckDir(target);
                EditorHelp.CopyFile(f, target, true, false);
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
        }

        [MenuItem("AssetBundles/BuildAssetBundleNoAssets &1")]
        public static void BuildAssetBundleNoAssets()
        {
            var cacheMode = EditorSettings.spritePackerMode;

            var cacheFiles = new List<string>()
            {
                "Android",
                "Android.manifest",
            };
            string outputPath = Path.Combine(AssetBundlesOutputPath,
                GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));

            var backupPath = outputPath + "/backup/";
            EditorHelp.CheckDir(backupPath);
            var count = cacheFiles.Count;
            for (int i = 0; i < count; i++)
            {
                var item = cacheFiles[i];
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildAssetBundleNoAssets..cache", item,
                    (float)i / (float)count);
                if (isCancel)
                {
                    break;
                }

                EditorHelp.CopyFile(outputPath + "/" + item, backupPath + item);
            }

            EditorUtility.ClearProgressBar();

            EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
            /// abname:path
            var dic = new Dictionary<string, string>()
            {
                { "luascript", "Assets/Lua" },
                { "battlescp", "Assets/BattleScp" },
                { "gamescp", "Assets/GameScp" },
                { "configs", "Assets/Configs" },
            };
            var buildmap = new AssetBundleBuild[1];
            var assNames = new List<string>();
            var abd = new AssetBundleBuild();

            foreach (var pair in dic)
            {
                var abn = pair.Key;
                var assets = AssetDatabase.FindAssets("", new string[] { pair.Value });
                assNames.Clear();
                foreach (var item in assets)
                {
                    var asset = AssetDatabase.GUIDToAssetPath(item);
                    var abnThis = AssetDatabase.GetImplicitAssetBundleName(asset);
                    if (abnThis == abn)
                    {
                        assNames.Add(asset);
                    }
                }

                abd.assetBundleName = abn;
                abd.assetNames = assNames.ToArray();
                buildmap[0] = abd;
                Debug.Log(string.Format("abn:{1} assNames.Count:{0}", assNames.Count, abn));
                if (assNames.Count > 0)
                {
                    BuildPipeline.BuildAssetBundles(outputPath, buildmap
                        , BuildAssetBundleOptions.ChunkBasedCompression |
                          BuildAssetBundleOptions.DeterministicAssetBundle
                        , EditorUserBuildSettings.activeBuildTarget);
                }
            }

            EditorSettings.spritePackerMode = cacheMode;

            AssetDatabase.Refresh();

            EncryptAssetBundles(outputPath);

            foreach (var item in cacheFiles)
            {
                EditorHelp.CopyFile(backupPath + item, outputPath + "/" + item);
            }

            Directory.Delete(backupPath, true);
            CreatePatch();
        }

        [MenuItem("AssetBundles/ExtractAssetBundleFromFtp &2")]
        public static void ExtractAssetBundleFromFtp()
        {
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp start");
            /*#region svn获取
            System.Diagnostics.Process p = new System.Diagnostics.Process();
            //设置要启动的应用程序
            p.StartInfo.FileName = "cmd.exe";
            //p.StartInfo.Arguments = "svn info";
            //是否使用操作系统shell启动
            p.StartInfo.UseShellExecute = false;
            // 接受来自调用程序的输入信息
            p.StartInfo.RedirectStandardInput = true;
            //输出信息
            p.StartInfo.RedirectStandardOutput = true;
            // 输出错误
            p.StartInfo.RedirectStandardError = true;
            //不显示程序窗口
            p.StartInfo.CreateNoWindow = true;
            //启动程序
            p.Start();

            //向cmd窗口发送输入信息
            p.StandardInput.Flush();
            p.StandardInput.WriteLine("chcp 936");
            p.StandardInput.WriteLine("svn info | findstr \"^Relative\"&exit");
            p.StandardInput.AutoFlush = true;

            //等待程序执行完退出进程
            p.WaitForExit();
            p.Close();
            string svn_url = Regex.Match(p.StandardOutput.ReadToEnd(), @"Relative[^/\n]* ?/(\w+)/").Groups[1].Value;
#endregion*/

            string svn_url = "RTS20180306";
            if (File.Exists("svn_url"))
            {
                svn_url = File.ReadAllText("svn_url");
            }

            Debug.Log(svn_url);
            var asspath = AssetBundlesOutputPath;
            asspath = Path.GetFullPath(asspath);
            //			var plat = GetPlatformFolderForAssetBundles (BuildTarget.iOS);
            var plat = GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            var platFolder = asspath + "/" + plat;
            var platZip = string.Format("{0}/{1}.zip", asspath, plat);

            if (File.Exists(platZip))
            {
                File.Delete(platZip);
            }

            EditorHelp.CheckDir(platZip);
            //ftp://*************:2121/Assetbundle/Android/
            var fw = new FtpWeb("***********:2121", string.Format("Assetbundle/{1}/{0}", plat, svn_url), "user",
                "user");
            fw.Download(asspath, plat + ".zip");
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp Download end");

            if (Directory.Exists(platFolder))
            {
                Directory.Delete(platFolder, true);
            }

            ZipHelper.UnZipFile(platZip, asspath);
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp UnZipFile end");
            //			System.Diagnostics.Process.Start ("explorer.exe", asspath);
        }

        [MenuItem("AssetBundles/G_Patch &3")]
        public static void G_Patch()
        {
            var isOsx = Application.platform == RuntimePlatform.OSXEditor;

            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);

            var cmd = @"
cd ..\..\Tools\Patch\
python -OO Patch.py {0}
{1} -r {0}/* war_update/{0}
python -m SimpleHTTPServer 80
{1}";
            var patch_path = "../../Patch/Public";
            var replace_str = string.Format("cd {0} {1}", patch_path, isOsx ? "cp" : "copy");
            cmd = string.Format(cmd, platform, replace_str, isOsx ? "" : "pause");

            if (isOsx)
            {
                File.WriteAllText(patch_path + "/" + "start_server.sh", cmd);
            }
            else
            {
                var patchPublicstartServerbat = "../../Patch/Public/startServer.bat";
                patchPublicstartServerbat = Path.GetFullPath(patchPublicstartServerbat);
                System.Diagnostics.Process.Start(patchPublicstartServerbat, platform);
            }
        }

        static public void DirectoryCopy(string sourceDirectory, string targetDirectory)
        {
            try
            {
                DirectoryInfo dir = new DirectoryInfo(sourceDirectory);
                //获取目录下（不包含子目录）的文件和子目录
                FileSystemInfo[] fileinfo = dir.GetFileSystemInfos();
                foreach (FileSystemInfo i in fileinfo)
                {
                    if (i is DirectoryInfo) //判断是否文件夹
                    {
                        if (!Directory.Exists(targetDirectory + "/" + i.Name))
                        {
                            //目标目录下不存在此文件夹即创建子文件夹
                            Directory.CreateDirectory(targetDirectory + "/" + i.Name);
                        }

                        //递归调用复制子文件夹
                        DirectoryCopy(i.FullName, targetDirectory + "/" + i.Name);
                    }
                    else
                    {
                        //不是文件夹即复制文件，true表示可以覆盖同名文件
                        File.Copy(i.FullName, targetDirectory + "/" + i.Name, true);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError("复制文件异常:" + ex.Message);
            }
        }

        public static void CompareDiffs(int version)
        {
            void error(string text)
            {
                Debug.LogError(text);
                string workspace = JenkinsEnv.Instance.Get("WORKSPACE");
                if (workspace != null)
                {
                    string outputPath = ModifyAB.Instance.win2linuxDir(workspace + "/DiffDetails.json");
                    File.WriteAllText(outputPath, "Compare File.cause: " + text);
                }
            }

            var files = Directory.GetDirectories(Path.GetFullPath(ModifyAB.Instance.GetResUrlPrefix()));
            int m = 0;
            foreach (var f in files)
            {
                var fn = Path.GetFileName(f);
                if (int.TryParse(fn, out var v))
                {
                    m = Mathf.Max(m, v);
                }
            }

            if (m == 0)
            {
                error("resource folder not found");
                return;
            }

            if (version >= m)
            {
                error("can not compare resource because input version greater then or equal current version");
                return;
            }

            var oldHashCheck = ModifyAB.Instance.GetHashCheck(version);

            var currentHashCheck = ModifyAB.Instance.GetHashCheck(m);

            if (oldHashCheck == null)
            {
                error("compare resource fail. get old hashcheck error");
                return;
            }

            if (currentHashCheck == null)
            {
                error("compare resource fail. get current hashcheck error");
                return;
            }

            long time = DateTime.Now.Millisecond;
            string workspace = JenkinsEnv.Instance.Get("WORKSPACE");
            if (workspace != null)
            {
                string outputPath = ModifyAB.Instance.win2linuxDir(workspace + "/DiffDetails.json");
                File.WriteAllText(outputPath,
                    HotFixFilesDiffDetails.getCompareResult(oldHashCheck, currentHashCheck).outputString);
                Debug.Log("Compare Diffs complete, file: " + outputPath + ", time: " +
                          (DateTime.Now.Millisecond - time) + "ms");
            }
            else
            {
                Debug.LogError("write compare file error,workspace not found");
            }
        }

        public static void SendToDingDingSimple(string token, string title, string text, bool isAtAll)
        {
            var atMsg = new Dictionary<string, System.Object>()
            {
                { "msgtype", "text" },
                {
                    "at", new Dictionary<string, System.Object>
                    {
                        { "isAtAll", isAtAll }
                    }
                },
                {
                    "text", new Dictionary<string, System.Object>
                    {
                        { "title", $"热更新 标题:{title}, {DateTime.Now.ToString()}" },
                        { "content", $"热更新 内容：{text}" }
                    }
                }
            };
            string jsonData = UIHelper.ToJson(atMsg);
            Debug.Log($"send dingding msg=={jsonData}");
            string ReturnJson1 = Post(token, jsonData);
            Debug.Log($"send dingding result=={ReturnJson1}");
        }

        public static void ComparePreVersion()
        {
            long time = DateTime.Now.Millisecond;
            var ps = JenkinsEnv.Instance.Get("comparePreVersionParams");
            var cache = new Dictionary<string, string>();
            var lines = ps.Split(';');
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                var vals = line.Split('-');
                if (vals.Length >= 2)
                {
                    cache[vals[0]] = vals[1];
                }
            }

            string build_url = JenkinsEnv.Instance.Get("BUILD_URL");
            string svn_url = JenkinsEnv.Instance.Get("SVN_URL");
            string svntag = string.Empty;
            bool svntagFlag = cache.TryGetValue("SVNTag", out svntag);
            if (!svntagFlag)
            {
                Debug.LogError("parameter SVNTag is missing!!!");
                return;
            }

            string HServerPath = string.Empty;
            bool hserverPathFlag = cache.TryGetValue("HServerPath", out HServerPath);
            if (!hserverPathFlag)
            {
                Debug.LogError("parameter HServerPath is missing!!!");
                return;
            }

            string HServerURL = string.Empty;
            bool hserverURLFlag = cache.TryGetValue("HServerURL", out HServerURL);
            if (!hserverURLFlag)
            {
                Debug.LogError("parameter HServerURL is missing!!!");
                return;
            }

            string token = string.Empty;
            cache.TryGetValue("Token", out token);
            int limitNumber = string.IsNullOrEmpty(cache["LimitNumber"]) ? 40 : Convert.ToInt32(cache["LimitNumber"]);
            long value = limitNumber * 1024L * 1024L;
            string outputPathNotice = HServerPath + "Notice/";
            //创建输出目录
            if (!Directory.Exists(outputPathNotice))
            {
                Directory.CreateDirectory(outputPathNotice);
            }

            string outputPathLog = outputPathNotice + "Log/";
            if (!Directory.Exists(outputPathLog))
            {
                Directory.CreateDirectory(outputPathLog);
            }

            string timeName = System.DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss");
            string timeName2 = System.DateTime.Now.ToString("F");
            string svnLogName = "svnlog" + timeName + ".txt";
            string logName = svntag + timeName + ".json";
            string logSpaceURL = HServerURL + "Notice/Log/";
            string outputPath = outputPathLog + logName;
            string svnOutputPath = outputPathLog + svnLogName;
            string logURL = logSpaceURL + logName;
            string svnLogURL = logSpaceURL + svnLogName;
            string versiontipsPath = outputPathNotice + "versiontips.json";

            void error(string text)
            {
                Debug.LogError(text);
                if (outputPath != null)
                {
                    File.WriteAllText(outputPath, "Compare File.cause: " + text);
                }
            }

            int revision = JenkinsEnv.Instance.GetInt("newest_svn", -1);
            if (revision == -1)
            {
                error("revision can not fetch,revision==-1");
                return;
            }

            var files = Directory.GetDirectories(Path.GetFullPath(ModifyAB.Instance.GetResUrlPrefix()));
            int fileVersion = -1;
            foreach (var f in files)
            {
                var fn = Path.GetFileName(f);
                if (int.TryParse(fn, out var v))
                {
                    fileVersion = Mathf.Max(fileVersion, v);
                }
            }

            if (fileVersion == -1)
            {
                error("resource folder not found");
                return;
            }

            if (!File.Exists(versiontipsPath))
            {
                SaveVersion(fileVersion, revision, versiontipsPath);
                Debug.LogWarning("versiontips first build");
            }

            VersionTips tips;
            using (FileStream fs = new FileStream(versiontipsPath, FileMode.Open))
            {
                BinaryFormatter format = new BinaryFormatter();
                tips = (VersionTips)format.Deserialize(fs);
            }

            int preFileVersion = tips.PreFileVersion;
            int preVersion = tips.PreVersion;
            if (preVersion == -1 || preFileVersion == -1)
            {
                SaveVersion(fileVersion, revision, versiontipsPath);
                Debug.LogWarning("preVersion==-1 or preFileVersion == -1 and return");
                return;
            }

            if (preFileVersion >= fileVersion)
            {
                error("can not compare resource because  preFileVersion greater then or equal current fileVersion");
                return;
            }

            var oldHashCheck = ModifyAB.Instance.GetHashCheck(preFileVersion);
            var currentHashCheck = ModifyAB.Instance.GetHashCheck(fileVersion);
            if (currentHashCheck == null)
            {
                error("compare resource fail. get current hashcheck error");
                return;
            }

            if (oldHashCheck == null)
            {
                error(
                    "compare resource fail. get old hashcheck error, current hashcheck is not null and save current fileVersion and revision");
                SaveVersion(fileVersion, revision, versiontipsPath);
                return;
            }

            var result = HotFixFilesDiffDetails.getCompareResult(oldHashCheck, currentHashCheck);
            //热更量是否正常
            bool IsAbnormal = IsOverLimit(cache, oldHashCheck, currentHashCheck);
            //是否显示正常时的差异文件数据
            int IsShowNormal = string.IsNullOrEmpty(cache["IsShowNormal"]) ? 0 : Convert.ToInt32(cache["IsShowNormal"]);
            File.WriteAllText(outputPath, result.outputString);
            string svn_url_2 = svn_url.Replace("/BinClient/Client/jenkins", "");
            //获取svn记录并保存
            string str = $"/c svn log {svn_url_2} -r {revision}:{preVersion} -v > {svnOutputPath}";
            using (System.Diagnostics.Process compiler = new System.Diagnostics.Process())
            {
                compiler.StartInfo.FileName = "cmd.exe";
                compiler.StartInfo.Arguments = str;
                compiler.StartInfo.UseShellExecute = false;
                compiler.StartInfo.CreateNoWindow = true;
                compiler.StartInfo.RedirectStandardOutput = true;
                compiler.Start();
                compiler.WaitForExit();
            }

            Debug.Log("process complete:" + str);
            if ((IsShowNormal == 0 && IsAbnormal) || IsShowNormal != 0)
            {
                SendToDingDing(token,
                    $"{"####  热更新报告 \n "}  \n  时间：{timeName2}  \n  {"状态：" + (IsAbnormal ? "<font color=#FF0000 >异常</font>" : "<font color=#0000FF >正常</font>")}  \n SVN标签：{svntag}  \n  SVN当前版本:{revision}  \n  SVN前一版本:{preVersion}  \n  SVN_URL:{svn_url}  \n  CasualGame：{HotFixFilesDiffDetails.SizeSuffix(result.casualGameSize, 2)}  \n  Asset：{HotFixFilesDiffDetails.SizeSuffix(result.assetSize, 2)}  \n  [jenkins工程链接]({build_url})  \n  [差异文件地址]({logURL})  \n  [SVN日志地址]({svnLogURL})",
                    "差异文件", logURL, IsAbnormal, "0", limitNumber, errorMessage);
            }

            //删除超过指定时间的文件
            double day = string.IsNullOrEmpty(cache["SaveForDay"]) ? 5 : Convert.ToDouble(cache["SaveForDay"]);
            DeleteFiles(outputPathLog, day);
            Debug.Log(
                $"revision:{revision} preVersion:{preVersion} fileversion:{fileVersion} prefileversion{preFileVersion}");
            Debug.Log("Compare With PreVersion Diffs complete, file: " + outputPath + ", time: " +
                      (DateTime.Now.Millisecond - time) + "ms");
            SaveVersion(fileVersion, revision, versiontipsPath);
        }

        public static bool IsOverLimit(Dictionary<string, string> cache, hashCheck oldHashCheck,
            hashCheck currentHashCheck)
        {
            // 从缓存中获取限制值，如果不存在则设置默认值
            int limitNumber =
                cache.TryGetValue("LimitNumber", out var limitNumberStr) &&
                int.TryParse(limitNumberStr, out var limitNumberValue)
                    ? limitNumberValue
                    : 40;
            int limitExecSize =
                cache.TryGetValue("LimitExec", out var limitExecSizeStr) &&
                int.TryParse(limitExecSizeStr, out var limitExecSizeValue)
                    ? limitExecSizeValue
                    : 40;
            int limitGamescpLangSize =
                cache.TryGetValue("LimitGamescp_Lang", out var limitGamescpLangSizeStr) &&
                int.TryParse(limitGamescpLangSizeStr, out var limitGamescpLangSizeValue)
                    ? limitGamescpLangSizeValue
                    : 40;
            int limitLuaScriptSize =
                cache.TryGetValue("LimitLuaScript", out var limitLuaScriptSizeStr) &&
                int.TryParse(limitLuaScriptSizeStr, out var limitLuaScriptSizeValue)
                    ? limitLuaScriptSizeValue
                    : 40;
            int limitAssetOtherSize =
                cache.TryGetValue("LimitAssetOther", out var limitAssetOtherSizeStr) &&
                int.TryParse(limitAssetOtherSizeStr, out var limitAssetOtherSizeValue)
                    ? limitAssetOtherSizeValue
                    : 40;
            int limitCasualGameSize =
                cache.TryGetValue("LimitAssetOther", out var limitCasualGameSizeStr) &&
                int.TryParse(limitCasualGameSizeStr, out var limitCasualGameSizeValue)
                    ? limitCasualGameSizeValue
                    : 40;

            // 以字节为单位计算限制值
            var limitNumberBytes = limitNumber * 1024L * 1024L;
            var limitExecSizeBytes = limitExecSize * 1024L * 1024L;
            var limitGamescpLangSizeBytes = limitGamescpLangSize * 1024L * 1024L;
            var limitLuaScriptSizeBytes = limitLuaScriptSize * 1024L * 1024L;
            var limitAssetOtherSizeBytes = limitAssetOtherSize * 1024L * 1024L;
            var limitCasualGameSizeBytes = limitCasualGameSize * 1024L * 1024L;

            // 计算热更差异细节
            var hotfixDiffDetails = HotFixFilesDiffDetails.getCompareResult(oldHashCheck, currentHashCheck);

            // 检查热更大小是否在限制范围内
            var isAbnormal = limitNumberBytes <= hotfixDiffDetails.totalSize;
            var isLimitExecSize = limitExecSizeBytes <= hotfixDiffDetails.execSize;
            var isLimitGamescpLangSize = limitGamescpLangSizeBytes <= hotfixDiffDetails.gamescp_langSize;
            var isLimitLuaScriptSize = limitLuaScriptSizeBytes <= hotfixDiffDetails.luascriptSize;
            var isLimitAssetOtherSize = limitAssetOtherSizeBytes <= hotfixDiffDetails.assetOtherSize;
            var isLimitCasualGameSize = limitCasualGameSizeBytes <= hotfixDiffDetails.casualGameSize;
            errorMessage = "";
            // 如果任何大小超出限制，则生成错误消息
            if (isAbnormal)
            {
                errorMessage += $"总打包量超出{limitNumber} MB。\n";
            }

            if (isLimitExecSize)
            {
                errorMessage += $"exec打包量超出{limitExecSize} MB。\n";
            }

            if (isLimitGamescpLangSize)
            {
                errorMessage += $"gamescp_lang打包量超出{limitGamescpLangSize} MB。\n";
            }

            if (isLimitLuaScriptSize)
            {
                errorMessage += $"luascript打包量超出{limitLuaScriptSize} MB。\n";
            }

            if (isLimitAssetOtherSize)
            {
                errorMessage += $"assetOther打包量超出{limitAssetOtherSize} MB。\n";
            }

            if (isLimitCasualGameSize)
            {
                errorMessage += $"casualGame打包量超出{limitCasualGameSize} MB。\n";
            }

            string token = string.Empty;
            // 从缓存中获取token
            var tokenFlag = cache.TryGetValue("Token", out token);

            // 如果任何大小超出限制，则将错误消息发送到DingDing
            if (!string.IsNullOrEmpty(errorMessage))
            {
                var statMessage =
                    $"---总新增文件大小---\ntotal : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.totalSize)}\n---详细资源情况---\ncasualGameSize : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.casualGameSize)} \nexecSize : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.execSize)} \ngamescp_langSize : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.gamescp_langSize)} \nluascriptSize : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.luascriptSize)} \nassetOtherSize : {HotFixFilesDiffDetails.SizeSuffix(hotfixDiffDetails.assetOtherSize)} \n";
                errorMessage += statMessage;
                Debug.Log("新添加的预警");
                return true;
            }

            return false;
        }

        private static void SaveVersion(int prefileversion, int preversion, string versiontipsPath)
        {
            VersionTips temp = new VersionTips();
            temp.PreVersion = preversion;
            temp.PreFileVersion = prefileversion;
            using (FileStream fs = new FileStream(versiontipsPath, FileMode.Create))
            {
                BinaryFormatter format = new BinaryFormatter();
                format.Serialize(fs, temp);
            }
        }

        private static readonly StringBuilder jsonStringBuilder = new StringBuilder(512);
        private const string keyWord = "热更新";

        private static String Post(string token, string data)
        {
            string WebHookUrl = "https://oapi.dingtalk.com/robot/send?access_token=" + token;
            Debug.Log("webhookurl:" + WebHookUrl);
            var httpWebRequest = HttpWebRequest.Create(WebHookUrl);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = WebRequestMethods.Http.Post;
            if (data != null)
            {
                byte[] dataBytes = Encoding.UTF8.GetBytes(data);
                httpWebRequest.ContentLength = dataBytes.Length;
                using (var reqStream = httpWebRequest.GetRequestStream())
                {
                    reqStream.Write(dataBytes, 0, dataBytes.Length);
                }
            }

            using (var httpWebResponse = httpWebRequest.GetResponse())
            {
                var responseStream = httpWebResponse.GetResponseStream();
                return responseStream == null ? null : new StreamReader(responseStream, Encoding.UTF8).ReadToEnd();
            }
        }

        public static void SendToDingDing(string token, string text, string btnTitle1, string btnUrl1, bool isAtAll,
            string btnOrientation, int limitNumber, string errorMessage)
        {
            jsonStringBuilder.Clear();
            jsonStringBuilder.Append($"{{\"msgtype\":\"actionCard\"," +
                                     $"\"actionCard\":{{\"title\":\"{keyWord}\"," +
                                     $"\"text\":\"{text}\"," +
                                     $"\"btnOrientation\":\"{btnOrientation}\"," +
                                     $"\"btns\":[{{\"title\":\"{btnTitle1}\"," +
                                     $"\"actionURL\":\"{btnUrl1}\"}}]}}}}");
            Debug.Log("sendToDingDing:\n" + jsonStringBuilder);
            string ReturnJson1 = Post(token, jsonStringBuilder.ToString());
            Debug.Log("DingDing actionCard return result：" + ReturnJson1);
            jsonStringBuilder.Clear();
            if (isAtAll)
            {
                jsonStringBuilder.Append(
                    $"{{\"msgtype\":\"text\",\"text\":{{\"content\":\"---资源{keyWord}打包情况---\n{errorMessage}需要排查!\"}},\"at\":{{\"isAtAll\":{isAtAll.ToString().ToLower()}}}}}");
                string ReturnJson2 = Post(token, jsonStringBuilder.ToString());
                Debug.Log("DingDing Text return result：" + ReturnJson2);
            }
        }

        public static void DeleteFiles(string path, double day)
        {
            DateTime nowTime = DateTime.Now;

            if (path == string.Empty)

            {
                Debug.LogError("DeleteFiles>>path is empty string,return");
                return;
            }

            string[] files = Directory.GetFiles(path, "*.json", SearchOption.AllDirectories); //获取该目录下所有 .json文件

            foreach (string file in files)
            {
                FileInfo fileInfo = new FileInfo(file);

                TimeSpan t = nowTime - fileInfo.CreationTime; //当前时间  减去 文件创建时间

                double dt = t.Days;

                if (dt > day) //

                {
                    File.Delete(file); //删除超过时间的文件

                    Debug.Log("删除文件" + file + fileInfo.CreationTime);
                }
            }
        }

        [Serializable]
        class VersionTips
        {
            public int PreFileVersion;
            public int PreVersion;
        }


        #region TestCasualChannel

        public static void CopyAssetBundlesTest(bool copyab, string lang)
        {
            copyAssetBundle = copyab;
            JenkinsEnv.Instance.Set(CasualChannelBuild.CHANNEL_RES_KEY, lang);
            CasualChannelBuild.Instance.BuildABList();
            CopyAssetBundles();
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles true")]
        public static void CopyAssetBundlesTest1()
        {
            CopyAssetBundlesTest(true, "");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles false")]
        public static void CopyAssetBundlesTest2()
        {
            CopyAssetBundlesTest(false, "");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles true ja")]
        public static void CopyAssetBundlesTest3()
        {
            CopyAssetBundlesTest(true, "ja");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles false ja")]
        public static void CopyAssetBundlesTest4()
        {
            CopyAssetBundlesTest(false, "ja");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles true es")]
        public static void CopyAssetBundlesTest5()
        {
            CopyAssetBundlesTest(true, "es");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles false es")]
        public static void CopyAssetBundlesTest6()
        {
            CopyAssetBundlesTest(false, "es");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles true in")]
        public static void CopyAssetBundlesTest7()
        {
            CopyAssetBundlesTest(true, "in");
        }

        [MenuItem("AssetBundles/TestCasualChannel/CopyAssetBundles false in")]
        public static void CopyAssetBundlesTest8()
        {
            CopyAssetBundlesTest(false, "in");
        }

        #endregion
    }
}