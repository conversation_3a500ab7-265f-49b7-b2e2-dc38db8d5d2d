using System.Collections;
using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;
using System.IO;
using System;
using System.Collections.Generic;
using War.Base;

public class AutoCopySymbolsPostprocessor{
	[PostProcessBuild()]
    public static void OnPostprocessBuild(BuildTarget target, string pathToBuiltProject)
    { 
        Debug.Log($"构建完成！目标平台：{target}，输出路径：{pathToBuiltProject}");
       
        if (target == BuildTarget.Android)
            PostProcessAndroidBuild(pathToBuiltProject);
    }

    static bool IsEnableUploadBuglySymbols()
    {
        if (File.Exists(ConstPaths.editor_build_config))
        {
            var strEditorBuildConfig = File.ReadAllText(ConstPaths.editor_build_config);
            var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strEditorBuildConfig);
            if (!War.Base.BuildScript.IsEnableSwitch(editorBuildConfig, "UPLOAD_BUGLY_SYMBOL"))
            {
                return false;
            }
            return true;
        }
        return false;
    }

    static bool IsEnableBugly()
    {
        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        Dictionary<string, object> gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);

        var enableBuglyCfg = gameConfig["ENABLE_BUGLY"];
        if (enableBuglyCfg == null)
        {
            //未配置使用 Bugly
            Debug.LogWarning("Do not use Bugly");
            return false;
        }
        var enableBuglyValue = Convert.ToBoolean(enableBuglyCfg);
        if (!enableBuglyValue)
        {
            Debug.LogWarning("Do not use Bugly, ENABLE_BUGLY is false");
            return false;
        }
        return true;
    }

    public static void PostProcessAndroidBuild(string pathToBuiltProject)
    {
        CopyAndroidSymbols(pathToBuiltProject, PlayerSettings.Android.targetArchitectures);
    }

    
    public static void CopyAndroidSymbols(string pathToBuiltProject, AndroidArchitecture targetDevice)
    {
        Debug.Log("CopyAndroidSymbols:" + pathToBuiltProject + " targetDevice:" + targetDevice);
        string buildName = Path.GetFileNameWithoutExtension(pathToBuiltProject);
        string symbolsDir = buildName + "_Symbols/";
        string sourceSymbolsDir = pathToBuiltProject + "/unityLibrary/symbols/";
        var abi_x86 = "x86/";
        var abi_v7a = "armeabi-v7a/";
        var abi_v8a = "arm64-v8a/";

        //backup symbols
        string driveLetter = Path.GetPathRoot(pathToBuiltProject).Substring(0, 2);
        string unityInstallPath = Path.GetDirectoryName(UnityEditor.EditorApplication.applicationPath);
        unityInstallPath = unityInstallPath.Replace("\\", "/");
        string unitySymbolsDir = unityInstallPath + "/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/Symbols/";
        string symbolsBackupDir = driveLetter + "/BackupSymbols/" + buildName + "/" + PlayerSettings.bundleVersion + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "/";
        CreateDir(symbolsBackupDir + abi_x86);
        CreateDir(symbolsBackupDir + abi_v7a);
        CreateDir(symbolsBackupDir + abi_v8a);
        MoveAllFiles(sourceSymbolsDir + abi_x86, symbolsBackupDir + abi_x86);
        MoveAllFiles(sourceSymbolsDir + abi_v7a, symbolsBackupDir + abi_v7a);
        MoveAllFiles(sourceSymbolsDir + abi_v8a, symbolsBackupDir + abi_v8a);

        MoveAllFiles(unitySymbolsDir + abi_x86, symbolsBackupDir + abi_x86);
        MoveAllFiles(unitySymbolsDir + abi_v7a, symbolsBackupDir + abi_v7a);
        MoveAllFiles(unitySymbolsDir + abi_v8a, symbolsBackupDir + abi_v8a);

        UnityEngine.Debug.Log("Backup Android Symbols Complete:" + symbolsBackupDir);

        CreateDir(symbolsDir);

        if ((PlayerSettings.Android.targetArchitectures & AndroidArchitecture.ARM64) > 0)
        {
            var dir = CopySymbols(symbolsDir, symbolsBackupDir, abi_v8a);
            GenerateAllSymbols(dir,pathToBuiltProject);
        }
        if ((PlayerSettings.Android.targetArchitectures & AndroidArchitecture.ARMv7) > 0)
        {
            var dir = CopySymbols(symbolsDir, symbolsBackupDir, abi_v7a);
            GenerateAllSymbols(dir,pathToBuiltProject);
        }

#if UNITY_2018_1_OR_NEWER
         
#else

        if ((PlayerSettings.Android.targetArchitectures & AndroidArchitecture.X86) > 0)
        {
            var dir = CopySymbols(symbolsDir, sourceSymbolsDir, abi_x86);
            GenerateAllSymbols(dir,pathToBuiltProject);
        }
#endif
        //GenerateAllSymbols(symbolsDir);

        //switch (PlayerSettings.Android.targetDevice)
        //{
        //    case AndroidTargetDevice.FAT:
        //        {
        //            //CopyARMSymbols(symbolsDir);
        //            //CopyX86Symbols(symbolsDir);
        //            CopySymbols(symbolsDir, abi_v8a);
        //            CopySymbols(symbolsDir, abi_v7a);
        //            CopySymbols(symbolsDir, abi_x86);

        //            GenerateAllSymbols(symbolsDir);
        //            break;
        //        }
        //    case AndroidTargetDevice.ARMv7:
        //        {
        //            //CopyARMSymbols(symbolsDir);
        //            CopySymbols(symbolsDir,abi_v7a);
        //            GenerateAllSymbols(symbolsDir);
        //            break;
        //        }
        //    case AndroidTargetDevice.x86:
        //        {
        //            CopySymbols(symbolsDir,abi_x86);
        //            GenerateAllSymbols(symbolsDir);
        //            break;
        //        }
        //    default:
        //        break;
        //}
    }

    const string libpath = "/../Temp/StagingArea/libs/";

    //private static void CopyARMSymbols(string symbolsDir)
    //{
    //    string sourcedirARM = Application.dataPath + libpath + "armeabi-v7a/";
    //    Debug.Log(sourcedirARM);
    //    CreateDir(symbolsDir + "armeabi-v7a/");
    //    MoveAllFiles(sourcedirARM, symbolsDir + "armeabi-v7a/");
    //}

    //private static void CopyX86Symbols(string symbolsDir)
    //{
    //    string sourcedirX86 = Application.dataPath + libpath + "x86/";
    //    CreateDir(symbolsDir + "x86/");
    //    MoveAllFiles(sourcedirX86, symbolsDir + "x86/");
    //}
    private static string CopySymbols(string symbolsDir,string sourceSymbolsParentDir ,string abi)
    {
        //const string abi = "arm64-v8a/";
        // string sourcedir = Application.dataPath + libpath + abi;
        string sourcedir = sourceSymbolsParentDir + abi;
        string abi_dir = symbolsDir + abi;
        CreateDir(abi_dir);
        MoveAllFiles(sourcedir, abi_dir);
        "".Print("CopySymbols", symbolsDir, abi);
        return abi_dir;
    }
    
    public static void CreateDir(string path)
    {
        if (Directory.Exists(path))
            Directory.Delete(path,true);

        Directory.CreateDirectory(path);
    }

    public static void MoveAllFiles(string src, string dst)
    {
        DirectoryInfo srcinfo = new DirectoryInfo(src);
        if (srcinfo.Exists)
        {
            dst = dst.Replace("\\", "/");
            FileInfo[] files = srcinfo.GetFiles("*.*");
            for (int i = 0; i < files.Length; i++)
            {

                if (File.Exists(dst + "/" + files[i].Name))
                {
                    File.Delete(dst + "/" + files[i].Name);
                }
                File.Copy(files[i].FullName, dst + "/" + files[i].Name);
            }
        }
    }

    public static void GenerateAllSymbols(string symbolsdir,string pathToBuiltProject)
    {        
        if (!IsEnableBugly())
        {
            Debug.Log("ENABLE_BUGLY is false, skip upload symbols");
            return;
        }

        if (!IsEnableUploadBuglySymbols())
        {
            Debug.LogWarning("Disable Upload Bugly Symbol in editor_build_config.json, skip upload symbols");
            return;
        }
        
        Debug.Log("GenerateAllSymbols:" + symbolsdir);
        DirectoryInfo srcinfo = new DirectoryInfo(symbolsdir);
        if (srcinfo.Exists)
        {
            string cmd = Application.dataPath;
            string soPath = Application.dataPath;
            string jdk = EditorPrefs.GetString("JdkPath");
            if (Application.platform == RuntimePlatform.OSXEditor)
            {
                // cmd += "/../BuglySymbol/buglySymbolAndroid.jar";
                cmd += "/../BuglySymbol/buglyqq-upload-symbol.jar";
                jdk += "/bin/java";
                soPath += "/../" + symbolsdir;
            }
            else if(Application.platform == RuntimePlatform.WindowsEditor)
            {
                // cmd += "/../BuglySymbol/buglySymbolAndroid.jar";
                cmd += "/../BuglySymbol/buglyqq-upload-symbol.jar";
                cmd = cmd.Replace("/", "\\");
#if UNITY_ANDROID && !(UNITY_STANDALONE_OSX || UNITY_EDITOR_OSX)
                if (string.IsNullOrEmpty(jdk) || !jdk.ToLower().Contains("jdk"))
                {
                    jdk = UnityEditor.Android.AndroidExternalToolsSettings.jdkRootPath;
                }
#endif
                jdk = jdk.Replace("/","\\") + "\\bin\\java.exe";
                soPath += "/../" + symbolsdir;
                soPath = soPath.Replace("/", "\\");
            }

            var appID = "ef9301f8fb";
            var appKey = "ea81cbea-41b4-49d0-a40a-0dcad0cf4c6b";
            var channelTag = GameConfig.Instance().TryGetString("CHANNEL_TAG", "com.bc.avenger");
            var buglyChannelAsset = Resources.Load<com.bugly.sdk.BuglyChannelAsset>("bugly_config");
            var buglyCfg = buglyChannelAsset.GetBuglyChannelCfg(channelTag);
            if(buglyCfg == null)
            {
                Debug.LogError("can not find bugly channel data:" + channelTag);
            }
            bool q1DebugValue = GameConfig.Instance().TryGetBool("ENABLE_Q1_DEBUG_MODE", false);
            if (q1DebugValue)
            {
                if (buglyCfg != null)
                {
                    appID = buglyCfg.debugAppID;
                    appKey = buglyCfg.debugAppKey;
                }
                else
                {
                    Debug.LogError("use default debug bugly channel data:" + channelTag);
                }
            }
            else
            {
                if (buglyCfg != null)
                {
                    appID = buglyCfg.appID;
                    appKey = buglyCfg.appKey;
                }
                else
                {
                    // throw new Exception("can not find bugly channel data:" + channelTag);
                    //暂时使用默认的安卓
                    appID = "e35a77beb4";
                    appKey = "fc271e2c-f851-4d35-b577-32f5b92e7799";
                }
 
            }

            Debug.Log("bugly symbolsdir:" + symbolsdir);

            string logDir = pathToBuiltProject + "/log_upload_bugly_symbols.txt";
#if UPLOAD_SYMBOLS || true
            // ProcessCommand(jdk, "-jar " + cmd + " -i " + symbolsdir + " -u -id " + appID + " - key " + appKey + " - package " + PlayerSettings.applicationIdentifier + " -version " + PlayerSettings.bundleVersion);
            string commandStr = "-jar " + cmd + " -appid "  + appID + " -appkey " + appKey + " -bundleid " + PlayerSettings.applicationIdentifier + " -version " + PlayerSettings.bundleVersion + " -platform Android" + " -inputSymbol " + soPath + " > "+logDir+" 2>&1 &";

            Debug.Log("bugly ProcessCommand str:" + commandStr);
            ProcessCommand(jdk, commandStr);
#else
            ProcessCommand(jdk, "-jar " + cmd + " -i " + symbolsdir);
#endif
            //ProcessCommand(cmd,"-i " + symbolsdir + " -u -id 844a29e21e -key b85577b4-1347-40bb-a880-f8a91446007f -package " + PlayerSettings.applicationIdentifier + " -version " + PlayerSettings.bundleVersion);
        }
    }

    public static void ProcessCommand(string command, string argument)
    {
        System.Diagnostics.ProcessStartInfo info = new System.Diagnostics.ProcessStartInfo(command);
        info.Arguments = argument;
        info.CreateNoWindow = false;
        info.ErrorDialog = true;
        info.UseShellExecute = true;

        if (info.UseShellExecute)
        {
            info.RedirectStandardOutput = false;
            info.RedirectStandardError = false;
            info.RedirectStandardInput = false;
        }
        else
        {
            info.RedirectStandardOutput = true;
            info.RedirectStandardError = true;
            info.RedirectStandardInput = true;
            info.StandardOutputEncoding = System.Text.UTF8Encoding.UTF8;
            info.StandardErrorEncoding = System.Text.UTF8Encoding.UTF8;
        }
        try
        {
            System.Diagnostics.Process process = System.Diagnostics.Process.Start(info);

            if (!info.UseShellExecute)
            {
                Debug.Log(process.StandardOutput);
                Debug.Log(process.StandardError);
            }

            process.WaitForExit();
            process.Close();
        }
        catch (Exception ex)
        {
            "".PrintError("ProcessCommand error",command,argument, ex);
        }
    }
}
