using System.Collections.Generic;
using UnityEngine;

namespace editor.tools.asset
{
    [CreateAssetMenu(menuName = "CreateAssets/DeepLinkChannelAsset")]
    [System.Serializable]
    public class DeepLinkChannelAsset : ScriptableObject
    {
        [System.Serializable]
        public class DeepLinkCfg
        {
            public string channelTag;
            public List<DeepLinkUrlSchemes> urlSchemes;
            public List<string> universalLinks;
        }
        [System.Serializable]
        public class DeepLinkUrlSchemes
        {
            public string identifier;
            public List<string> schemes;
        }

        public List<DeepLinkCfg> dataSet = new List<DeepLinkCfg>();
        
        public DeepLinkCfg GetDeepLinkCfg(string channelTag)
        {
            var channelCount = dataSet.Count;
            if (channelCount == 0)
            {
                Debug.LogError("未配置 deeplink 数据");
                return null;
            }
            foreach(var deeplinkCfg in dataSet)
            {
                if(deeplinkCfg.channelTag == channelTag)
                {
                    return deeplinkCfg;
                }
            }

            Debug.LogError("deeplinkCfg channelTag 配置错误:" + channelTag);
            return null;
        }
        
    }
}
