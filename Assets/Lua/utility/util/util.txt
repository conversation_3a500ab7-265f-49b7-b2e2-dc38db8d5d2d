local pairs = pairs
local type = type
local tostring = tostring
local tonumber = tonumber
local table = table
local coroutine = coroutine
local string = string
local os = os
local loadstring = loadstring
local print = print
local ipairs = ipairs
local dump = dump
local typeof = typeof
local io = io
local math = math
local setmetatable = setmetatable
local rawget = rawget

local _G = _G

local HorizontalWrapMode = CS.UnityEngine.HorizontalWrapMode
local VerticalWrapMode = CS.UnityEngine.VerticalWrapMode
local TextAnchor = CS.UnityEngine.TextAnchor
local TextAlignmentOptions = CS.TMPro.TextAlignmentOptions
local NetworkReachability = CS.UnityEngine.NetworkReachability
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Time = CS.UnityEngine.Time
local SystemInfo = CS.UnityEngine.SystemInfo
local GameObject = CS.UnityEngine.GameObject
local RectTransformUtility = CS.UnityEngine.RectTransformUtility
local Vector3 = CS.UnityEngine.Vector3
local DateTime = CS.System.DateTime
local DateTimeKind = CS.System.DateTimeKind
local Convert = CS.System.Convert
local Debug = CS.UnityEngine.Debug
local Camera = CS.UnityEngine.Camera
local Directory = CS.System.IO.Directory
local Path = CS.System.IO.Path
local Utility = CS.War.Script.Utility
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local ImageGray = CS.War.UI.ImageGray
local RawImage = CS.UnityEngine.UI.RawImage
local Image = CS.UnityEngine.UI.Image
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local HttpUtils = CS.War.Script.HttpUtils
local Encoding = CS.System.Text.Encoding
local HybridCLREditorWindow = CS.HybridCLREditorWindow
local UtilityTool = CS.War.Base.UtilityTool
local Config = CS.War.Base.Config
local Type = CS.System.Type
local File = CS.System.IO.File
local Screen = CS.UnityEngine.Screen
local screen_util = require "screen_util"
local Q1SDK = CS.Q1.Q1SDK
local BindingFlags = CS.System.Reflection.BindingFlags
local AssetBundleManager = CS.War.Base.AssetBundleManager
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote
local AssetDatabase = CS.UnityEditor.AssetDatabase
local FileInfo = CS.System.IO.FileInfo
local Crc32 = CS.ICSharpCode.SharpZipLib.Checksums.Crc32
local PostProcessingBehaviour = CS.UnityEngine.PostProcessing.PostProcessingBehaviour
local Int32 = CS.System.Int32
local SearchOption = CS.System.IO.SearchOption
local LunarConsole = CS.LunarConsolePlugin.LunarConsole
local GetArrayIndex = Utility.GetArrayIndex
local System = CS.System
local ParticleSystemType = typeof(CS.UnityEngine.ParticleSystem)
local GraphicRaycasterType = typeof(CS.War.UI.GRaycaster)
local NotchHelper = CS.com.notch.NotchHelper
local LuaModuleSwitch = LuaModuleSwitch
local Rect = CS.UnityEngine.Rect
local Vector2 = CS.UnityEngine.Vector2
local AssetsUpdator = CS.AssetsUpdator
local start_time = DateTime.Now.Ticks
local Aes = CS.War.Common.Aes

local Gradient = CS.War.UI.Gradient
local Outline = CS.UnityEngine.UI.Outline
local RectTransform = CS.UnityEngine.RectTransform
local VerticalWrapMode = CS.UnityEngine.VerticalWrapMode

local _require = require

local saveModules = {}
local Text = CS.UnityEngine.UI.Text
local huiguang = nil
local isEditor = Application.isEditor

local IsPBNew = IsPBNew

function makeRequire(module)
    if not module then
        return
    end

    local lm = nil
    if not saveModules[module] then
        local last = DateTime.Now.Ticks
        lm = _require(module)
        saveModules[module] = (DateTime.Now.Ticks - last) / 10000
    else
        lm = _require(module)
    end

    return lm
end

-- require = makeRequire
local event = require "event"
local const = require "const"
local game_config = require "game_config"
local timer_mgr = require "timer_mgr"
local lang = require "lang"
local log = require "log"
local json = require "dkjson"
local color_palette = require "color_palette"

local bgAssetBundleName = "art/actorbackground/@@@autoaddedroot@@@.prefab"

local blurbg_pool = nil
local useBlockAll = false
local require = require
local Application = CS.UnityEngine.Application
module("util")

TICK_ZERO = start_time - start_time

local base_object = require "base_object"
local delayObj = base_object()
-- 允许coroutine.yield ,一般延迟操作请使用DelayCallOnce
function DelayCall(seconds, func, bStack)
    return delayObj:CreateTimeTicker(seconds, func, bStack)
end
function DelayCallOnce(seconds, func)
    return timer_mgr:AddTimer(seconds, func)
end
--判定使用设备是否为ios
function IsIosPlayer()
    if Application.platform == RuntimePlatform.IPhonePlayer then
        return true
    end
    return false
end

--这个状态是用来处理一个隐患问题的，在协议界面已加载，uiloginmain加载时BlockAll值为true协议界面是点击不了的，重构CheckState时要注意
function SetUseBlockAll(b)
    useBlockAll = b
end
function GetUseBlockAll()
    return useBlockAll
end

---是否能使用截图做背景的功能
---@return boolean true表示可以
function CanUseBlurBg()
    return false
    -- local menu_bot_data = require "menu_bot_data"
    -- return (not IsIosPlayer()) and (not menu_bot_data.IsHubPage()) and (not menu_bot_data.IsRiskPage())
end

function RemoveDelayCall(ticker)
    if not ticker then
        return
    end
    if type(ticker) == "number" then
        timer_mgr:RemoveTimer(ticker)
    else
        delayObj:RemoveTimeTicker(ticker)
    end
end

function GetBlurBgPool()
    if (blurbg_pool == nil) then
        local base_game_object = require "base_game_object"
        local baseGameObj = base_game_object()
        baseGameObj:LoadResource(bgAssetBundleName, nil, function(UIPrefab)
            if UIPrefab then
                local canvasMRoot = GameObject.Find("/UIRoot/CanvasWithMesh")
                local prefab_pool = require "prefab_pool"
                blurbg_pool = prefab_pool.CreatePrefabPool(canvasMRoot.transform, UIPrefab, "blurbg_pool")
            end
        end)
    end
    return blurbg_pool
end


-- func返回true表示结束



--动态间隔计算计时器
---@param func 计算函数必须return [true/false],[time] 两个返回值
---@param delayStart 延迟启动时间
function DynamicIntervalCall(func, delayStart)
    local ticker = nil
    local data = {}
    delayStart = delayStart or 0
    data.start = os.time() + delayStart
    ticker = timer_mgr:AddTimer(delayStart or 0, function()
        local stop, interval = func(os.time() - data.start)
        if stop then
            RemoveDelayCall(ticker)
            ticker = nil
        end
        return interval
    end)
    return ticker
end

local DelayMark = {}
local DelayMarkTickers = {}
function DelayOneCall(mark, call, delay)
    delay = delay or 0.5
    local osTime = DateTime.Now.Ticks / 1000000
    DelayMark[mark] = DelayMark[mark] or osTime
    --print("DelayOneCall",mark,osTime,DelayMark[mark])
    if osTime >= DelayMark[mark] then
        DelayMark[mark] = osTime + delay * 10
        -- print("DelayCall",))
        DelayCall(delay, call)
    end
end
function DelayOneCallNoCoroutine(mark, call, delay)
    delay = delay or 0.5
    local osTime = DateTime.Now.Ticks / 1000000
    DelayMark[mark] = DelayMark[mark] or osTime
    --print("DelayOneCall",mark,osTime,DelayMark[mark])
    if osTime >= DelayMark[mark] then
        DelayMark[mark] = osTime + delay * 10
        -- print("DelayCall",))
        DelayMarkTickers[mark] = DelayCallOnce(delay, call)
    end
end



function Until(judge, func)
    local judgeFunc = judge
    if type(judge) ~= "function" then
        judgeFunc = function()
            return judge ~= nil
        end
    end
    if judgeFunc() then
        func()
        return nil
    end

    local ticker = nil
    ticker = delayObj:CreateTimeTicker(0, function()
        while true do
            if judgeFunc() then
                break
            end

            coroutine.yield(0)
        end

        func()
        delayObj:RemoveTimeTicker(ticker)
        ticker = nil
    end
    )

    return ticker
end

function YieldFrame(func)
    local ticker = nil
    ticker = DelayCallOnce(0, function()
        -- coroutine.yield(0)

        func()
        RemoveDelayCall(ticker)
        -- delayObj:RemoveTimeTicker(ticker)
        ticker = nil
    end
    )
end

local limitObj = nil
--限制多少秒内只调用一次，limit为秒
function LimitCall(mark,func, limit)
    local osTime = DateTime.Now.Ticks / 1000000
    limit = limit or 0.5
    if not limitObj then
        limitObj = {}
    end
    limitObj[mark] = limitObj[mark] or osTime
    if osTime >= limitObj[mark] then
        limitObj[mark] = osTime + limit
        func()
    end
end


--[[
	让在delay时间内调用多次的函数只调用一次
]]
local CoverMark = {}
function CoverOneCall(mark, delay, call)
    -- if CoverMark[mark] then
    -- 	RemoveDelayCall(CoverMark[mark])
    -- end
    -- CoverMark[mark] = DelayCallOnce(delay,call)
    DelayOneCallNoCoroutine(mark, call, delay)
end

function ClearCoverCall(mark)
    if DelayMarkTickers[mark] then
        RemoveDelayCall(DelayMarkTickers[mark])
        DelayMarkTickers[mark] = nil
    end
end

------------------------编辑器下模拟华佗小游戏---------------------------------

function IsSimulateHybridCLR()
    if not Application.isEditor then
        return false
    else
        return HybridCLREditorWindow.HybridCLRInEditor
    end
end

function IsSimulateMultiTiny()
    if not Application.isEditor then
        return false
    else
        return HybridCLREditorWindow.MultiTinyInEditor
    end
end

function GetSimulateTinyRes()
    if Application.isEditor then
        return HybridCLREditorWindow.TinyResKey
    else
        return ""
    end
end

function GetSimulateHybridCLRTinyResUrl()
    return HybridCLREditorWindow.TinyResUrl
end

function GetSimulateHybridCLRTinyPlatformString()
    return HybridCLREditorWindow.TinyPlatformString
end

function GetSimulateHybridCLRTinyLocalLua()
    return IsSimulateMultiTiny() and HybridCLREditorWindow.TinyLocalLua
end

function GetSimulateHybridCLRTinyLocalAB()
    return IsSimulateMultiTiny() and HybridCLREditorWindow.TinyLocalAB
end

function GetSimulateHybridCLRTinyOpenPreload()
    return IsSimulateMultiTiny() and HybridCLREditorWindow.TinyPreload
end

function SimulateHybridCLRLoadLocalLua(name)
    return HybridCLREditorWindow.LoadLocalLua(name)
end

function SimulateHybridCLRRedirectionAssembly()
    return HybridCLREditorWindow.RedirectionTinyAssembly()
end

-----------------------------------------------------------------------

--[[格式化时间
@param nTime 秒
@param strFormat 格式化字符串，比如：#D天#H小时#M分#S秒
@param isShowDayTag 是否显示天标签,如d天这样，默认不显示
]]
function FormatTime2(nTime, strFormat, isShowDayTag)
    strFormat = strFormat or "#D #H:#M:#S"

    local dayNum, timeStr = string.countdown(nTime)
    local dataStr = isShowDayTag and '%dd' or '%d'
    local day = string.format(dataStr, dayNum)
    local hour, min, sec = string.match(timeStr, '(%d+):(%d+):(%d+)')
    --[[local day = string.format("%02d", nTime / 86400)
    local hour = string.format("%02d", math.mod(nTime, 86400) / 3600)
    local min = string.format("%02d", math.mod(nTime / 60, 60))
    local sec = string.format("%02d", math.mod(nTime, 60))
	]]
    if not string.find(strFormat, "#D") then
        hour = hour + day * 24
    end
    --print(nTime,strFormat,day,hour,min,sec,dayNum,timeStr)
    strFormat = string.gsub(strFormat, "#D", day)
    strFormat = string.gsub(strFormat, "#H", hour)
    strFormat = string.gsub(strFormat, "#M", min)
    strFormat = string.gsub(strFormat, "#S", sec)

    return strFormat
end

--[[格式化时间
@param nTime 秒
@param content 前缀 如：剩余时间
]]
function FormatTime3(nTime, content)
    local s = string.format("#D%s#H:#M:#S", lang.Get(36061))
    local strFormat = string.format(content, s)
    local dayNum, timeStr = string.countdown(nTime)
    local day = string.format('%d', dayNum)
    local hour, min, sec = string.match(timeStr, '(%d+):(%d+):(%d+)')

    if not string.find(strFormat, "#D") then
        hour = hour + day * 24
    end
    if dayNum <= 0 then
        if tonumber(hour) > 0 then
            s = string.format("#H:#M:#S", lang.Get(36061))
            strFormat = string.format(content, s)
        elseif tonumber(hour) <= 0 then
            s = string.format("#M:#S", lang.Get(36061))
            strFormat = string.format(content, s)
        end
    end
    if dayNum > 0 then
        strFormat = string.gsub(strFormat, "#D", day)
        strFormat = string.gsub(strFormat, "#H", hour)
        strFormat = string.gsub(strFormat, "#M", min)
        strFormat = string.gsub(strFormat, "#S", sec)
    elseif dayNum <= 0 then
        if tonumber(hour) > 0 then
            strFormat = string.gsub(strFormat, "#H", hour)
            strFormat = string.gsub(strFormat, "#M", min)
            strFormat = string.gsub(strFormat, "#S", sec)
        elseif tonumber(hour) <= 0 then
            strFormat = string.gsub(strFormat, "#M", min)
            strFormat = string.gsub(strFormat, "#S", sec)
        end
    end
    return strFormat
end
---@public 格式化时间 将传入的时间秒数 转换成时间单位的字符串 eg. 118 --> 1m 58s    3672 --> 1h 1m 12s
---@param seconds  number 秒
function FormatTime4(seconds)
    local days = math.floor(seconds / 86400)  -- 1天=86400秒
    seconds = seconds % 86400                   -- 取余数
    local hours = math.floor(seconds / 3600)    -- 1小时=3600秒
    seconds = seconds % 3600                     -- 取余数
    local minutes = math.floor(seconds / 60)     -- 1分钟=60秒
    seconds = seconds % 60                        -- 取余数
    local result = ""
    if days > 0 then
        result = result .. days .. "d "
    end
    if hours > 0 then
        result = result .. hours .. "h "
    end
    if minutes > 0 then
        result = result .. minutes .. "m "
    end
    if seconds >= 0 then
        result = result .. seconds .. "s"
    end
    return result
end

--格式化间隔时间
function FormatDelayTime(time)
    local t = math.floor(time)
    local day = math.floor(t / (86400))
    local hour = math.floor(t / (3600))
    local second = math.floor(t / 60)
    if day > 0 then
        return string.format(lang.Get(19065), day)
    elseif hour > 0 then
        return string.format(lang.Get(19066), hour)
    elseif second > 0 then
        return string.format(lang.Get(19067), second)
    else
        return lang.Get(19068)
    end
end

-------------------------------------------------------------------
-- 分割字符串
-- brief : 分割字符串，返回一个字符串数组
-- @param str : 要分割的字符串
-- @param separator : 分割符,可以是一个长度1-n的字符串
-- @param filter    : 过滤器,预处理函数,可以为nil,比如可以传入tonumber,这样就全部转成数字了
-------------------------------------------------------------------
function SplitString(str, separator, filter)
    if str == nil then
        local log = require "log"
        log.Error("要分割的字符串为空,请检查！")
        return nil
    end

    local ret = {}
    local sp_len = string.len(separator)
    local pos = 1 - sp_len
    while true do
        local pre = pos + sp_len
        pos = string.find(str, separator, pre, true)
        if pos == nil then
            table.insert(ret, string.sub(str, pre))
            break
        else
            table.insert(ret, string.sub(str, pre, pos - 1))
        end
    end

    -- 预处理一下
    if filter ~= nil then
        for k, v in ipairs(ret) do
            ret[k] = filter(v)
        end
    end

    return ret
end

--[[获取当前时间]]
function GetCurTickCount()
    -- Time.realtimeSinceStartup 不受 Time.timeScale 影响
    -- 按目前此接口调用情况，多用于计算时长，此时排除 timeScale 影响更利于提高准确度
    return Time.realtimeSinceStartup * 1000
end

--[[将一个table转换成一个字符串]]
function TableToString(t)
    local retstr = "{"

    local i = 1
    for key, value in pairs(t) do
        local signal = ","
        if i == 1 then
            signal = ""
        end

        --		if key==i then
        --			-- 不加键值
        --
        --			retstr = retstr..signal..tostringEx(value)
        --		else
        if type(key) == 'number' then
            retstr = retstr .. signal .. '[' .. key .. "]=" .. ToStringEx(value)
        else
            retstr = retstr .. signal .. key .. "=" .. ToStringEx(value)
        end
        --		end

        i = i + 1

    end

    retstr = retstr .. "}"
    return retstr
end

--[[将一个值转换成字符串]]
function ToStringEx(value)
    if type(value) == 'table' then
        return TableToString(value)
    elseif type(value) == 'string' then
        return "\"" .. value .. "\""
    else
        return tostring(value)
    end
end

--[[将一个整型数据转换成0，1数组]]
function IntToBiteArray(intNum)
    local bytes = Utility.IntToBiteArray(intNum)
    return bytes
end

ENETWORK_TYPE = {
    MOBILE = 1, --移动数据
    WIFI = 2, --WIFI
    NO_NETWORK = 3        --无网络
}
local lastNetworkTypeTime = 0
local lastNetworkType = nil
--获取当前网络类型：移动数据，WIFI，无网络
function GetNetworkType()
    if Time.time - lastNetworkTypeTime > 0.5 then
        lastNetworkTypeTime = Time.time
        lastNetworkType = GetInternalNetworkType()
        -- print("GetInternalNetworkType",lastNetworkType)
    end

    return lastNetworkType
end
function GetInternalNetworkType()
    if Application.internetReachability == NetworkReachability.NotReachable then
        return ENETWORK_TYPE.NO_NETWORK
    elseif Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork then
        return ENETWORK_TYPE.WIFI
    elseif Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork then
        return ENETWORK_TYPE.MOBILE
    end
    return nil
end

-- 获取版本号
function GetClientVersion()
    return Application.version
end

--[[退出游戏]]
function QuitGame()
    if Application.platform == RuntimePlatform.IPhonePlayer then
        Debug.LogError("准备退出游戏！！！")
        local q1sdk = require "q1sdk"
        q1sdk.ExitApplication()
    else
        Utility.QuitGame()
    end
end

--[[格式化时间
@param nTime 秒
@param strFormat 格式化字符串
]]
function FormatTime(nTime)
    local date = os.date("*t", nTime)
    return string.format('%d-%02d-%02d %02d:%02d', date.year, date.month, date.day, date.hour, date.min)
end

--获取格式化的倒计时,超过24小时显示单位为：X天X小时
function GetCountDownString(surplusTime)
    local timeStr = ""
    local lang = require "lang"
    if surplusTime > 86400 then
        timeStr = FormatTime2(surplusTime, "#D " .. lang.Get(36061) .. " #H " .. lang.Get(15828))
    else
        timeStr = FormatTime2(surplusTime, "#H:#M:#S")
    end
    return timeStr
end

-- local timeDes = {"%d年前","%d月前","%d天前","%d小时前","%d分钟前","%d秒前"}
local timeKey = { "year", "month", "day", "hour", "min", "sec", }

local yearSec = 3600 * 24 * 30 * 365
local monthSec = 3600 * 24 * 30
local daySec = 3600 * 24
local hourSec = 3600
local minSec = 60
local secSec = 1
function IntervalTime(nTime)
    -- if nTime <=0 then return end
    local lang = require "lang"
    nTime = nTime or 0
    local side = nTime > 0 and 1 or 2
    nTime = math.abs(nTime)
    -- local date = os.date("*t", nTime)
    -- print(nTime)
    local date = {}
    date.year = math.floor(nTime / yearSec)
    date.month = math.floor(nTime % yearSec / monthSec)
    date.day = math.floor(nTime % monthSec / daySec)
    date.hour = math.floor(nTime % daySec / hourSec)
    date.min = math.floor(nTime % hourSec / minSec)
    date.sec = math.floor(nTime % minSec)
    for i = 1, #timeKey do

        if date[timeKey[i]] > 0 then
            local duration = string.format(lang.Get(87 + i), tostring(date[timeKey[i]]))
            local des = string.format(lang.Get(93 + side), tostring(duration))

            return des
        end
    end
end

--浮点数转int
function Float2Int(num)
    local returnNum = 0
    if nil == num then
        return 0
    end
    if num <= 0 then
        return math.ceil(num);
    end
    if math.ceil(num) == num then
        returnNum = math.ceil(num);
    else
        returnNum = math.ceil(num) - 1;
    end
    return returnNum;
end

-- 如果siblingTrans不为nil。表示设置相对于siblingTrans的次序。
-- 0表示设置到它前一位。-1前两位。1后一位
-- 如果siblingTrans为nil
-- 支持负数 -1最后一个
function SetSiblingIndexEx(trans, index, siblingTrans)
    local parentTrans = trans.parent
    if siblingTrans then
        index = index + siblingTrans:GetSiblingIndex()
    else
        if index < 0 then
            index = index + parentTrans.childCount
        end
    end

    --@TODO 2018-12-24 15:18:12 截取index值
    trans:SetSiblingIndex(index)
end

-- 刘海屏的手机型号
local externDevices = {
    "iPhone10,3",
    "iPhone10,6",
    "vivo vivo Y85A",
    "OPPO PADM00",
    "OPPO PACM00",
}

function IsWebSocket()
	if Application.platform == RuntimePlatform.WebGLPlayer then
		return true
	end
	return (not not game_config.ENABLE_WEB_SOCKET)
end

--[[通过设备型号判断该设备是不是异形屏幕（带刘海），如果是则返回true]]
function Flag_ExternScreen()
    if IsModuleOpen("ExternScreenMatching") then
        return true
    end
    --return true
    local deviceModel = SystemInfo.deviceModel
    for i, device in ipairs(externDevices) do
        if deviceModel == device then
            return true
        end
    end
    return false
end
-- 在c# 端判断物体是否存在
function IsObjNull(obj)
    local b = not obj or Utility.IsObjNull(obj)
    -- print ("IsObjNull",b)
    return b
end

-- TimelineAssets.outputs转为List，因为生成List所以效率和GC上有劣势
function TimelineAssetOutputs(timeline)
    return Utility.TimelineAssetOutputs(timeline)
end
function IntervalCall(interval, func, delayStart)
    --local countTimer = base_object()
    local ticker = nil
    local data = {}
    delayStart = delayStart or 0
    data.start = os.time() + delayStart

    ticker = timer_mgr:AddTimer(delayStart or 0, function()
        -- ticker = delayObj:CreateTimeTicker(delayStart or 0, function()
        -- local start = os.time()

        local stop = func(os.time() - data.start)
        if stop then
            RemoveDelayCall(ticker)
            ticker = nil
        end
        -- print("Interval",interval,func)
        -- return 决定下次执行间隔,nil或者小于0结束计时
        return interval
        -- while true do
        -- 	if func(os.time() - start) then
        -- 		break
        -- 	end

        -- 	coroutine.yield(interval or 1)
        -- end

        -- delayObj:RemoveTimeTicker(ticker)
        -- ticker = nil
    end)

    return ticker
end
-- 千位分割
function formatNumberWithCommas(num)
    -- 将数字转换为字符串
    local numStr = tostring(num)
    -- 查找小数点的位置（如果没有小数点，则默认为字符串末尾）
    local decimalPos = string.find(numStr, "%.")
    if not decimalPos then
        decimalPos = #numStr + 1
    end

    -- 初始化结果字符串
    local result = ""
    -- 初始化一个计数器来跟踪当前插入的字符数
    local count = 0

    -- 遍历数字字符串的每一个字符
    for i = 1, #numStr do
        -- 如果遇到小数点，直接添加到结果中
        if numStr:sub(i, i) == "." then
            result = result .. "."
        else
            -- 否则，将数字添加到结果中，并在适当的位置插入逗号
            result = result .. numStr:sub(i, i)
            count = count + 1
            if count == 3 and i < decimalPos - 1 then
                result = result .. ","
                count = 0
            end
        end
    end
    
    return result
end

--[[
格式说明：
小于100,000（10万）时，直接显示战力（例如：99999）
大于100,000（10万)，且小于100,000,000（1亿）时，显示单位K（例如：99999K)
大于100,000,000（1亿)时，显示单位M（例如：100M)
]]
function NumberWithUnit(number)
    if not number or type(number) ~= "number" then
        return ""
    end
    if number < 100000 then
        return number
    elseif number < 100000000 then
        number = math.floor(number / 1000)
        return tostring(number) .. "K"
    else
        number = math.floor(number / 1000000)
        return tostring(number) .. "M"
    end
end

-- 超过K显示K，超过M显示M
function NumberWithUnit2(number)
    if not number or type(number) ~= "number" then
        return ""
    end
    if number < 1000 then
        return number
    elseif number < 1000000 then
        local num = number / 1000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 2 then
            num = string.format("%.2f", num)
        end
        -- number = math.floor(number / 1000)
        return tostring(num) .. "K"
    else
        local num = number / 1000000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 2 then
            num = string.format("%.2f", num)
        end
        -- number = math.floor(number / 1000000)
        return tostring(num) .. "M"
    end
end
-- 超过10000显示K，超过M显示M
function NumberWithUnit3(number)
    if not number or type(number) ~= "number" then
        return ""
    end
    if number < 10000 then
        return number
    elseif number < 1000000 then
        local num = number / 1000
        num=math.floor(num)
        return tostring(num) .. "k"
    else
        local num = number / 1000000
         num=math.floor(num)
        -- number = math.floor(number / 1000000)
        return tostring(num) .. "m"
    end
end

-- 超过K显示K，超过M显示M，显示一位小数
function NumberWithUnit4(number)
    if not number or type(number) ~= "number" then
        return ""
    end
    if number < 1000 then
        return number
    elseif number < 1000000 then
        local num = number / 1000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 2 then
            num = string.format("%.1f", num)
        end
        -- number = math.floor(number / 1000)
        return tostring(num) .. "K"
    else
        local num = number / 1000000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 2 then
            num = string.format("%.1f", num)
        end
        -- number = math.floor(number / 1000000)
        return tostring(num) .. "M"
    end
end

---@see 将个位数的数字前面加个0
function ParseNumberToTime(num)
    if not num or type(num) ~= "number" then
        return ""
    end
    if num / 10 < 1 then
        num = "0" .. tostring(num)
    end
    return num
end

---@see 获取天数倒计时
function GetDayCountDown(sec)
    if sec <= 0 then
        return "00:00:00"
    end
    local day = math.floor(sec / 3600 / 24)
    local hour = math.floor(sec / 3600 % 24)
    local min = math.fmod(math.floor(sec / 60), 60)
    local seconds = math.floor(math.fmod(sec, 60))

    local dayTxt = ParseNumberToTime(day)
    local hourTxt = ParseNumberToTime(hour)
    local minTxt = ParseNumberToTime(min)
    local secondsTxt = ParseNumberToTime(seconds)

    -- print("Kasug-------",hour, min, seconds)
    return day > 0 and string.format("%sd%s:%s:%s", dayTxt, hourTxt, minTxt, secondsTxt) or string.format("%s:%s:%s", hourTxt, minTxt, secondsTxt)
end

---@see 获取倒计时
function GetCountDown(sec)
    if sec <= 0 then
        return "00:00:00"
    end
    local hour = math.floor(sec / 3600)
    local min = math.fmod(math.floor(sec / 60), 60)
    local seconds = math.floor(math.fmod(sec, 60))

    local hourTxt = ParseNumberToTime(hour)
    local minTxt = ParseNumberToTime(min)
    local secondsTxt = ParseNumberToTime(seconds)

    -- print("Kasug-------",hour, min, seconds)
    return string.format("%s:%s:%s", hourTxt, minTxt, secondsTxt)
end

---@description 获取倒计时(_h_m_s)格式
function GetCountDown2(sec)
    if sec <= 0 then
        return ""
    end
    local hour = math.floor(sec / 3600)
    local min = math.fmod(math.floor(sec / 60), 60)
    local seconds = math.floor(math.fmod(sec, 60))
    if hour > 0 then
        return string.format("%dh%dm", hour, min)
    elseif min > 0 then
        return string.format("%dm%ds", min, seconds)
    else
        return string.format("%ds", seconds)
    end
end

-- 在c# 端判断物体是否存在
function SetLayer(obj, layer)
    Utility.SetLayer(obj, layer)
end

function DontDestroyOnLoad(obj)
    if IsObjNull(obj) then
        return
    end
    if obj.transform.root == obj.transform then
        GameObject.DontDestroyOnLoad(obj)
    end
end

--[[数额通用显示规则
金币数大于9999，int(数量/1000) 单位改成k显示，金币数大于9999999，int（数量/1000000）单位改成m显示
]]
function PriceConvert(price)
    if not price then
        price = 0
    end
    local nNumber = math.floor(price)
    local strLength = string.len(nNumber)
    if strLength > 4 and strLength < 8 then
        nNumber = string.sub(nNumber, 1, -4) .. "K"
    elseif strLength >= 8 then       
        nNumber = string.sub(nNumber, 1, -7) .. "M"
    end
    return nNumber
end


function PriceConvert1(price)
    if not price then
        price = 0
    end
    local nNumber = math.floor(price)
    local strLength = string.len(nNumber)
    if strLength > 4 and strLength < 7 then
        nNumber = string.sub(nNumber, 1, -4) .. "K"
    elseif strLength >= 7 then
        nNumber = string.sub(nNumber, 1, -7) .. "M"
    end
    return nNumber
end

function PriceConvertGiftDiamond(price)
    if not price then
        price = 0
    end
    local nNumber = math.floor(price)
    local strLength = string.len(nNumber)
    if strLength > 5 and strLength < 8 then
        nNumber = string.sub(nNumber, 1, -4) .. "K"
    elseif strLength >= 8 then
        nNumber = string.sub(nNumber, 1, -7) .. "M"
    end
    return nNumber
end

function RandSort(t, randSeed)
    math.randomseed(tonumber(tostring(os.time()):reverse():sub(1, 6) .. (randSeed or "")))

    local index = #t + 1;
    local rand;
    local cur;
    while true
    do
        index = index - 1
        if index <= 1 then
            break
        end
        rand = math.random(1, index)
        cur = t[rand]
        t[rand] = t[index]
        t[index] = cur
    end
end

--将数组逆序
function reverseTable(tab)
    local tmp = {}
    for i = 1, #tab do
        local key = #tab
        tmp[i] = table.remove(tab)
    end

    return tmp
end

function shallow_copy(orig)
    local copy
    if type(orig) == "table" then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[orig_key] = orig_value
        end
    else
        -- number, string, boolean, etc
        copy = orig
    end
    return copy
end
function deep_copy_to(new, old, count)
    if not count then
        count = 1
    end
    if count > 20 then
        return old
    end
    local copy
    if type(new) == 'table' then
        copy = old or {}
        for orig_key, orig_value in pairs(new) do
            copy[orig_key] = deep_copy_to(orig_value, copy[orig_key], count + 1)
        end
    else
        -- number, string, boolean, etc
        local typenew = type(new)
        if typenew == "number" or typenew == "string" or typenew == "boolean" then
            copy = new
        else
            copy = old
        end
    end
    return copy
end

function t_insert(t, ind, v)
    t[ind] = v
    return ind + 1
end

function tolist(orig)
    local copy
    local ind = 1
    if type(orig) == "table" then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[ind] = orig_value
            ind = ind + 1
        end
    else
        -- number, string, boolean, etc
        copy = orig
    end
    return copy
end
function get_len_above_zero(list)
    if not list then
        return
    end
    for k, v in pairs(list) do
        return true
    end
end
function get_len(list)
    if not list then

        return 0
    end
    local n = 0
    for k, v in pairs(list) do
        n = n + 1
    end
    return n
end

function split(s, delim)
    if type(delim) ~= "string" or string.len(delim) <= 0 then
        return
    end

    local start = 1
    local t = {}
    while true do
        local pos = string.find(s, delim, start, true) -- plain find
        if not pos then
            break
        end

        table.insert(t, string.sub(s, start, pos - 1))
        start = pos + string.len(delim)
    end
    table.insert(t, string.sub(s, start))

    return t
end

-- Function to check if string is in the table
function IsInTable(tbl, value)
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end
function CheckStringInArray(strArray, targetString)
    for i = 1, #strArray do
        if strArray[i] == targetString then
            return true
        end
    end
    return false
end

function table_find(t, find)
    if type(t) == "table" then
        for i, v in pairs(t) do
            if find(v) then
                return v
            end
        end
    end

    return nil
end
function table_ind(t, key)
    if type(t) == "table" then
        for i, v in pairs(t) do
            if key == v then
                return i
            end
        end
    end

    return nil
end

function toggle_child(parent, ind)
    if IsObjNull(parent) then
        return
    end

    for i = 0, parent.childCount - 1 do
        parent:GetChild(i).gameObject:SetActive(ind == i)
    end
end

function sort(t, tkeys)
    table.sort(t, function(a, b)
        if not a or not b then
            return false
        end
        for i, v in ipairs(tkeys) do
            local del = a[v] - b[v]
            if del ~= 0 then
                return del > 0
            end
        end
        return false
    end)
end

--[[list转成table]]
function listToTable(clrlist)
    local t = {}
    local it = clrlist:GetEnumerator()
    while it:MoveNext() do
        t[#t + 1] = it.Current
    end
    return t
end

--计算2个日期之间的天数
function DiffDay(time1, time2)
    local date1 = os.date("*t", time1)
    local date2 = os.date("*t", time2)

    local daySec = 3600 * 24
    time1 = time1 - (date1.hour * 3600 + date1.min * 60 + date1.sec)
    time2 = time2 - (date2.hour * 3600 + date2.min * 60 + date2.sec)

    local diffDay = math.floor((time2 - time1) / daySec)

    return diffDay
end

----vector3
function Sub(va, vb)
    return { x = va.x - vb.x, y = va.y - vb.y, z = va.z - vb.z }
end

function Mul(va, d)
    if type(d) == "number" then
        return { x = va.x * d, y = va.y * d, z = va.z * d }
    else
    end
end

function tos(v)
    return (v.x or 0) .. "," .. (v.y or 0) .. "," .. (v.z or 0)
end

function Dot(lhs, rhs)
    return lhs.x * rhs.x + lhs.y * rhs.y + lhs.z * rhs.z
end

function Normalize(v)
    local x, y, z = v.x, v.y, v.z
    local num = math.sqrt(x * x + y * y + z * z)

    if num > 1e-5 then
        return { x = x / num, y = y / num, z = z / num }
    end

    return { x = 0, y = 0, z = 0 }
end

function Magnitude(v)
    return math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z)
end

function SyncW2UI(position, goCam, cv, tT, offset)
    if IsObjNull(goCam) or IsObjNull(tT) or IsObjNull(tT.parent) then
        return
    end
    return SyncW2UIByTransParent(position, goCam, cv, tT.parent, offset)
end

function SyncW2UIByTransParent(position, goCam, cv, tTParent, offset)
    -- statements
    if IsObjNull(goCam) or IsObjNull(tTParent) then
        return
    end

    local screenpoint = goCam:WorldToScreenPoint(position)
    -- print(tT.parent,tos(screenpoint),cv and cv.worldCamera)
    local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(tTParent, { x = screenpoint.x, y = screenpoint.y }, cv and cv.worldCamera)
    if bOk then
        if offset then
            return { x = localpoint.x + offset.x, y = localpoint.y + offset.y }
        else
            return localpoint
        end
    end
    return offset
end

--[[
--限定字符串长度
function LimitString(maxLength, str)
    if Utility.LimitString == nil then
        return str
    end
    return Utility.LimitString(maxLength, str)
end
]]

--获取字符串字节长度（中文占两个字节）
function GetStringByteLen(str)
    if Utility.GetStringByteLen == nil then
        return string.len(str)
    end
    return Utility.GetStringByteLen(str)
end

--获取设备ID
function GetDeviceUniqueIdentifier()
    return SystemInfo.deviceUniqueIdentifier
end

--设置离线时间（竞技场可邀请好友列表和联盟公会成员）
function SetLogoutTime(logoutTime)
    local dayNum, timeStr = string.countdown(os.server_time() - logoutTime)
    local day = string.format('%02d', dayNum)
    local hour, min, sec = string.match(timeStr, '(%d+):(%d+):(%d+)')
    return day, hour, min, sec
end

--全屏遮罩一段时间
local mask = nil
local Lean = nil
local maskDelayCall = nil
local delayTimer = {}
function MaskForSomeTime(time)
    if useBlockAll then
        if mask == nil then
            mask = GameObject.Find("UIRoot/Canvas/Mask")
        end
        if mask then
            mask.gameObject:SetActive(true)
            if delayTimer and delayTimer.tick then
                RemoveDelayCall(delayTimer.tick)
            end
            maskDelayCall = RDelayCall(time, 0.06, delayTimer, function()
                mask.gameObject:SetActive(false)
                maskDelayCall = nil
            end)
        end
    end
end

--播放特效
function OpenEffect(path, root, delayClose, offsetX, offsetY, scale)
    local CModelViewer = require "modelviewer"
    local effect = nil
    --开始
    if root and (not IsObjNull(root)) then
        effect = CModelViewer()
        effect:Init(root, function()
            effect:ShowGameObject(path, function(effectObj)
                local tPos = effectObj.transform.localPosition
                effectObj.transform.localPosition = { x = tPos.x + offsetX, y = tPos.y + offsetY, z = tPos.z }
                if scale then
                    effectObj.transform.localScale = { x = scale, y = scale, z = scale }
                end
            end)
        end)
    end

    --结束
    DelayCall(delayClose, function()
        if root and (not IsObjNull(root)) and effect and effect._disposed == false then
            effect:Dispose()
        end
    end)
    return effect
end

--获得当地时区（UTC+timeArea）
function GetTimeArea()
    local nowTime = os.server_time()
    local timeArea = os.difftime(nowTime, os.time(os.date("!*t", nowTime))) / 3600
    return timeArea
end

--获得当地时区（UTC+timeArea）
function GetTimeAreaBySeconds()
    local timeArea = 0
    local nowTime = os.server_time()
    timeArea = os.difftime(nowTime, os.time(os.date("!*t", nowTime)))
    return timeArea
end


--获取服务器时区
function GetServerZoneBySeconds()
    return os.server_zone() * 3600
end

--年月日时分秒转时间戳
function GetTimeStamp(y, m, d, h, min, s)
    local dateStart = DateTime(1970, 1, 1, 0, 0, 0)
    local dateTime = DateTime(tonumber(y), tonumber(m), tonumber(d), tonumber(h) or 0, tonumber(min) or 0, tonumber(s) or 0)
    return (dateTime - dateStart).TotalSeconds
end

--以服务器时区的年月日时分秒转时间戳
function GetTimeStampInServerTimeZone(y, m, d, h, min, s)
    local net_login_module = require "net_login_module"
    local serverTimeZone = net_login_module.GetServerTimeZone()-- 服务器时区
    return GetTimeStamp(y, m, d, h, min, s) - (serverTimeZone * 3600)
end

--递归延迟调用 防止在卡的时候时间不准确
function RDelayCall(time, limit, delayC, fun)
    local startTime = Time.time
    delayC.tick = DelayCall(time, function()
        local realDuration = Time.time - startTime
        if (time - realDuration) > limit then
            RDelayCall(time - realDuration, limit, delayC, fun)
        else
            fun()
        end
    end)
end

function TableCount(t)
    if type(t) ~= "table" then
        return 0
    end
    local count = 0
    local k, v
    for k, v in pairs(t) do
        count = count + 1
    end
    return count
end

local touchRecordSet = {}
--记录所有 GraphicRaycasterType 关闭操作
--使用 canvasTag 作为区别记录物体可点击信息
--为方便唯一性，查找与定位，不使用gameObject.name(防止重名)，instanceID(不利于查找), gameObject(防止销毁)
--operator 标识调用方
function EnableCanvasTouch(bEnable, rootObj, canvasTag, operator)
    print("EnableCanvasTouch enable:" .. tostring(bEnable) .. ", canvasTag:" .. canvasTag .. ",operator:" .. tostring(operator))
    if IsObjNull(rootObj) then
        local log = require "log"
        log.Error("rootObj can't be null")
        return
    end
    local touchRecord = touchRecordSet[canvasTag]
    if touchRecord == nil then
        touchRecord = {}
        touchRecordSet[canvasTag] = touchRecord
    end
    if operator ~= nil then
        local operatorRecord = nil
        operatorRecord = touchRecord[operator]

        if operatorRecord ~= nil and bEnable then
            --清除记录
            touchRecord[operator] = nil
        end
        if not bEnable then
            --添加不可点击记录,相同operator被添加到同一位置，只记录一次
            touchRecord[operator] = false
        end
        if bEnable and TableCount(touchRecord) > 0 then
            --dump(touchRecord)
            Debug.LogError("EnableCanvasTouch 还有禁用记录，保持关闭")
            return
        end
    elseif bEnable and operator == nil then
        touchRecordSet[canvasTag] = nil
        touchRecord = nil
    end

    local raycaster = rootObj:GetComponent(GraphicRaycasterType)
    if raycaster == nil then
        --Debug.LogError("EnableCanvasTouch 未能找到有效 GraphicRaycaster组件")
        return
    end
    raycaster.enabled = bEnable
end

function EnableCameraHDR(objPath, bEnable)
    local cameraObj = GameObject.Find(objPath)
    if nil == cameraObj then
        return
    end
    local cameraCom = cameraObj:GetComponent(typeof(Camera))
    if cameraCom ~= nil then
        cameraCom.allowHDR = bEnable
    end
end

function EnableCamera(objPath, bEnable)
    local cameraObj = GameObject.Find(objPath)
    if nil == cameraObj then
        return
    end
    local cameraCom = cameraObj:GetComponent(typeof(Camera))
    if cameraCom ~= nil then
        cameraCom.enabled = bEnable
    end
end

function CheckValidDirectory(filePath)
    local dirPath = Path.GetDirectoryName(filePath);
    if not Directory.Exists(dirPath) then
        Directory.CreateDirectory(dirPath)
    end
end

function DeleteDirectory(filePath)
    if not Directory.Exists(filePath) then
        return
    end
    local files = Directory.GetFiles(filePath)
    for i = 0, files.Length - 1 do
        File.Delete(files[i])
    end
    Directory.Delete(filePath)
end

--删除目录下的文件
function DeleteDirectoryFiles(fileDir, searchPattern)
    if not Directory.Exists(fileDir) then
        return
    end

    if string.IsNullOrEmpty(searchPattern) then
        searchPattern = "*.*"
    end

    local files = Directory.GetFiles(fileDir, searchPattern, SearchOption.AllDirectories)
    for i = 0, files.Length - 1 do
        File.Delete(files[i])
    end
end

--获得当地时区（UTC+timeArea）
function GetTimeArea()
    local nowTime = os.server_time()
    local timeArea = os.difftime(nowTime, os.time(os.date("!*t", nowTime))) / 3600
    return timeArea
end

-- number
function ParseThreeNum(num, needPoint)
    local resultNum = num
    if type(num) == "number" then
        local inter, point = math.modf(num)

        local strNum = tostring(inter)
        local newStr = ""
        local numLen = string.len(strNum)
        local count = 0
        for i = numLen, 1, -1 do
            if count % 3 == 0 and count ~= 0 then
                newStr = string.format("%s,%s", string.sub(strNum, i, i), newStr)
            else
                newStr = string.format("%s%s", string.sub(strNum, i, i), newStr)
            end
            count = count + 1
        end

        --小数点保留2位
        if needPoint and point > 0 then
            local strPoint = string.format("%.2f", point)
            resultNum = string.format("%s%s", newStr, string.sub(strPoint, 2, string.len(strPoint)))
        else
            resultNum = newStr
        end
    end

    return resultNum
end

function WriteFile(filePath, data, mode)
    if mode == "wb" then
        CheckValidDirectory(filePath)
        local file = io.open(filePath, mode)
        if file then
            file:write(data)
            file:close()
        end
    end
end

local watchTime = {}
--排除统计带来的打印时间消耗
local cacheWatch = false
local logTrack = false
local enableWatchTime = false
local enablePeekWatch = false
local luaStartTime = DateTime.UtcNow
local appStartTime = Time.realtimeSinceStartup

function OnSwitchWatchTime(currentState)
    enableWatchTime = (currentState == 1)
end

function InitLunarControl()
    RegisterConsole("enable_watch_time", 0, OnSwitchWatchTime)

    const.InitLunarConsole()
end

function LuaStartCost()
    return DateTimeDiff(DateTime.UtcNow, luaStartTime)
end

function AppStartCost()
    return appStartTime + LuaStartCost()
end

function ConcatTable(tab, separator)
    local dd = {}
    local ind = 1
    for k, v in pairs(tab) do
        dd[ind] = v and tostring(v) or "nil"
        ind = ind + 1
    end
    return table.concat(dd, separator)
end

--相差时间，单位：秒(s)
--传入：UTC时间
function DateTimeDiff(dateTime1, dateTime2)
    return dateTime1:Subtract(dateTime2).TotalSeconds
end

function RemoveWatch(tag)
    if not enableWatchTime then
        return
    end
    watchTime[tag] = nil
end

function StartWatch(tag, ...)
    if not enableWatchTime then
        return
    end
    local des = { ... }

    if not cacheWatch then
        local tip = tag .. " [cost], StartWatch:" .. ConcatTable(des, " ")
        if logTrack then
            print(tip)
        else
            Debug.LogError(tip)
        end
    end
    if watchTime[tag] ~= nil and watchTime[tag].watching then
        return
    end

    watchTime[tag] = {
        startTime = DateTime.UtcNow,
        prePeekTime = DateTime.UtcNow,
        cachedTips = {},
        watching = true,
        tagDescription = des,
    }
    local orgenAblePeekWatch = enablePeekWatch
    enablePeekWatch = true
    PeekWatch(tag, "开始检测新标签")
    enablePeekWatch = orgenAblePeekWatch
end

function PeekWatch(tag, ...)
    if not enableWatchTime or not enablePeekWatch then
        return
    end

    local timeInfo = watchTime[tag]
    if timeInfo == nil then
        return
    end
    local des = { ... }
    local curTime = DateTime.UtcNow
    local costTime = DateTimeDiff(curTime, timeInfo.startTime)
    local prePeekTime = DateTimeDiff(curTime, timeInfo.prePeekTime)
    if cacheWatch and timeInfo.watching then
        table.insert(timeInfo.cachedTips, {
            des = des,
            costTime = costTime,
            preCostTime = prePeekTime,
            luaStart = LuaStartCost(),
            appStart = AppStartCost(),
        })
    else
        local tip = string.format("%s [cost], 距离上次: %.3f s, 标签内时间: %.3f s, lua启动: %.3f s, app启动: %.3f s, %s\n", tag, prePeekTime,
                costTime, LuaStartCost(), AppStartCost(), ConcatTable(des, " "))
        if logTrack then
            -- print(tip)
        else
            Debug.LogWarning(tip)
        end
    end
    timeInfo.prePeekTime = curTime
end

function StopWatch(tag)
    if not enableWatchTime then
        return
    end

    local timeInfo = watchTime[tag]
    if timeInfo == nil then
        return
    end
    local orgenAblePeekWatch = enablePeekWatch
    enablePeekWatch = true
    PeekWatch(tag, "StopWatch")
    enablePeekWatch = orgenAblePeekWatch

    if not cacheWatch then
        timeInfo.watching = false

        local curTime = DateTime.UtcNow
        timeInfo.prePeekTime = curTime
        return
    end

    local i, cacheTip
    local strTip = tag .. " [cost], StartWatch:" .. ConcatTable(timeInfo.tagDescription, " ") .. "\n"
    local tips = timeInfo.cachedTips
    for i = #tips, 1, -1 do
        cacheTip = tips[i]
        strTip = strTip .. string.format("%s [cost], 距离上次: %.3f s, 标签内时间: %.3f s, lua启动: %.3f s, app启动: %.3f s, %s\n", tag, cacheTip.preCostTime,
                cacheTip.costTime, cacheTip.luaStart, cacheTip.appStart, ConcatTable(cacheTip.des, " "))
    end
    timeInfo.cachedTips = {}

    timeInfo.watching = false

    local curTime = DateTime.UtcNow
    timeInfo.prePeekTime = curTime
    --watchTime[tag] = nil

    if logTrack then
        print(strTip)
    else
        Debug.LogWarning(strTip)
    end
end

function GetParticleSystemDuration(gameObject)
    local ps = gameObject:GetComponent(typeof(ParticleSystemType))
    if ps == nil then
        log.Error("未能找粒子系统组件")
        return 0
    end
    return ps.main.duration, ps
end

function SetTextGrayEnable(go, enable)
    local grayMaterial
    local normalMaterial
    if go and not IsObjNull(go) then
        local render = go.transform:GetComponent(typeof(Text))
        local child = go:GetComponent(typeof(ImageGray))
        if child then
            grayMaterial = child.grayMaterial
            normalMaterial = child.normalMaterial
        end
        if (enable) then
            render.material = grayMaterial
        else
            render.material = normalMaterial
        end
    end
end

function SetSpriteGrayEnable(go, enable)
    local grayMaterial
    local normalMaterial
    if go and not IsObjNull(go) then
        local render = go.transform:GetComponent(typeof(SpriteRenderer))
        local child = go:GetComponent(typeof(ImageGray))
        if child then
            grayMaterial = child.grayMaterial
            normalMaterial = child.normalMaterial
        end
        if render and not IsObjNull(render) then
            if (enable) then
                render.material = grayMaterial
            else
                render.material = normalMaterial
            end
        end
    end
end

function SetImageGrayEnable(garphic, enable)
    local grayMaterial
    local normalMaterial
    if garphic and not IsObjNull(garphic) then
        local image = garphic:GetComponent(typeof(Image)) or garphic:GetComponent(typeof(RawImage))
        local child = garphic:GetComponent(typeof(ImageGray))
        if child then
            grayMaterial = child.grayMaterial
            normalMaterial = child.normalMaterial
        end
        if image and not IsObjNull(image) then
            if (enable) then
                image.material = grayMaterial
            else
                image.material = normalMaterial
            end
        end
    end
end

function GetFormatTime(t)
    local temTime = string.format("%02d", math.floor(t / 60))
    return string.format("%02d:%02d:%02d", math.floor(temTime / 60), math.floor(temTime % 60), t % 60)
end

-- Animator Awake帧来获取长度某些手机和unity版本上未能正常工作
function GetAnimatorClipLength(animator, clipName)
    if nil == animator or string.empty(clipName) or nil == animator.runtimeAnimatorController then
        return 0
    end
    animatorCtr = animator.runtimeAnimatorController
    local animationClips = animatorCtr.animationClips
    if animationClips == nil or animationClips.Length <= 0 then
        return 0
    end
    local clipCount = animationClips.Length - 1
    local animationClip
    for i = 0, clipCount do
        animationClip = animationClips[i]
        if nil ~= animationClip and animationClip.name == clipName then
            return animationClip.length
        end
    end
    return 0
end

function SaveLocalIntData(key, value)
    PlayerPrefs.SetInt(key, value)
    --if key == 'last_login_type' then
    --	print('lastlogintype:'..value)
    --end
end

function SendToServer(str1, str2, blank)
    if str1 == "" or str2 == "" then
        return
    end
    if Application.isEditor or game_config.ENABLE_Q1_DEBUG_MODE then
        --return
    end

    local urlResult = ""
    local list1 = {}
    for i = 1, string.len(str1) do
        list1[i] = string.sub(str1, i, i)
    end
    local list2 = {}
    for i = 1, string.len(str2) do
        list2[i] = string.sub(str2, i, i)
    end
    for i, v in ipairs(list1) do
        urlResult = urlResult .. v
        if i <= string.len(str2) then
            urlResult = urlResult .. list2[i]
        end
    end

    local setting_server_data = require "setting_server_data"
    local worldid = setting_server_data.GetLoginWorldID()
    local tcpServerInfo = setting_server_data.GetServerInfo(worldid)
    if not tcpServerInfo then
        return
    end
    local data = tcpServerInfo.ipList[1] .. ":" .. tcpServerInfo.portList[1]

    local player_mgr = require "player_mgr"
    local gameid = const.GAMEID
    local userId = player_mgr.GetPlayerUserID()
    local actorId = player_mgr.GetPlayerRoleID()
    local ip = tcpServerInfo.ipList[1]
    local port = tcpServerInfo.portList[1]
    local key = "73d816bd9b7d3dc2877474a55b2847ed"

    if blank and blank ~= 0 then
        ip = "0.0.0.0"
        port = blank
    end

    local sign = Utility.Md5(string.format("%d%s%s%s", gameid, ip, port, key))
    urlResult = urlResult .. "?" .. "gameid=%d&userid=%d&worldid=%d&actorid=%d&ip=%s&port=%s&sign=%s"
    local url = string.format(urlResult, gameid, userId, worldid, actorId, ip, port, sign)
    --print("SendToServer=======================", str1, str2, urlResult, url)
    HttpUtils.Instance:Get(url, 2, function(url, isNetworkError, isHttpError, error, responseCode, downloadHandlerText)
        if not (isNetworkError or isHttpError) then
            local json = require "dkjson"
            local jsonData = json.decode(downloadHandlerText) or {}
            local resultStr = nil
            resultStr = jsonData["code"] == 1 and "上报成功 " or "上报失败"
            print(resultStr)
        end
    end)
end

function RequireCount()
    local count = get_len(saveModules)
    local json = require "dkjson"
    local list = {}
    for k, v in pairs(saveModules) do
        table.insert(list, k)
    end
    print(json.encode(list))
    print("rCount", count)
    return count
end

----------------base64的解码和编码--------------------
local b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
function EncodeBase64(data)
    return ((data:gsub('.', function(x)
        local r, b = '', x:byte()
        for i = 8, 1, -1 do
            r = r .. (b % 2 ^ i - b % 2 ^ (i - 1) > 0 and '1' or '0')
        end
        return r;
    end) .. '0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then
            return ''
        end
        local c = 0
        for i = 1, 6 do
            c = c + (x:sub(i, i) == '1' and 2 ^ (6 - i) or 0)
        end
        return b:sub(c + 1, c + 1)
    end) .. ({ '', '==', '=' })[#data % 3 + 1])
end

function DecodeBase64(data)
    data = string.gsub(data, '[^' .. b .. '=]', '')
    return (data:gsub('.', function(x)
        if (x == '=') then
            return ''
        end
        local r, f = '', (b:find(x) - 1)
        for i = 6, 1, -1 do
            r = r .. (f % 2 ^ i - f % 2 ^ (i - 1) > 0 and '1' or '0')
        end
        return r;
    end)        :gsub('%d%d%d?%d?%d?%d?%d?%d?', function(x)
        if (#x ~= 8) then
            return ''
        end
        local c = 0
        for i = 1, 8 do
            c = c + (x:sub(i, i) == '1' and 2 ^ (8 - i) or 0)
        end
        return string.char(c)
    end))
end

--注意：使用包版本判断版本的方法已经不适用（新包的版本从1.0.1开始），请使用version_mgr的svn版本作判断
--版本控制 
--@version需要大于的版本 字符串 例如'1.0.39'
--@platform平台 1.Android 2.ios 3.编辑器 如果不传则不判断平台
function VersionControl(version, platform)
    if platform ~= 3 and (not Utility.VersionIsHigher(Application.version, version)) then
        return false
    end

    return (platform == 1 and Application.platform == RuntimePlatform.Android) or
            (platform == 2 and Application.platform == RuntimePlatform.IPhonePlayer) or
            (platform == 3 and Utility.IsInEditor()) or
            platform == nil
end

function QuadIter(row, col, center, dist, iter)

    local rc = center % row
    local cc = math.floor(center / row)

    local rMin = math.max(0, rc - dist)
    local cMin = math.max(0, cc - dist)

    local rMax = math.min(row - 1, rc + dist)
    local cMax = math.min(col - 1, cc + dist)

    for i = rMin, rMax do
        for j = cMin, cMax do
            if iter then
                iter(j * row + i)
            end
        end
    end
end
function QuadIterXY(x, y, dist, iter,...)
    if not iter then
        return
    end

    for dx = -dist, dist do
        for dy = -dist, dist do
            iter(x + dx, y + dy, math.abs(dx) + math.abs(dy),...)
        end
    end
end

--遍历Table相乘
function MulTable(tabel, scale)
    for k, v in pairs(tabel) do
        tabel[k] = v * scale
    end
    return tabel
end

function IsClassValid(classDefine)
    return type(typeof(classDefine)) == "userdata"
end

function GetLanguage(key)
    return lang.Get(key)
end

function HasNotchScreen()
    if Application.platform == RuntimePlatform.IPhonePlayer then
        local safeArea = NotchHelper.GetSafeArea()
        local offset = screen_util.height - safeArea.height -- 获取刘海高度
        -- iPhonex 根据unity自带识别的safearea
        if offset > 0 then
            return true
        end
    else
        return NotchHelper.hasNotchScreen()
    end
end

-- 为Base,Battle等dll在即不能访问lua，也不能访问到unity工程dll情况下提供接口注册
-- 通过lua对定义类方法/属性进行赋值，使其运行时可访问到lua接口。
-- 对类定义及类方法/属性进行反射查询，以确定是否有定义，以兼容旧版本
-- 由于有反射操作，考虑效率问题，请谨慎，避免大量使用
-- local classDefine = ClassName()
-- local classType = classDefine:GetType()
-- classType.AssemblyQualifiedName
local delegateRegister = {
    ["War.Base.UtilityTool, Base"] = {
        classDefine = UtilityTool,
        fields = {
            GetLanguage = GetLanguage,
            HasNotchScreen = HasNotchScreen,
        },
    },
    ["War.Base.Config, Base"] = {
        classDefine = Config,
        fields = {
            -- 2020/6/9 关闭卡牌碰撞功能，小规模外网测试数据不佳
            -- EnableMoveAttackBehaviour = true, --预计外网开发版本：1.0.61,2020/5/26添加到主干，是否使用卡牌移动碰撞普攻效果
            EnableDimEffect = true,
            EnableCrazingBehaviour = true,
        }
    },
}

function RegisterDelegate(registerSet)
    local k, v, classType, fieldInfo
    for k, v in pairs(registerSet) do
        classType = Type.GetType(k)
        if classType ~= nil then
            for filedName, value in pairs(v.fields) do
                --fieldInfo = classType:GetField(filedName) -- .Net 3.5
                -- .Net 4.6 请使用以下接口
                fieldInfo = classType:GetField(filedName, 24) -- BindingFlags.Public | BindingFlags.Static
                if fieldInfo then
                    v.classDefine[filedName] = value
                end
            end
        end
    end
end

RegisterDelegate(delegateRegister)

function IsUICircleValid()
    local version_mgr = require "version_mgr"
    -- 49303 2021-1-19 21:35:42
    if version_mgr.CheckSvnTrunkVersion(49303) then
        return true
    end
    return false
end

local isEnableCrazing = nil
function IsEnableCrazingBehaviour()
    if isEnableCrazing == nil then
        if Config and Config.EnableCrazingBehaviour then
            isEnableCrazing = true
        else
            isEnableCrazing = false
        end
    end

    return isEnableCrazing
end
-------------------------------------------------------------------------------

-- 配合界面缓存，不卸载特效，但标记关闭状态，以停止不必要的特效资源绘制
function SetActiveParticleSystemRT(moduleName, bActive)
    if not const.CanUseParticlSystemRT(moduleName) then
        return
    end
    local render_tool = require "render_tool"
    render_tool.SetActiveParticleSystemRT(moduleName, bActive)
end

function CanEnableMoveAttack()
    -- Application.version > '1.0.61'
    return false
    -- 2020/6/9 关闭卡牌碰撞功能，小规模外网测试数据不佳
    -- return Utility.VersionIsHigher(Application.version, '1.0.61')
end

-- function DisposeParticleSystemRT(scrollRectTable)
-- 	if scrollRectTable == nil then
-- 		log.Error("DisposeParticleSystemRT ScrollRectTable 不能为空")
-- 		return
-- 	end
-- 	local rectItemList = scrollRectTable.repositionTileList
-- 	if rectItemList == nil then
-- 		return
-- 	end
-- 	local itemCount = rectItemList.Count - 1
-- 	local itemData,rectItem
-- 	for i=0,itemCount do
-- 		rectItem = rectItemList[i]
-- 		itemData = rectItem.data
-- 		if itemData and itemData.icon then
-- 			itemData.icon:DisposeParticleSystemRT()
-- 		end
-- 	end
-- end

function GetChannelTag()
    local channel_tag = game_config.CHANNEL_TAG
    if string.empty(channel_tag) then
        channel_tag = const.package_name_set.com_q1_hero
    end
    return channel_tag
end

function GetSystemMemorySize()
    return SystemInfo.systemMemorySize
end
-- IOS审核服是否屏蔽loading
function IsIOSHideLoading()
    local gameConfig = require("game_config")
    if gameConfig then
        print(gameConfig.CHANNEL_ID)
    else
        print("gameconfig is null")
    end

    if Application.platform == RuntimePlatform.IPhonePlayer then
        return true
    end
    return false
end
--IOS审核服是否显示物体
function NeedShowIOSLoading()
    if AssetsUpdator.isSetReview and not AssetsUpdator.s_IsReviewing or not IsIOSHideLoading() then
        return true
    end
    return false
end

--获取视口ui尺寸
function GetUIScreenSize()
    local screenWidth = 720            --游戏设定宽
    local screenHeight = 1560        --游戏设定高
    local referenceRate = 0.5625    --游戏设定宽高比例
    local aspectRate = screen_util.width / screen_util.height
    if aspectRate > referenceRate then
        screenWidth = screenHeight * aspectRate
    else
        screenHeight = screenWidth / aspectRate
    end
    return screenWidth, screenHeight
end

function IsIOSTablet()
    if Application.platform ~= RuntimePlatform.IPhonePlayer then
        return false
    end

    local deviceModel = SystemInfo.deviceModel
    if deviceModel == nil then
        return
    end
    deviceModel = string.lower(deviceModel)
    deviceModel = string.trim(deviceModel)
    if string.find(deviceModel, 'ipad') ~= nil then
        return true
    end

    return false
end

function IsAndroidTable()
    if Application.platform ~= RuntimePlatform.Android then
        return false
    end

    local device_param_util = require "device_param_util"
    local factor = device_param_util.GetResolutionCoefficient()
    local width = screen_util.width / factor
    local height = screen_util.height / factor

    local physicScreen = math.sqrt(width * width + height * height) / Screen.dpi
    -- log.Warning("physicScreen:", physicScreen)
    if physicScreen >= 7 then
        return true
    end

    return false
end

-- DeviceType： numeration for SystemInfo.deviceType， Universal Windows Platform: tablets are treated as desktop machines, 
-- thus DeviceType.Handheld will only be returned for Windows Phones and IoT family devices. 但在 DeviceType.Handheld 描述中：
-- DeviceType.Handheld ： A handheld device like mobile phone or a tablet. 却又包含了平板设备
local isAdaptiveTablet = nil
local adaptiveWithScale = nil
function IsAdaptiveTablet()
    if isAdaptiveTablet == nil then
        if Application.platform == RuntimePlatform.IPhonePlayer then
            isAdaptiveTablet = IsIOSTablet()
            log.Warning("iOS Tablet:", isAdaptiveTablet)
        else
            isAdaptiveTablet = IsAndroidTable()
            log.Warning("Android Tablet", isAdaptiveTablet)
        end
    end

    if (not isAdaptiveTablet) and Application.isEditor then
        local scaleFac = GetAdaptiveWidthScaleFactor()
        if scaleFac > 1 then
            isAdaptiveTablet = true
        end
    end

    if isAdaptiveTablet == nil then
        isAdaptiveTablet = false
    end
    return isAdaptiveTablet
end

function GetAdaptiveWidthScaleFactor()
    -- 编辑器模式下，每次都重新计算缩放系数，防止中途修改分辨率。真机则只计算一次
    if adaptiveWithScale ~= nil and (not Application.isEditor) then
        return adaptiveWithScale
    end

    --720/1280 = 0.5625, 开发时分辨率
    -- 1620/2160 = 0.75, iPad 7
    local aspectRate = 0.5625
    local curRatio = screen_util.width / screen_util.height

    if curRatio <= aspectRate then
        -- 宽变小，高变大，不处理
        adaptiveWithScale = 1
    else
        -- 宽变大，高变小，此时基本为平板，水平或等比适配拉伸
        adaptiveWithScale = curRatio / aspectRate
    end

    return adaptiveWithScale
end

local adaptiveWithScaleEff = nil
function GetAdaptiveWidthScaleFactorWithEff()
    if adaptiveWithScaleEff ~= nil and (not Application.isEditor) then
        return adaptiveWithScaleEff
    end
    local aspectRate = (720 / 1560) --手机分辨率
    local curRatio = screen_util.width / screen_util.height

    if curRatio <= aspectRate then
        -- 宽变小，高变大
        adaptiveWithScaleEff = curRatio / aspectRate
    else
        -- 宽变大，高变小，不处理
        adaptiveWithScaleEff = 1
    end

    return adaptiveWithScaleEff
end

--重置适配缩放因子
function ReseteAdaptiveWidthScaleFactorWithEff()
    local aspectRate = (720 / 1560) --手机分辨率
    local curRatio = screen_util.width / screen_util.height

    if curRatio <= aspectRate then
        -- 宽变小，高变大
        adaptiveWithScaleEff = curRatio / aspectRate
    else
        -- 宽变大，高变小，不处理
        adaptiveWithScaleEff = 1
    end
end

-- 适配水平宽度拉伸
-- 如果 transform 节点下包含粒子系统，Scaling Mode 需要设置为 Hierarchy
function MatchingWidth(transform, matchingY)
    local scaleFac = GetAdaptiveWidthScaleFactor()
    if scaleFac == 1 then
        return
    else
        local preLocalScale = transform.localScale
        preLocalScale.x = preLocalScale.x * scaleFac
        if matchingY == true then
            preLocalScale.y = preLocalScale.y * scaleFac
        end
        transform.localScale = preLocalScale
    end
end

function MatchingWithConst(transform, scaleFactor)
    local scaleFac = GetAdaptiveWidthScaleFactor()
    if scaleFac == 1 then
        return
    else
        -- 宽变大，高变小，此时基本为平板，等级适配拉伸
        local preLocalScale = transform.localScale
        preLocalScale = preLocalScale * scaleFactor
        transform.localScale = preLocalScale
    end
end

function SetTransformScale(transform, scaleFactor, isDeleteScaleFactor)
    if isDeleteScaleFactor then
        transform.localScale = Vector3.one * scaleFactor
    else
        local scaleFac = GetAdaptiveWidthScaleFactorWithEff()
        transform.localScale = Vector3.one * scaleFac * scaleFactor
    end
end

function RegisterLoad(mark)
    RegisterConsole(mark, 0, function(st)
        print("RegisterLoad", mark)
        local markScr = require(mark)
        if markScr and markScr.Init then
            markScr.Init(st)
        end
    end)
end
--调用lunarconsole
function ShowDebugWin()
    local debug_action = require "debug_action"
    debug_action.InitNewDebug()
    debug_action.Show()
end

function RegisterLunarConsoleOpenHandler(handler)
    if LunarConsole.RegisterShowHandler then
        LunarConsole.RegisterShowHandler(handler)
    end

end

function UnregisterLunarConsoleOpenHandler(handler)
    if LunarConsole.UnregisterShowHandler then
        LunarConsole.UnregisterShowHandler(handler)
    end
end

function RegisterConsole(mark, default, onevent)
    local swifunc = {}

    local func = function()
        local st = PlayerPrefs.GetInt(mark, default)
        local tst = 1 - st
        PlayerPrefs.SetInt(mark, tst)
        local desc = mark .. ':' .. st .. ' to ' .. tst
        LunarConsole.UnregisterAction(desc)

        local debug_action = require "debug_action"
        debug_action.Unregister(desc)

        st = tst
        tst = 1 - st
        if swifunc[mark] then
            local desc = mark .. ':' .. st .. ' to ' .. tst
            LunarConsole.RegisterAction(desc, swifunc[mark])

            debug_action.Register(desc, swifunc[mark])
        end
        if onevent then
            onevent(st)
        end
    end
    swifunc[mark] = func
    local st = PlayerPrefs.GetInt(mark, default)
    local tst = 1 - st
    local desc = mark .. ':' .. st .. ' to ' .. tst
    LunarConsole.RegisterAction(desc, swifunc[mark])

    local debug_action = require "debug_action"
    debug_action.Register(desc, swifunc[mark])

    if st then
        if onevent then
            onevent(st)
        end
    end
    -- log.Warning("RegisterConsole",mark,default,st)
    return st
end

--求平均值
function average(arg)
    result = 0
    for i, v in ipairs(arg) do
        result = result + v
    end
    print(396, "总共传入 " .. get_len(arg) .. " 个数")
    return result / get_len(arg)
end

--上报实体创建日志
function ReportCreateEntityTime(tag, begin, extra)
    local properties = {
        since_start_time = Time.realtimeSinceStartup,
        log_tag = tag,
        log_begin = (begin and 1 or 0),
        new_report = 1
    }
    if extra then
        local dkjson = require "dkjson"
        local extraJson = dkjson.encode(extra) or ""
        if extraJson ~= nil and extraJson ~= "" then
            properties["extra_report"] = extraJson
        end
    end
    local event = require "event"
    event.Trigger(event.GAME_EVENT_REPORT, "create_entity_time", properties)
end

function GetUTC0TimeStamp()
    local tss = System.DateTime.UtcNow - System.DateTime(1970, 1, 1, 0, 0, 0)
    local timestamp = math.floor(tss.TotalSeconds)
    --local offset = System.TimeZone.CurrentTimeZone:GetUtcOffset(System.DateTime.Now).Hours
    --return timestamp - offset * 3600
    return timestamp
end

function UTC0Seconds()
    local tss = System.DateTime.UtcNow - System.DateTime(1970, 1, 1, 0, 0, 0)
    return tss.TotalSeconds
end

function GetUTC0TimeStampPlayerPrefs()
    local timestampStr = PlayerPrefs.GetString("utc0_timestamp", "")
    if timestampStr == "" then
        local timestamp = GetUTC0TimeStamp()
        PlayerPrefs.SetString("utc0_timestamp", tostring(timestamp))
    end
    return tonumber(PlayerPrefs.GetString("utc0_timestamp", "0")) or 0
end

function AssetBundleManagerTrackEvent(eventName, data)
    local isTable = data and type(data) == "table"
    if isTable then
        local files_version_mgr = require "files_version_mgr"
        local v = files_version_mgr.GetCurResourceVersion()        
        data["resVer"] = v or "unknown"
    end
    if AssetBundleManager.TrackEvent then
        AssetBundleManager.TrackEvent(eventName, data)
    else
        local event = require "event"
        if isTable then
            data["utc0_timestamp"] = GetUTC0TimeStampPlayerPrefs()
        end
        event.RecodeTrigger(eventName, data)
    end
end

function ShowUISourceList(onshow, onHide)
    local ui_window_mgr = require("ui_window_mgr")

    local ReviewingUtil = require "ReviewingUtil"
    local ui_source_list_new = require "ui_source_list_new"
    if ReviewingUtil.IsReviewing() then
        if ui_source_list_new.GetGoodsId() ~= 1 then
            -- 金币
            local ui_value_gift = require "ui_value_gift"
            ui_value_gift.SetParent(4)
            ui_window_mgr:ShowModule("ui_value_gift", onshow, onHide)
            return
        end
    end
    if ui_source_list_new.GetGoodsId() then
        ui_window_mgr:ShowModule("ui_source_list_new", onshow, onHide)
    end
end

--[[
    @desc: 从pb对象复制数据出来
    author:{author}
    time:2020-10-29 15:04:49
     --@tc:
 	--@msg: 
    @return:
]]
function cp_pb_data(msg, tc)
    if IsPBNew() then
        return msg
    end
    add_exe = add_exe or function(proto, msg, exe_list, name)
        exe_list[exe_list.ind] = { proto, msg, name }
        exe_list.ind = exe_list.ind + 1
    end

    enc = enc or function(proto, msg, exe_list, name)
        if msg._message_descriptor then
            --and msg._message_descriptor.label == 3 
            for k, v in ipairs(msg) do
                if type(v) ~= "table" then
                    proto[k] = v
                else
                    if v then
                        local tmp = proto[k] or {}
                        proto[k] = tmp
                        add_exe(tmp, v, exe_list, k)
                    end
                end
            end

        elseif msg._fields then
            for k, v in pairs(msg._fields) do
                if type(v) ~= "table" then
                    proto[k.name] = v
                else
                    if v then
                        local tmp = proto[k.name] or {}
                        proto[k.name] = tmp
                        add_exe(tmp, v, exe_list, k.name)
                    end
                end
            end
        else
            -- print("Error",name,tostring(msg))
            if #msg > 0 then
                for k, v in ipairs(msg) do
                    proto[k] = v
                end
            end
        end
    end

    local tt = tc or {}
    local exe_list = { ind = 1 }

    add_exe(tt, msg, exe_list)
    for i, v in ipairs(exe_list) do
        enc(v[1], v[2], exe_list, v[3])
    end
    return tt
end

function fill_pb_data(msg, tc)
    add_exe_fill = add_exe_fill or function(proto, msg, exe_list)
        exe_list[exe_list.ind] = { proto, msg }
        exe_list.ind = exe_list.ind + 1
    end

    enc_fill = enc_fill or function(proto, msg, exe_list)
        if not msg then
            log.Error("enc_fill error msg nil", json.encode(proto))
            return
        end
        local f = msg.add or msg.append
        if f then
            if msg.add then
                for i, v in ipairs(proto) do
                    local a = msg:add()
                    add_exe_fill(v, a, exe_list)
                end
            else
                msg:append(v)
            end
        end
        for k, v in pairs(proto) do
            if type(v) == "table" then
                add_exe_fill(v, msg[k], exe_list)
            else
                msg[k] = v
            end
        end
    end

    local tt = tc or {}
    local exe_list = { ind = 1 }

    add_exe_fill(tt, msg, exe_list)
    for i, v in ipairs(exe_list) do
        enc_fill(v[1], v[2], exe_list)
    end
    return msg
end

function dic2list(t, keys)
    local list = {}
    if keys then
        for i, key in ipairs(keys) do
            table.insert(list, t[key])
        end
    else
        local ind = 1
        for k, v in pairs(t) do
            list[ind] = v
            ind = ind + 1
        end
    end
    return list
end

function list2dic(l, key, t)
    if not l then
        return {}
    end
    t = t or {}
    if key then
        for i, v in ipairs(l) do
            t[v[key]] = v
        end
    else
        for i, v in ipairs(l) do
            t[v] = v
        end
    end
    return t
end
function key2list(l, t)
    if not l then
        return {}
    end
    t = t or {}
    local i = 1
    for k, v in pairs(l) do
        t[i] = k
        i = i + 1
    end
    table.sort(t)
    return t
end

local utcOriginal = DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
-- 2020.12.22:12.30 -> 年.月.日:时.分
-- 使用 UTC 时间
-- strTime: 为 UTC 时间表示
function Str2Stamp(strTime)
    return UTCTime2Seconds(Str2UTCDateTime(strTime))
end

function UTCTime2Seconds(dateTime)
    return (dateTime - utcOriginal).TotalSeconds
end

-- 2020.12.22:12.30 -> 年.月.日:时.分
-- 使用 UTC 时间
-- strTime: 为 UTC 时间表示
function Str2UTCDateTime(strTime)
    local dayAndHour = string.split(strTime, ':')
    if #dayAndHour ~= 2 then
        log.Error("时间格式错误", tostring(strTime))
        return 0
    end
    -- 解析年月日
    local strYear = dayAndHour[1]
    local yearInfo = string.split(strYear, '.')
    if #yearInfo ~= 3 then
        log.Error("年月日格式错误", tostring(strYear))
        return 0
    end
    -- 解析时分秒
    local strHour = dayAndHour[2]
    local hourInfo = string.split(strHour, '.')
    local hour, minute
    local second = 0
    local hourInfoLen = #hourInfo
    if hourInfoLen < 2 then
        log.Error("时分秒格式错误", tostring(strHour))
        return 0
    end
    hour = tonumber(hourInfo[1])
    minute = tonumber(hourInfo[2])
    if hourInfoLen == 3 then
        second = tonumber(hourInfo[3])
    end
    return DateTime(tonumber(yearInfo[1]), tonumber(yearInfo[2]), tonumber(yearInfo[3]), hour, minute, second, DateTimeKind.Utc)
end

function pb2str(msg)
    indent = indent or { indent = true }
    local msg_str = json.encode(cp_pb_data(msg), indent)
    return msg_str
end
--[[通过一个时间戳计算当天凌晨24点时间]]
function GetTimeOfZero(timeArea, nowTime)
    if not nowTime then
        return
    end
    timeArea = timeArea or 0--当前时区
    local time = nowTime - (nowTime + timeArea * 60 * 60) % 86400--当天0点时间
    --print("通过一个时间戳计算当天0点时间nowTime",nowTime,"time",time,"time+86400",time+86400)
    return time + 86400
end
--[[通过一个时间戳计算当天凌晨24点时间]]
function GetZeroTime(timeArea)
    timeArea = timeArea or 0--当前时区
    local nowTime = os.server_time()--服务器当前时间(0时区)
    local time = nowTime - (nowTime + timeArea * 60 * 60) % 86400--当天0点时间
    --print("通过一个时间戳计算当天0点时间nowTime",nowTime,"time",time,"time+86400",time+86400)
    return time + 86400
end

--[[通过一个时间戳判断是否是属于当天的时间]]
function GetIsCurDayTime(timstamp, timeArea)
    timstamp = timstamp or 0 --时间戳
    timeArea = timeArea or 0--当前时区
    local nowTime = os.server_time()--服务器当前时间(0时区)
    local startTime = nowTime - (nowTime + timeArea * 60 * 60) % 86400--当天0点时间
    local endTime = startTime + 86400
    return timstamp >= startTime and timstamp < endTime

end

-- true: versionCompare > versionTarget
function CompareVersion(versionCompare, versionTarget)
    local v1 = string.split(versionCompare, ".")
    local v2 = string.split(versionTarget, ".")
    local v1Count, v2Count = #v1, #v2
    local len = math.min(v1Count, v2Count)
    local i, iV1, iV2
    for i = 1, len do
        iV1 = tonumber(v1[i])
        iV2 = tonumber(v2[i])
        if iV1 ~= iV2 then
            return iV1 > iV2
        end
    end

    -- 1.0.1.1 > 1.0.1
    return v1Count > v2Count
end

function SetGoActive(object, isActive)
    if not object or IsObjNull(object) then
        return
    end
    local go = object.gameObject
    if go and not IsObjNull(go) then
        go:SetActive(isActive)
    end
end

function IsAssetBundlExist(assetbundleName)
    if Application.isEditor then
        local assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetbundleName)
        if assetPaths == nil or assetPaths.Length == 0 then
            return false
        end
        return true
    else
        local size = hashRemote:GetSize(assetbundleName)
        return size > 0
    end
end

math.randomseed(os.time())
function Random(n, m)
    if m < n then
        -- 避免 interval is empty 报错
        n, m = m, n
    end
    return math.random(n, m)
end

function RandomFloat(min, max)
    return min + (max - min) * math.random()
end

function IsModuleOpen(moduleName)
    if not Application.isEditor then
        return false
    end
    if LuaModuleSwitch == nil then
        return false
    end
    local moduleTable = LuaModuleSwitch[moduleName]
    if moduleTable == nil then
        return false
    end
    return (not not moduleTable["enabled"])
end

local ReviewingUtil = nil
-- 设置物体是否显示
-- 提审版本强制隐藏物体
function SetGameObjectActivedJudgeReview(gameObject, bActived)
    if ReviewingUtil == nil then
        ReviewingUtil = require "ReviewingUtil"
    end
    if bActived and ReviewingUtil.IsReviewing() then
        bActived = false
    end
    if gameObject then
        gameObject:SetActive(bActived)
    end
end

function GetLoginState()
    local player_mgr = require "player_mgr"
    local playerEntity = player_mgr.GetPlayerEntity()
    if playerEntity == nil then
        return 1
    else
        return 2
    end
end

-- 添加系统附加数据，生成反馈字符串
-- feedType: 反馈的问题类型
function BuildPostData(intFeedbackType, feedType, userDescription, logUri)
    local channelId = game_config.CHANNEL_ID
    if not channelId then
        channelId = ""
    end

    local deviceInfo = {
        netReach = tostring(Application.internetReachability),
        deviceModel = tostring(SystemInfo.deviceModel), --设备型号
        deviceName = tostring(SystemInfo.deviceName), --设备的名称
        deviceType = tostring(SystemInfo.deviceType), --设备类型
        sysMemSize = tostring(SystemInfo.systemMemorySize), --系统内存大小
        opSys = tostring(SystemInfo.operatingSystem), --操作系统
        gMemSize = tostring(SystemInfo.graphicsMemorySize), --显卡内存大小
        loginState = GetLoginState(),
        logUri = logUri, --日志下载路径
        language = tostring(Application.systemLanguage),
        feedbackType = tostring(intFeedbackType),
        channelId = tostring(channelId),
        -- feedType 由于多语言翻译问题不再使用，由后台完成 id 到中文的翻译映射
        -- feedbackIntType = tostring(intFeedbackType),
    }
    -- 分割符 §
    local delimiter = "§"
    local k, v
    local deviceDes = ""
    for k, v in pairs(deviceInfo) do
        deviceDes = deviceDes .. k .. "=" .. v .. delimiter
    end
    if userDescription == nil then
        userDescription = ""
    end

    return deviceDes .. userDescription
end

--[[
    @desc: 比较服务器时间是否大于传入时间
    @time: string 配置时间（2021-2-20-0:00）
    @return: bool
]]
function compareServerTime(time)
    --hero/RoleFace/skin表配置，图鉴开启时间
    if not time or time == "" or time == "0" then
        --hero表配置，图鉴开启时间，不做时间限制则不配或者为0
        return true
    end
    if type(time) ~= "string" then
        log.Error("传入time参数格式，请检查")
        return false
    end
    --获取客户端时区、服务器时区、夏令时
    local net_login_module = require "net_login_module"
    local serverTime = net_login_module.GetServerTime()--服务器时间
    local Server_TimeZone = net_login_module.GetServerTimeZone()-- 服务器时区
    local Client_TimeZone = GetTimeArea()--*3600--客户端时区
    local client_date = os.date("*t", os.server_time())
    if client_date.isdst then
        Client_TimeZone = Client_TimeZone + 1--夏令时有一个小时的差别，要加回去
    end
    -- print("serverTime:",serverTime,"Client_TimeZone:",Client_TimeZone,"Server_TimeZone:",Server_TimeZone,"client_date.isdst:",client_date.isdst)

    --处理当前时间的时区
    local curTimestamp_zone = serverTime - (Client_TimeZone - Server_TimeZone) * 3600

    --处理传入时间（为配置时间，格式：2021-2-20-0:00，不用做时区处理）
    local dayStr = string.split(time, '-')
    if #dayStr < 4 then
        log.Error("dayStr格式解析错误，请检查传入time配置")
        return false
    end
    local timeStr = string.split(dayStr[4], ':', tonumber)
    if #timeStr < 2 then
        log.Error("timeStr解析错误，请检查传入时间配置dayStr[4] = ", dayStr[4])
        return false
    end

    local timestamp = os.time({ day = dayStr[3], month = dayStr[2], year = dayStr[1], hour = timeStr[1], min = timeStr[2], sec = 0 })
    -- print(time,"curTimestamp_zone:"..curTimestamp_zone,"timestamp:"..timestamp)
    if not curTimestamp_zone then
        log.Error("curTimestamp_zone值为空，请检查curTimestamp_zone = ", curTimestamp_zone, serverTime, Client_TimeZone, Server_TimeZone)
        return false
    end

    if not timestamp then
        log.Error("timestamp为空，请检查，timestamp = ", timestamp)
        dump(dayStr)
        return false
    end
    return timestamp <= curTimestamp_zone
end

-- 以物体绑定的 BoxCollider2D 组件设定的 box 区域作为攻击区域
-- box 区域对齐世界坐标轴，而非物体模型空间坐标轴。若需要对齐模型空间，可后续扩展
function GetBoxAttackRange(boxCollider2D, circleCollider2D)
    if boxCollider2D then
        local bounds = boxCollider2D.bounds
        local extents = bounds.extents
        -- 需要扩大一点点检测区域
        local extentSize = 0.05
        extents.x = extents.x + extentSize
        extents.y = extents.y + extentSize
        local leftBottom = bounds.center - extents
        return Rect(leftBottom.x, leftBottom.y, bounds.size.x + 2 * extentSize, bounds.size.y + 2 * extentSize)
    elseif circleCollider2D then
        local bounds = circleCollider2D.bounds
        local extents = Vector2(circleCollider2D.radius, circleCollider2D.radius)
        -- 需要扩大一点点检测区域
        local extentSize = 0.05
        extents.x = extents.x + extentSize
        extents.y = extents.y + extentSize
        local center = Vector2(bounds.center.x, bounds.center.y)
        local leftBottom = center - extents
        local diameter = circleCollider2D.radius * 2
        return Rect(leftBottom.x, leftBottom.y, diameter + 2 * extentSize, diameter + 2 * extentSize)
    end

    return nil
end

function GetCapsuleAttackRange(capsuleCollider)
    local bounds = capsuleCollider.bounds
    local extents = bounds.extents
    -- 需要扩大一点点检测区域
    local extentSize = 0.05
    extents.x = extents.x + extentSize
    extents.y = extents.y + extentSize
    local leftBottom = bounds.center - extents
    return Rect(leftBottom.x, leftBottom.y, bounds.size.x + 2 * extentSize, bounds.size.y + 2 * extentSize)
end

function ShouldUseCustomSDKUI()
    local game_config = require "game_config"
    if game_config.Q1SDK_DOMESTIC then
        return false
    end

    return true
end

local uniform_random_classify = nil
function GetUserRandomClassify()
    if uniform_random_classify then
        return uniform_random_classify
    end

    -- 对玩家随机分类型。目前按类型决定热更策略
    local random_classify = PlayerPrefs.GetInt("random_classify", -1)
    if random_classify == -1 then
        local utc_stamp = GetUTC0TimeStampPlayerPrefs()
        local random_classify = math.fmod(utc_stamp, 1000)
        PlayerPrefs.SetInt("random_classify", random_classify)
    end
    uniform_random_classify = random_classify
    return uniform_random_classify
end

function ReadJson(jsonFilePath)
    local fileData = File.ReadAllText(jsonFilePath)
    local dkjson = require "dkjson"
    local json = dkjson.decode(fileData)
    if json == nil then
        log.Error(jsonFilePath .. " json 格式错误")
        return nil
    end
    return json
end

function WriteJson(jsonFilePath, jsonData)
    local dkjson = require "dkjson"
    local json_str = dkjson.encode(jsonData)
    if json_str == nil then
        log.Error(jsonFilePath .. " json 格式错误")
        return nil
    end
    -- log.Error(json_str, jsonFilePath)
    File.WriteAllText(jsonFilePath, json_str)
end

-- return: Crc32, 类型: long
function Crc32File(filePath)
    local fileInfo = FileInfo(filePath)
    local fileData = File.ReadAllBytes(filePath)
    crc32 = Crc32()
    crc32:Update(fileData, 0, Int32.Parse(tostring(fileInfo.Length)))
    return crc32.Value
end

function Long2Int(longValue)
    return Int32.Parse(tostring(longValue))
end

function StringToTableHelper(t)
    return t
end

function StringToTable(str)
    local f = loadstring("return " .. str)
    if f == nil then
        return
    end
    return f()
end

--判断time1，time2是否为同一天
function IsSameDay(time1, time2)
    local net_login_module = require "net_login_module"
    local serverTimeZone = net_login_module.GetServerTimeZone()-- 服务器时区
    local Time1 = time1 + serverTimeZone * 3600
    local Time2 = time2 + serverTimeZone * 3600
    local time_date1 = os.date("!*t", Time1)
    local time_date2 = os.date("!*t", Time2)
    return (time_date1.year == time_date2.year and time_date1.month == time_date2.month and time_date1.day == time_date2.day)
end

function IsSameDayNew(time1, time2)
    local net_login_module = require "net_login_module"
    local serverTimeZone = net_login_module.GetServerTimeZone()-- 服务器时区
    local Time1 = time1
    local Time2 = time2
    local time_date1 = os.date("*t", Time1) --返回当时时间戳的年月日
    local time_date2 = os.date("*t", Time2)    --返回当时时间戳的年月日
    -- print("Time1",Time1)
    -- dump(time_date1)
    -- print("Time2",Time2)
    -- dump(time_date2)
    local result = (time_date1.year == time_date2.year and time_date1.month == time_date2.month and time_date1.day == time_date2.day)
    -- print("result:",tostring(result))
    return (time_date1.year == time_date2.year and time_date1.month == time_date2.month and time_date1.day == time_date2.day)
end

--- 判断 time2 是否为 time1 当日之后
---@param time1 number
---@param time2 number
function IsAfterDay(time1, time2)
    -- 获取详细时间
    local date1 = os.date("!*t", time1)
    local date2 = os.date("!*t", time2)
    -- 比较当天的初始时间戳
    --如果传入的是0的话会报错，做一个保护
    local base1 = os.time({ year = date1.year, month = date1.month, day = date1.day, hour = 0, min = 0, sec = 0 }) or 0  
    local base2 = os.time({ year = date2.year, month = date2.month, day = date2.day, hour = 0, min = 0, sec = 0 }) or 0
    return base2 > base1
end

function GetServerTime()
    local serverTime = os.server_time()
    local serverTimeZone = os.server_zone()
    return serverTime + serverTimeZone * 3600
end

--获取下一个时间戳（指定0-24点）
function GetCountDownByHour(hour)
    local serverTime = GetServerTime()
    local localTimeZone = GetTimeArea()
    --服务器时间转utc0时间
    local date = os.date("!*t", serverTime)
    date.hour = 0
    date.min = 0
    date.sec = 0
    --服务器当天0点时间, 因为os.time会减去本地时区，所以要加回去
    local nowTime = os.time(date)
    if nowTime == nil then
        log.Error("nowTime is nil")
        return 0
    end
    local time = nowTime + localTimeZone * 3600
    local offset = (24 + hour) * 3600
    --服务器的目标时间
    local target = time + offset
    --服务器目标时间减去服务器当前时间，得到倒计时
    return target - serverTime
end

--获取一个时间戳（指定0-24点）
function GetTimeCountDownByHour(time, hour)
    local serverTime = time
    local localTimeZone = GetTimeArea()
    --服务器时间转utc0时间
    local date = os.date("!*t", serverTime)
    date.hour = 0
    date.min = 0
    date.sec = 0
    --服务器当天0点时间, 因为os.time会减去本地时区，所以要加回去
    local nowTime = os.time(date)
    if nowTime == nil then
        log.Error("nowTime is nil")
        return 0
    end
    local time = nowTime + localTimeZone * 3600
    local offset = (24 + hour) * 3600
    --服务器的目标时间
    local target = time + offset
    --服务器目标时间减去指定时间，得到倒计时
    return target - serverTime
end

-- 传入一个时间搓戳，获取此时间戳下服务器的0点时间戳
function GetServerTime0(timestamp)
    if not timestamp then
        timestamp = os.server_time()
    end
    local net_login_module = require "net_login_module"
    local serverTimeZone = net_login_module.GetServerTimeZone()
    local time = timestamp - (timestamp + serverTimeZone * 3600) % 86400
    return time
end

---给定一个时间搓time，获取day天后hour小时的时间搓
---@param timeStamp number 开始的时间搓，需要加上服务器时区的时间偏移
---@param day number 天数，多少天后，0天算第一天
---@param hour number 小时，几点
---@return number 返回的是加上服务器时区的时间，需要用target-util.GetServerTime()来获取倒计时
function GetAppointTimeStamp(timeStamp, day, hour)
    local serverTime = timeStamp
    local localTimeZone = GetTimeArea()
    --服务器时间转utc0时间
    local date = os.date("!*t", serverTime)
    date.hour = 0
    date.min = 0
    date.sec = 0
    --服务器当天0点时间, 因为os.time会减去本地时区，所以要加回去
    local nowTime = os.time(date)
    if nowTime == nil then
        log.Error("nowTime is nil")
        return 0
    end
    local time = nowTime + localTimeZone * 3600
    local offset = (day * 24 + hour) * 3600
    --服务器的目标时间
    local target = time + offset
    return target
end

function GetWeekDay()
    local serverTime = GetServerTime()
    local localTimeZone = GetTimeArea()
    --服务器时间转utc0时间
    local date = os.date("!*t", serverTime)
    --周日为第1天, 转为周日为第7天
    local nowWeekDay = date.wday - 1
    if nowWeekDay == 0 then
        nowWeekDay = 7
    end
    return nowWeekDay
end

--计算平均数
function averager(tmp4V3Arr)
    local div = { x = 0, y = 0, z = 0 }
    local len = tmp4V3Arr.Length or tmp4V3Arr.Count or 1
    for i = 0, 3 do
        local t = tmp4V3Arr[i]
        div.x = div.x + t.x / len
        div.y = div.y + t.y / len
        div.z = div.z + t.z / len
    end
    return div
end

-- 
function combine_event(inst, key, func)

    inst[key]("+", func)
    -- if inst[key] then
    -- else
    -- 	inst[key] = func
    -- end
    -- inst[key] = (inst[key] and (inst[key] +  func)) or func
end

function remove_event(inst, key, func)
    inst[key] = (inst[key] and (inst[key] - func)) or func
end

--获取下一个时间戳（指定周1-7）
function GetCountDownByWeekDay(weekDay)
    local serverTime = GetServerTime()
    local localTimeZone = GetTimeArea()
    --服务器时间转utc0时间
    local date = os.date("!*t", serverTime)
    date.hour = 0
    date.min = 0
    date.sec = 0
    --服务器当天0点时间, 因为os.time会减去本地时区，所以要加回去
    local time = os.time(date) + localTimeZone * 3600

    --周日为第1天, 转为周日为第7天
    local nowWeekDay = date.wday - 1
    if nowWeekDay == 0 then
        nowWeekDay = 7
    end
    local diff = weekDay - nowWeekDay
    if diff < 0 then
        diff = diff + 7
    end

    local offset = diff * 86400
    --服务器的目标时间
    local target = time + offset
    --服务器目标时间减去服务器当前时间，得到倒计时
    return target - serverTime
end

-- 设置辉光光效
function SetCameraHuiguang(enabled)
    local device_level_controller = require "device_level_controller"
    if not Application.isEditor and device_level_controller.OpenHdrController() then
        enabled = false
    end

    --print("SetCameraHuiguang",enabled)
    if IsObjNull(huiguang) then
        local uiCamera = GameObject.Find("/UIRoot/UICamera")
        huiguang = uiCamera:GetComponent(typeof(PostProcessingBehaviour))
    end

    if not IsObjNull(huiguang) then
        huiguang.enabled = enabled
    end
end

--设置Text、radient和outline颜色
function SetTextColors(uitext, colors)
    local four_colors = string.splitcache(colors or "", "#")
    if four_colors and uitext then
        if four_colors[1] then
            uitext.color = color_palette.HexToColor(four_colors[1])
        end

        local gradient = uitext:GetComponent(typeof(Gradient))
        if gradient then
            if four_colors[2] then
                gradient.TopColor = MulTable(color_palette.HexToColor(four_colors[2]), 255)
            end
            if four_colors[3] then
                gradient.BottomColor = MulTable(color_palette.HexToColor(four_colors[3]), 255)
            end
        end

        local outline = uitext:GetComponent(typeof(Outline))
        if outline and four_colors[4] then
            outline.effectColor = color_palette.HexToColor(four_colors[4])
        end
    end
end

--设置Text的outline颜色
function SetTextOutlineColor(uitext, color)
    if uitext then
        local outline = uitext:GetComponent(typeof(Outline))
        if outline then
            outline.effectColor = color_palette.HexToColor(color)
        end
    end

end

--解码url字符串
function DecodeURL(s)
    s = string.gsub(s, '%%(%x%x)', function(h)
        return string.char(tonumber(h, 16))
    end)
    return s
end

function IterDictCS(dic, func)
    local iter = dic:GetEnumerator()        --获取迭代器
    while iter:MoveNext() do
        --只要movenext指针不为空，就一直迭代
        func(iter.Current)                                 --获取到就可以进行操作了
    end
end
-- 获取 app 安装包指定的配置参数
-- 获取大区时： Android -> RegionCode, iOS -> RegionId
function GetAppProperty(key, defaultValue, isInt, filedName, propertyType)
    if Application.isEditor then
        return defaultValue
    end

    if Application.platform == RuntimePlatform.IPhonePlayer then
        if (not Q1SDK.Instance.GetAppIntProperty) then
            return defaultValue
        end
        if isInt then
            return Q1SDK.Instance:GetAppIntProperty(key, defaultValue)
        end
        local valueType = type(defaultValue)
        if valueType == "boolean" then
            return Q1SDK.Instance:GetAppBoolProperty(key, defaultValue)
        end
        log.Error("不支持获取此数据类型")
    else
        if (not Utility.GetAppProperty) then
            return defaultValue
        end

        if filedName == nil then
            filedName = "metaData"
        end
        if propertyType == nil then
            propertyType = 128 -- PackageManager.GET_META_DATA
        end
        local game_config = require "game_config"
        if isInt then
            -- lua 5.1 int 自动转化为 double，不能自动区分类型
            if game_config.Q1SDK_DOMESTIC then
                return Utility.GetAppIntProperty(key, defaultValue, filedName, propertyType)
            else
                local errorCode, value = Utility.GetApplicationIntProperty(key, defaultValue, filedName, propertyType)
                if errorCode ~= 0 then
                    log.Error("获取数值,", key, ",错误：", errorCode)
                end
                return value
            end

        else
            -- GetAppProperty 获取 Application 中 metaData 数据，但依赖 com.q1.common.util.MetaUtils，此数据仅国内版有实现
            if game_config.Q1SDK_DOMESTIC then
                return Utility.GetAppProperty(key, defaultValue, filedName, propertyType)
            else
                local errorCode, value = Utility.GetApplicationProperty(key, defaultValue, filedName, propertyType)
                if errorCode ~= 0 then
                    log.Error("获取数值,", key, ",错误：", errorCode)
                end
                return value
            end
        end
    end
end
---@return ChannelMarkType @ 获取游戏渠道标识
function GetChannelMark()
    return _G["_GAME_CHANNEL_MARK"] or const.ChannelMarkType.WM2_2020
end

---@return string @ 修复用于gsub的特殊字符
function RefineGsubStr(str)
    if str then
        str = string.gsub(str, "[%%%]%-%^%.[()$*+?]", function(c)
            -- 特殊字符匹配,添加了%在[后面
            if string.len(c) == 0 then
                return c
            end
            return "%" .. c
        end)
    end

    return str
end

--[[

self.flag = DirtyValue(false)
self.flag.value = true
self.flag.value
self.flag.dirty

if self.flag.dirty then
	self.flag.value
	self.flag.dirty = false
end

]]
function DirtyValue(defaultValue, defaultDirty)
    -- 默认不更新状态
    local data = defaultValue
    local dirty = defaultDirty or false
    return setmetatable({}, {
        __index = function(myTable, key)
            if key == "dirty" then
                return dirty
            end
            return data
        end,
        __newindex = function(myTable, key, value)
            if key == "dirty" then
                dirty = value
                return
            end
            if value ~= data then
                data = value
                dirty = true
            end
        end
    })
end

function IsEnableHCLR()
    local files_version_mgr = require "files_version_mgr"
    local v=tostring(files_version_mgr.ApkUpdateConfigTryGetValue("p_EnableHybridCLR"))
    if IsSimulateHybridCLR() or v=="true" or v=="True" then
        return true
    end
    return false
end
function IsPLaunch()
    local files_version_mgr = require "files_version_mgr"
    return files_version_mgr.ApkUpdateConfigTryGetValue("p_launch")
end
countTryISO = nil

function GetISOCode()
    if Application.isEditor then
        return "default", false
    end
    local isSucceed = false
    if not countTryISO then
        if AssetsUpdator.ISOReqStr == nil or AssetsUpdator.ISOReqStr == "" then
            print("获取区域码失败!!")
            countTryISO = "default"
        else
            local kv = string.gmatch(AssetsUpdator.ISOReqStr, '\"isoCode\":\"(%a+)\"')
            for k in kv do
                countTryISO = k
                isSucceed = true
                print("初始化区域码成功:", countTryISO)
                break
            end
        end
    end
    if not countTryISO then
        countTryISO = "default"
        print("区域码异常!!!", AssetsUpdator.ISOReqStr)
    end
    local url_operation_mgr = require "url_operation_mgr"
    local testIpArea = url_operation_mgr.GetTestIpArea()
    if testIpArea then
        print("使用测试Ip地区", testIpArea)
        return testIpArea, isSucceed
    end
    return countTryISO, isSucceed
end

countryISOByLua = nil
local isFirstGetISOCodeByLua = nil
function GetISOCodeByLua(callback)
    if Application.isEditor then
        return
    end
    local isoCode, isSucceed = GetISOCode()
    if isSucceed then
        if callback then
            callback(isoCode, isSucceed)
        end
        return
    end
    -- local isSucceed = false
    if not isFirstGetISOCodeByLua then
        isFirstGetISOCodeByLua = true
        if countryISOByLua then
            isSucceed = true
            if callback then
                callback(countryISOByLua, isSucceed)
            end
            print("zzd_____countryISOByLua", countryISOByLua, "isSucceed", isSucceed)
        else
            local isoCodeUrl = "https://api-ea.q1.com/ip/api/ips/selfcountry"
            local http_inst = require "http_inst"
            event.Trigger(event.GAME_EVENT_REPORT, "GetISOCode_start", {})
            http_inst.Req_Timeout(isoCodeUrl, 2, function(data, hasError)
                print("zzd_____GetISOCodeByLua()   http_inst.Req_Timeout")
                if not hasError and data then
                    local kv = string.gmatch(data, '\"isoCode\":\"(%a+)\"')
                    for k in kv do
                        countryISOByLua = k
                        isSucceed = true
                        event.Trigger(event.GAME_EVENT_REPORT, "GetISOCode_success", {code = countryISOByLua})
                        print("zzd____初始化区域码成功:", countryISOByLua)
                        if callback then
                            callback(countryISOByLua, isSucceed)
                        end
                        break
                    end
                else
                    print("zzd_____GetISOCodeByLua()  Error ", error)
                    print("zzd____获取区域码失败!!")
                    countryISOByLua = "default"
                    event.Trigger(event.GAME_EVENT_REPORT, "GetISOCode_fail", {code = countryISOByLua})
                    if callback then
                        callback(countryISOByLua, isSucceed)
                    end

                end
            end)
        end
    end
    --if not countTryISO then
    --	countTryISO ="default"
    --	print("区域码异常!!!",AssetsUpdator.ISOReqStr)
    --end

end

--打点根据IP选择小游戏
function GameEventIPdiscern_minigame(isSucceed, minigamename, isocode, isGuarantees)
    local property = {}
    property.isSucceed = isSucceed
    property.minigamename = minigamename
    property.isocode = isocode
    property.isGuarantees = isGuarantees
    local event = require "event"
    event.Trigger(event.GAME_EVENT_REPORT, "IPdiscern_minigame", property)
end

--Aes特殊加密key为“oD3mgQUf32MrhJwd”(和平台约定的)，iv为“x;0y_,Q5x;0y_,Q5”
function GetAesEncryport(str)
    --传入string类型
    local newval = ""
    if str then
        local dataplain = str
        local data = ""
        local encryptorData = ""
        --  lua中string转byte
        for i = 1, string.len(dataplain) do
            local srcArray = string.sub(dataplain, i, i)
            data = data .. srcArray
        end
        local aesEncryportVal = Aes.Encryptor(data, "oD3mgQUf32MrhJwd", "x;0y_,Q5x;0y_,Q5")
        local val = Convert.ToBase64String(aesEncryportVal);    --加密后的数据中包含“==”，平台无法解析，所以多转一次base64String

        for i = 1, string.len(val) do
            local srcArray = string.sub(val, i, i)
            encryptorData = encryptorData .. srcArray
        end
        newval = Convert.ToBase64String(encryptorData);
    end
    return newval or 0
end

--Aes特殊解密key为“oD3mgQUf32MrhJwd”，iv为“x;0y_,Q5x;0y_,Q5”
function GetAesDencryport(str)
    --传入string类型
    local Decryptorrecharge = ""
    if str then
        local encryptorVal = Convert.FromBase64String(str)
        Decryptorrecharge = Aes.Decryptor(Convert.FromBase64String(encryptorVal), "oD3mgQUf32MrhJwd", "x;0y_,Q5x;0y_,Q5")
    end
    --log.Warning("解密数据",Decryptorrecharge)
    return Decryptorrecharge or 0
end

function IsNullOrEmpty(str)
    return (not str) or str == ""
end

--转换字符串到Int32
function TryParseStrToInt(str)
    local iRet = 0
    if not IsNullOrEmpty(str) then
        local success, value = Int32.TryParse(str)
        if success then
            iRet = value
        end
    end

    return iRet
end

function IsDisableViewBackground()
    ---因为disable_view_background有bug 先默认关闭
    if true then
        return true        
    end    
    if Application.isEditor then
        return true
    end

    local exist, isDisableViewBackground
    exist, isDisableViewBackground = AssetsUpdator.UpdateConfig:TryGetValue("disable_view_background", isDisableViewBackground)

    return true == isDisableViewBackground or "true" == isDisableViewBackground
end

--- 将UI下的指定节点拉伸到屏幕大小（不考虑放大缩小的情况）
---@param uiNode any 要拉伸的节点
function StretchNodeToScreen(uiNode)
    if IsObjNull(uiNode) then
        log.Error("FillScreen uiNode == nil")
        return
    end
    local rtNode = uiNode:GetComponent(typeof(RectTransform))
    if IsObjNull(rtNode) then
        log.Error("FillScreen rtNode == nil")
        return
    end

    local ui_window_mgr = require "ui_window_mgr"
    local rtCanvas = ui_window_mgr.canvasMeshT
    if rtCanvas == nil then
        log.Error("rtCanvas == nil")
        return
    end

    rtNode.anchorMin = { x = 0.5, y = 0.5 }
    rtNode.anchorMax = { x = 0.5, y = 0.5 }
    rtNode.pivot = { x = 0.5, y = 0.5 }
    rtNode.position = rtCanvas.position
    rtNode.sizeDelta = rtCanvas.sizeDelta
end

--- 将十进制数转换为二进制数, 注意顺序(10->1010)
function DecimalToBinary(decimal)
    local binary = ""
    while decimal > 0 do
        binary = tostring(decimal % 2) .. binary
        decimal = math.floor(decimal / 2)
    end
    return binary
end

--- 将十进制数转换成二进制数组, 注意顺序(10->[0,1,0,1])
function DecimalToBinaryArray(decimal)
    local binaryArray = {}
    while decimal > 0 do
        local remainder = decimal % 2
        table.insert(binaryArray, remainder)
        decimal = math.floor(decimal / 2)
    end
    return binaryArray
end

--- 将十进制数转换成二进制数组, 注意顺序(10->[false,true,false,true])
function BitValue2ArrayBoolean(decimal)
    local binaryArray = {}
    while decimal > 0 do
        local result = (decimal % 2) ~= 0
        table.insert(binaryArray, result)
        decimal = math.floor(decimal / 2)
    end
    return binaryArray
end

-- 屏幕尺寸变化回调
function toJsonString(t)
    return json.encode(t)
end

function URLEncode(s)
    s = string.gsub(s, "([^%w%.%- ])", function(c)
        return string.format("%%%02X", string.byte(c))
    end)
    return string.gsub(s, " ", "+")
end

function URLDecode(s)
    s = string.gsub(s, '%%(%x%x)', function(h)
        return string.char(tonumber(h, 16))
    end)
    return s
end

function toJsonString(t, indent, exceptionHandler)
    return json.encode(t, { indent = indent, exception = exceptionHandler })
end

---GetRayOfRt 计算点击屏幕映射到RenderTexture对应的射线
---@param rtCanvas RectTransform Canvas的Recttransform
---@param mousePoint table<x=number,y=num>
---@param previewImage RawImage 显示RenderTexture的图片
---@param uiCamera Camera ui相机
---@param previewCamera Camera 导出RenderTexture的相机
function GetRayOfRt(rtCanvas, mousePosition, previewImage, uiCamera, previewCamera)
    if IsObjNull(rtCanvas) or IsObjNull(previewImage) or IsObjNull(uiCamera) or IsObjNull(previewCamera) then
        log.Error("GetRayOfRt someone is nil")
        return nil
    end

    -- 将UI相机下点击的UI坐标转为相对RawImage的坐标
    local mousePositionV2 = { x = mousePosition.x, y = mousePosition.y }
    local bOk, clickPosInRawImg = RectTransformUtility.ScreenPointToLocalPointInRectangle(rtCanvas, mousePositionV2, uiCamera)
    if not bOk then
        log.Error("GetRayOfRt bOk false")
        return nil
    end

    --获取预览图的长宽
    local imageWidth = previewImage.rectTransform.rect.width
    local imageHeight = previewImage.rectTransform.rect.height
    -- 获取预览图的坐标
    local localPositionX = previewImage.rectTransform.localPosition.x - imageWidth * previewImage.rectTransform.pivot.x
    local localPositionY = previewImage.rectTransform.localPosition.y - imageHeight * previewImage.rectTransform.pivot.y
    -- 获取在预览映射相机viewport内的坐标（坐标比例）
    local p_x = (clickPosInRawImg.x - localPositionX) / imageWidth
    local p_y = (clickPosInRawImg.y - localPositionY) / imageHeight
    -- 从视口坐标发射线
    ray = previewCamera:ViewportPointToRay({ x = p_x, y = p_y })

    return ray
end

function IsCSharpClass(classType)
    if rawget(classType, ".fqn") then
        return false
    else
        return true
    end
end

-- 屏幕尺寸变化回调
function onScreenSizeChanged(eventName, newSize, oldSize)
    isAdaptiveTablet = nil
end
event.Register(event.SCREEN_SIZE_CHANGED_PREPASS, onScreenSizeChanged)
function IsNullOrEmpty(str)
    return (not str) or str == ""
end

--获取当前服务器的日期
function GetServerDateFromServerTimer(serverTime)
    local localTimeZone = GetTimeArea()
    local serverTimeZone = os.server_zone()
    serverTime = serverTime - (localTimeZone - serverTimeZone) * 3600
    --os.date("*t",ticks)会自动转时区
    return os.date("*t", serverTime)
end

function AutoSetPageSize(viewRectTransform, scrollRectTable)
    local viewHeight = math.abs(viewRectTransform.rect.height)
    local itemHeight = scrollRectTable.tileItem.transform.sizeDelta.y
    local padding = scrollRectTable.padding.y
    local tileHeight = itemHeight + padding
    scrollRectTable.pageSize = (math.ceil(viewHeight / tileHeight) + 1) * math.max(scrollRectTable.columns or 1, 1)
end

--禁用该接口，主界面打开关闭是业务逻辑，不封装在util中
--[[--主面板显示与隐藏
function MainPanel(isTrue)

    if const.USE_MAIN_SLG then
        local sand_ui_event_define = require "sand_ui_event_define"
        if isTrue then
            event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        else
            event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
        end
        return
    end
    
    local windowMgr = require "ui_window_mgr"
    if isTrue then
        if not windowMgr:IsModuleShown("new_hook_scene") then
            windowMgr:ShowModule("new_hook_scene")
        end
        if not windowMgr:IsModuleShown("ui_lobby") then
            windowMgr:ShowModule("ui_lobby")
        end
        if not windowMgr:IsModuleShown("ui_menu_top") then
            windowMgr:ShowModule("ui_menu_top")
        end
        if not windowMgr:IsModuleShown("ui_menu_bot") then
            windowMgr:ShowModule("ui_menu_bot")
        end
    else
        if windowMgr:IsModuleShown("new_hook_scene") then
            windowMgr:UnloadModule("new_hook_scene")
        end
        if windowMgr:IsModuleShown("ui_lobby") then
            windowMgr:UnloadModule("ui_lobby")
        end
        if windowMgr:IsModuleShown("ui_menu_top") then
            windowMgr:UnloadModule("ui_menu_top")
        end
        if windowMgr:IsModuleShown("ui_menu_bot") then
            windowMgr:UnloadModule("ui_menu_bot")
        end
    end
end]]

function ChangeLine(text)
    if (text and string and string.gsub) then
        return string.gsub(text, "/", "\n")
    else
        return text
    end
end

local function regexEscape(str)
    return str:gsub("[%(%)%.%%%+%-%*%?%[%^%$%]]", "%%%1")
end
-- you can use return and set your own name if you do require() or dofile()

-- like this: str_replace = require("string-replace")
-- return function (str, this, that) -- modify the line below for the above to work
function Replace(str, this, that)
    return str:gsub(regexEscape(this), that:gsub("%%", "%%%%")) -- only % needs to be escaped for 'that'
end

---@deprecated 删除字符串中的%号，避免使用lua的api处理字符串时，自动转正正则出错
---@param text string
---@return string
function RemovePercentSign(text)
    local tailIndex = string.find(text, '%%')
    if tailIndex then
        local newText = ""
        local headIndex = 1
        while tailIndex do
            newText = newText .. string.sub(text, headIndex, tailIndex - 1)
            headIndex = tailIndex + 1
            tailIndex = string.find(text, '%%', headIndex)
        end
        if headIndex < #text then
            newText = newText .. string.sub(text, headIndex)
        end
        return newText
    else
        return text
    end
end

--判断某个界面当天是否是首次进入
function GetIsTodayFirstEnter(moduleName)
    if moduleName then
        local player_mgr = require "player_mgr"
        local key = "_openTime" .. player_mgr.GetPlayerRoleID()
        local lastTime = PlayerPrefs.GetInt(moduleName .. key, 0)
        return not GetIsCurDayTime(lastTime, os.server_zone())
    end
    return false
end

--是否第一次进入某个界面
function GetFirstEnter(moduleName)
    if moduleName then
        local player_mgr = require "player_mgr"
        local key = "_openTime" .. player_mgr.GetPlayerRoleID()
        local lastTime = PlayerPrefs.GetInt(moduleName .. key, 0)
        return not (lastTime > 0)
    end
    return false
end

function SetEnterTime(moduleName)
    local player_mgr = require "player_mgr"
    local key = "_openTime" .. player_mgr.GetPlayerRoleID()
    PlayerPrefs.SetInt(moduleName .. key, os.server_time())
end

function ShowReward(rewardIds, callback)
    local reward_mgr = require "reward_mgr"
    local iui_reward = require "iui_reward"
    if rewardIds and #rewardIds > 0 then
        local rewards = {}
        for i = 1, #rewardIds do
            if rewardIds[i] ~= 0 then
                table.insert(rewards, reward_mgr.GetRewardGoods(rewardIds[i]))
            end
        end
        iui_reward.Show(rewards, nil, callback, nil, lang.Get(7507))
        event.Trigger(event.UPDATE_GOOD_PROP)
    end
end

---@see 服务器时间戳（时区处理）
function GetServerTimestamp_TimeZone(timestamp)
    local net_login_module = require "net_login_module"
    local Server_TimeZone = net_login_module.GetServerTimeZone()--服务器时区

    local client_date = os.date("*t", os.server_time())

    local Client_TimeZone = GetTimeArea()--客户端时区
    if client_date.isdst then
        Client_TimeZone = Client_TimeZone + 1--夏令时有一个小时的差别，要加回去
    end
    local Timestamp_zone = timestamp - (Client_TimeZone - Server_TimeZone) * 3600

    return Timestamp_zone
end
function SetTextRight2Left(text)
    if text.alignment == TextAnchor.UpperRight then
        text.alignment = TextAnchor.UpperLeft
    elseif text.alignment == TextAnchor.MiddleRight then
        text.alignment = TextAnchor.MiddleLeft
    elseif text.alignment == TextAnchor.LowerRight then
        text.alignment = TextAnchor.LowerLeft
    end
end
function SetTextLeft2Right(text)
    if text.alignment == TextAnchor.UpperLeft then
        text.alignment = TextAnchor.UpperRight
    elseif text.alignment == TextAnchor.MiddleLeft then
        text.alignment = TextAnchor.MiddleRight
    elseif text.alignment == TextAnchor.LowerLeft then
        text.alignment = TextAnchor.LowerRight
    end
end
function SetTextMeshProUGUILeft2Right(text)
    if text.alignment == TextAlignmentOptions.TopLeft then
        text.alignment = TextAlignmentOptions.TopRight
    elseif text.alignment == TextAlignmentOptions.Left then
        text.alignment = TextAlignmentOptions.Right
    elseif text.alignment == TextAlignmentOptions.BottomLeft then
        text.alignment = TextAlignmentOptions.BottomRight
    end
end
function SetTextMeshProUGUIRight2Left(text)
    if text.alignment == TextAlignmentOptions.TopRight then
        text.alignment = TextAlignmentOptions.TopLeft
    elseif text.alignment == TextAlignmentOptions.Right then
        text.alignment = TextAlignmentOptions.Left
    elseif text.alignment == TextAlignmentOptions.BottomRight then
        text.alignment = TextAlignmentOptions.BottomLeft
    end
end
function SetTextTruncate2OverFlowIfNotBestFit(text)
    if not text.resizeTextForBestFit then
        text.verticalOverflow = VerticalWrapMode.Overflow
    end
end
function ReverseLayout(parent)
    local childCount = parent.transform.childCount
    local childList = {}
    for i = 0, childCount - 1 do
        local child = parent.transform:GetChild(i)
        table.insert(childList, child)
    end
    for i = 1, childCount do
        local child = childList[i]
        child:SetAsFirstSibling()
    end
end
function FixSlash(left, right)
    if lang.GetUseLang() == lang.AR then
        return right .. "/" .. left
    else
        return left .. "/" .. right
    end
end
function GetTextAnchor(option)
    return TextAnchor[option]
end
function GetTextAlignmentOptions(option)
    return TextAlignmentOptions[option]
end
function GetHorizontalWrapMode(option)
    return HorizontalWrapMode[option]
end
function GetHorizontalWrapMode(option)
    return VerticalWrapMode[option]
end

---@deprecated 联盟简称拼接
---@param shortName string      联盟简称
---@param text string           被拼接的字符串(可不传）
function SplicingUnionShortName(shortName, text, needSpace)
    if not shortName or shortName == "" then
        return text or ""
    end
    local str = string.format("[%s]", shortName)
    if text then
        if needSpace then
            return str .. " " .. text
        else
            return str .. text
        end
    else
        return str
    end
end

---@public 转换百分比为字符串 % 保留两位小数
---@param number number
function ConvertPercentageToString(number)
    return string.format("%.2f%%", number / 10000)
end
---@public 转换百分比为字符串 + % 保留两位小数
---@param number number
function ConvertAddPercentageToString(number)
    return string.format("+%.2f%%", number / 10000)
end

--@public 转换数字为百分比字符串 % （整数）
---@param number number
function ConvertPercentageToInt(number)
    return string.format("%d%%", number * 100)        --0.8 -> 80%
end

--@public 万分数转换为百分比（整数）
function ConvertPercentToString(number)
    return string.format("%.2f%%", number / 100)
end

function GetErrorLangText(enErr)
    if type(enErr) ~= "number" then
        print("Invalid argument type for 'enErr', expected number.")
    else
        return lang.Get(100000 + enErr)
    end
end
EnumColor = {
    Red_Light = "#FF4D2A",
    Red_Dark = "#E63838",
    Red_Dark2 = "#CC2727",
    Green = "#87F425",
    Blue = "#5BCBFF",
    Yellow = "#FFDD22",
    Brown = "#795B27",
    Green_Dark="#319F38",
    White = "#FFFFFF",
}
EnumColorQuality = {
    White = "#B0C4CC",
    Green = "#6FE978",
    Blue = "#58A2F0",
    Purple = "#BF66FD",
    Orange = "#F1BA4E",
    Red = "#FF0000",
    Clor1 = { "#60EAFF", "#EAB9FF" },
    Clor2 = { "#FEFFA0", "#FF73F3" }
}
function SetColor(strColor, str)
    return string.format("<color=%s>%s</color>", strColor, str)
end
function ProtoTest(msgName, proFileName, msgStruct, strJson)
    local ui_window_mgr = require "ui_window_mgr"
    local wnd = ui_window_mgr:ShowModule("ui_send_msg")
    DelayCallOnce(0.2, function()
        --if wnd and wnd:IsValid() then
        wnd:SetMessage(msgName, proFileName, msgStruct, strJson)
        --end
    end)
end

--@public 保留两位小数
function FormatNumber_2(number)
    if math.floor(number) == number then
        -- 如果是整数，直接返回整数
        return tostring(number)
    else
        -- 如果是小数，保留两位小数
        return string.format("%.2f", number)
    end
end


---@public 获取一个有效的pb number 因为pb下发0的数据会被计息成2147483647
function GetValidPBNumber(originNumber)
    if originNumber == 2147483647 then
        return 0
    end
    return originNumber
end

function LuaGetArrayIndex(array, index)
    if GetArrayIndex then
    	return GetArrayIndex(array, index)
    else
        return array[index]
    end
end

function LuaGetComponentsInChildren(gameObject, genericType, includeInactive)
	if includeInactive == nil then
		return gameObject:GetComponentsInChildren(genericType)
	else
		return gameObject:GetComponentsInChildren(genericType, includeInactive)
	end
end


