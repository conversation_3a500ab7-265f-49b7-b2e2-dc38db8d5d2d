local cysoldierssortie_comp_base_bullet_entity = require("cysoldierssortie_comp_base_bullet_entity")
local cysoldierssortie_comp_bullet_curve_attack_entity = bc_Class("cysoldierssortie_comp_bullet_curve_attack_entity", cysoldierssortie_comp_base_bullet_entity) --类名用小游戏名加后缀保证全局唯一

local ApiHelper = CS.XLuaUtil.LuaApiHelper
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local bc_Time = bc_Time
local log = log
local bc_IsNotNull  = bc_IsNotNull
local Quaternion = CS.UnityEngine.Quaternion
local Physics = CS.UnityEngine.Physics
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local DoLookAt = ApiHelper.DoLookAt
local CSUpdateEvent = cysoldierssortie_EventName.CSUpdate
local Color = CS.UnityEngine.Color
local Vector3 = bc_CS_Vector3
local ParticleSystem = CS.UnityEngine.ParticleSystem
local ParticleSystemStopBehavior = CS.UnityEngine.ParticleSystemStopBehavior
local TrailRenderer = CS.UnityEngine.TrailRenderer
local Tweening = CS.DG.Tweening
local DOTween = CS.DG.Tweening.DOTween

local config =
{
    isFromCsv = true,
    throwDistance = 50,
    throwStartSpeed = 50,

    isDirectLineThrow = false,
    isOpenSelfRotation = false,
    isDebugPath = false,

    rotationAxis = Vector3.forward,
    rotationSpeed = 900,
    rotationEasing = Tweening.Ease.Linear,

    ellipsePoints = 20,
    gravity = 9.81,                           -- 重力加速度（米/秒^2）
    throwDuration = 2.5,
    throwEasing = Tweening.Ease.Linear,

    -- 额外弧高（视觉抬升，不影响命中），钟形权重 4t(1-t)
    arcHeightBonus = 1.5,

    -- 粒子延迟播放：射出后延迟多少帧再 Play 粒子（0=立即）
    particlePlayDelayFrames = 0,


    -- 发射起点定制参数（按使用者类型）
    buildingSpawnUpOffset = 1,             -- 建筑：在碰撞盒顶部基础上再上抬
    buildingSpawnForwardOffset = 0.0,        -- 建筑：沿发射者forward前移

    afterArriveStay = 0,                   -- 到达落点后停留时间(秒)再回收，避免“未命中就回收”的观感
    -- 终点前穿透命中后继续向前的距离设置
    forwardOvershoot = 0.0,                   -- 命中点后继续前进的距离（米）；为0则不前冲
    forwardOvershootRatio = 0,                 -- 也可用比例：overshoot = distToTarget * ratio（>0时覆盖上面的绝对值）
}

local function DrawDebugPath(path, isReturn)
    local color = isReturn and Color.green or Color.red

    for i = 1, #path - 1 do
        CS.UnityEngine.Debug.DrawLine(path[i], path[i+1], color, 5.0)
    end

    for i = 1, #path do
        CS.UnityEngine.Debug.DrawLine(
                path[i] + Vector3.up * 0.1,
                path[i] - Vector3.up * 0.1,
                Color.red,
                5.0
        )
    end
end

local function CreateArcPath_Gravity(startPos, endPos, segments, v0, g)
    segments = segments or config.ellipsePoints
    g = g or config.gravity

    local path = {}
    local dx = endPos.x - startPos.x
    local dz = endPos.z - startPos.z
    local horizontalDist = math.sqrt(dx*dx + dz*dz)

    -- 水平初速度（XZ 平面）
    local v0_h = v0
    if not v0_h or v0_h <= 0 then
        v0_h = (config.isFromCsv and v0) or config.throwStartSpeed
        if not v0_h or v0_h <= 0 then v0_h = 10 end
    end

    -- 飞行总时长：t = s / v
    local T = horizontalDist / v0_h
    if T <= 0 then T = config.throwDuration end

    -- 水平方向单位向量
    local ux = dx / (horizontalDist == 0 and 1 or horizontalDist)
    local uz = dz / (horizontalDist == 0 and 1 or horizontalDist)

    -- 根据起终点和时长反推垂直初速度，确保重力弹道精准命中
    local dy = (endPos.y or startPos.y) - startPos.y
    local v0y = (dy + 0.5 * g * T * T) / (T == 0 and 1 or T)

    for i = 1, segments do
        local t = (i - 1) / (segments - 1)
        local time = T * t
        local x = startPos.x + ux * v0_h * time
        local z = startPos.z + uz * v0_h * time
        local y = startPos.y + v0y * time - 0.5 * g * time * time
        if config.arcHeightBonus and config.arcHeightBonus ~= 0 then
            y = y + config.arcHeightBonus * 4 * t * (1 - t)
        end
        table.insert(path, Vector3(x, y, z))
    end

    -- 强制最后一个点对齐到目标点（确保落点就是目标）
    if segments > 0 then
        path[segments] = Vector3(endPos.x, endPos.y or path[segments].y, endPos.z)
    end

    if config.isDebugPath then DrawDebugPath(path, false) end
    return path, T
end


function cysoldierssortie_comp_bullet_curve_attack_entity.__init(self, parent)
    self._isBulletSystem = false
    self._isAutoRecycle = false
    cysoldierssortie_comp_base_bullet_entity.__init(self, parent)
end

function cysoldierssortie_comp_bullet_curve_attack_entity:CreateData(data)
    cysoldierssortie_comp_base_bullet_entity.CreateData(self, data)

    self._direction = data.direction
    -- 目标优先级：data.targetPos > data.targetObj > 按方向+距离
    self._targetPos = data.targetPos
    if (not self._targetPos) and data.targetObj and data.targetObj.transform then
        local tx, ty, tz = GetTransformPositionXYZ(data.targetObj.transform)
        self._targetPos = { x = tx, y = ty, z = tz }
    end
    -- 统一设置发射起点，兼容士兵/建筑不同发射方：优先 data.spawnTransform > data.spawnPos > 角色武器点/建筑顶部 > 当前
    --if data.spawnTransform and bc_IsNotNull(data.spawnTransform) then
    --    SetTransformPositionByTransform(self.transform, data.spawnTransform)
    --elseif data.spawnPos and data.spawnPos.x then
    --    SetTransformPositionXYZ(self.transform, data.spawnPos.x, data.spawnPos.y or 0, data.spawnPos.z)
    --else
        -- 根据使用者类型定制默认起点
       if self._character and self._character._unit_type == cysoldierssortie_unit_type.Building then
            -- 建筑：默认使用建筑碰撞盒顶部，再加微量上抬和前移
            local topPos = nil
            if self._character._player and self._character._player.GetColliderTopPosition then
                topPos = self._character._player:GetColliderTopPosition()
            end
            if topPos then
                local fwd = self._character.transform.forward
                SetTransformPositionXYZ(
                    self.transform,
                    topPos.x + fwd.x * (config.buildingSpawnForwardOffset or 0),
                    topPos.y + (config.buildingSpawnUpOffset or 0),
                    topPos.z + fwd.z * (config.buildingSpawnForwardOffset or 0)
                )
            else
                SetTransformPositionByTransform(self.transform, self._character.transform)
            end
        else
            -- 兜底：角色武器点 > 角色位置
            if self._character and self._character._weaponRoot and bc_IsNotNull(self._character._weaponRoot) then
                SetTransformPositionByTransform(self.transform, self._character._weaponRoot)
            elseif self._character then
                SetTransformPositionByTransform(self.transform, self._character.transform)
            end
        end
    --end
    local posX, posY, posZ = GetTransformPositionXYZ(self.transform)
    self._startPos = { x = posX, y = posY, z = posZ }

    local throwDis
    if self._targetPos then
        local dx = self._targetPos.x - self._startPos.x
        local dz = self._targetPos.z - self._startPos.z
        throwDis = math.sqrt(dx*dx + dz*dz)
        -- 在目标基础上向前“前冲”一段，防止在怪物前停下
        local overshoot = (config.forwardOvershootRatio and config.forwardOvershootRatio>0) and (throwDis * config.forwardOvershootRatio) or (config.forwardOvershoot or 0)
        local ux, uz = (throwDis>0 and dx/throwDis or 0), (throwDis>0 and dz/throwDis or 0)
        local hitY = self._targetPos.y or self._startPos.y
        if data.targetObj then
            local col = data.targetObj:GetComponentInChildren(typeof(CS.UnityEngine.Collider), true)
            if col then
                local b = col.bounds
                -- 取碰撞盒高度/2，加到底部（min.y），相当于中心高度
                hitY = b.min.y + (b.size.y * 0.5)
            end
        end
        self._hitPos = { x = self._targetPos.x, y = hitY, z = self._targetPos.z }
        self._endPos = { x = self._hitPos.x + ux*overshoot, y = self._hitPos.y, z = self._hitPos.z + uz*overshoot }
        throwDis = throwDis + overshoot
    else
        if config.isFromCsv then
            if (not self._ballisiticRange) or self._ballisiticRange <= 0 then
                throwDis = config.throwDistance
            else
                throwDis = self._ballisiticRange
            end
        else
            throwDis = config.throwDistance
        end
        self._endPos = {
            x = self._startPos.x + self._direction.x * throwDis,
            y = self._startPos.y,
            z = self._startPos.z + self._direction.z * throwDis,
        }
    end

    -- 粒子延迟播放：先隐藏/禁用，再按帧延迟后Play
    local frames = config.particlePlayDelayFrames or 0
    self:PrepareParticlesForDelayedPlay()
    if frames <= 0 then
        self:ResetAndPlayParticle()
    else
        self._delayParticleTimer = cysoldierssortie_DelayCallOnce(frames * (1/60), function()
            if not self._invalid then self:ResetAndPlayParticle() end
            self._delayParticleTimer = nil
        end)
    end

    self:CreateFlightSequence(throwDis)

    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self.eventMgr:RegisterEvt(self, CSUpdateEvent)
end

function cysoldierssortie_comp_bullet_curve_attack_entity:CreateFlightSequence(throwDis)
    local v_h = (self._ballisiticVelocity and self._ballisiticVelocity > 0) and self._ballisiticVelocity or config.throwStartSpeed

    -- 1) 到命中点（hitPos，如果无target则=endPos）
    local hitPos = self._hitPos or self._endPos
    local path1, T1 = CreateArcPath_Gravity(self._startPos, hitPos, config.ellipsePoints, v_h, config.gravity)

    -- 2) 从命中点到前冲终点 endPos（保持高度不再上拱，作直线轻微下落）
    local overshootDist = 0
    if self._hitPos then
        local dx, dz = self._endPos.x - hitPos.x, self._endPos.z - hitPos.z
        overshootDist = math.sqrt(dx*dx + dz*dz)
    end
    local path2 = {}
    local seg2 = math.max(2, math.floor(config.ellipsePoints * (overshootDist > 0 and math.min(0.3, overshootDist / (throwDis>0 and throwDis or 1)) or 0)))
    if overshootDist > 0 and seg2 > 1 then
        for i=1,seg2 do
            local t = (i-1)/(seg2-1)
            local x = hitPos.x + (self._endPos.x - hitPos.x) * t
            local z = hitPos.z + (self._endPos.z - hitPos.z) * t
            local y = hitPos.y - (config.gravity * 0.5) * ( (overshootDist / v_h) * t )^2 -- 轻微下落
            path2[i] = Vector3(x,y,z)
        end
    end

    self:LookAtPosition(Vector3(hitPos.x, hitPos.y, hitPos.z))
    self.sequence = DOTween.Sequence()

    local duration1 = (T1 and T1 > 0) and T1 or config.throwDuration
    local tween1 = self.transform:DOPath(path1, duration1, Tweening.PathType.CatmullRom)
    tween1:SetEase(config.throwEasing)
    tween1:SetRecyclable(true)
    tween1:SetLink(self.gameObject)
    if not config.isOpenSelfRotation then tween1:SetLookAt(0) else self.rotateTween = self:DORotate() end
    self.sequence:Append(tween1)

    if #path2 > 1 then
        local duration2 = overshootDist / v_h
        local tween2 = self.transform:DOPath(path2, duration2, Tweening.PathType.CatmullRom)
        tween2:SetEase(Tweening.Ease.Linear)
        tween2:SetRecyclable(true)
        tween2:SetLink(self.gameObject)
        tween2:SetLookAt(0)
        self.sequence:Append(tween2)
    end

    -- 到达最终终点后停留再回收
    self.sequence:AppendInterval(config.afterArriveStay or 0)
    self.sequence:AppendCallback(function()
        if bc_IsNotNull(self.transform) then self:Release() end
    end)

    self.sequence:Play()
end

function cysoldierssortie_comp_bullet_curve_attack_entity:CSUpdate()
    -- 走 DOTween Path，不需要手写位移/返回逻辑
end

function cysoldierssortie_comp_bullet_curve_attack_entity:LookAtPosition(targetPos, isSmoothRotate)
    local dirToTarget = targetPos - self.transform.position
    dirToTarget.y = 0
    if dirToTarget.magnitude < 0.8 then
        return
    end
    local targetRotation = Quaternion.LookRotation(dirToTarget)
    if isSmoothRotate then
        if bc_CS_Vector3.Angle(self.transform.forward, dirToTarget) <= 5 then
            self.transform.rotation = targetRotation
            return
        end
        self.transform.rotation = Quaternion.Slerp(
                self.transform.rotation,
                targetRotation,
                bc_Time.deltaTime * 10
        )
    else
        self.transform.rotation = targetRotation
    end
end

function cysoldierssortie_comp_bullet_curve_attack_entity:Release()
    self:UnRegisterEvt()
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if poolMgr then
        poolMgr:ReleaseObj(self)
    end
end

function cysoldierssortie_comp_bullet_curve_attack_entity:Dispose()
    self:UnRegisterEvt()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
    if self.rotateTween then
        self.rotateTween:Kill()
        self.rotateTween = nil
    end
    if not self._modelRoot then
        return
    end
    local psList = self._modelRoot:GetComponentsInChildren(typeof(ParticleSystem), true)
    if psList and psList.Length and psList.Length > 0 then
        for i=0, psList.Length-1 do
            local ps = psList[i]
            if ps then
                ps:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
                ps:Clear(true)
            end
        end
    end
end

function cysoldierssortie_comp_bullet_curve_attack_entity:UnRegisterEvt()
    if self._delayParticleTimer then
        cysoldierssortie_StopDelayCall(self._delayParticleTimer)
        self._delayParticleTimer = nil
    end
    if self.eventMgr then
        self.eventMgr:UnRegisterEvt(self, CSUpdateEvent)
        self.eventMgr = nil
    end
end

-- 覆盖触发：记录命中的Collider，供特效落点修正使用
--function cysoldierssortie_comp_bullet_curve_attack_entity:OnTriggerEnter(collider)
--    self._lastHitCollider = collider
--    cysoldierssortie_comp_base_bullet_entity.OnTriggerEnter(self, collider)
--    self._lastHitCollider = nil
--end

-- 覆盖受击特效位置：默认使用碰撞盒中心高度
function cysoldierssortie_comp_bullet_curve_attack_entity:PlayHitEffectXYZ(pX, pY, pZ)
    local fx, fy, fz = pX, pY, pZ
    local col = self._lastHitCollider
    if col then
        local b = col.bounds
        if b then
            fx = b.center.x
            fy = b.min.y + (b.size.y * 0.5)
            fz = b.center.z
        end
    elseif self._hitPos then
        fx, fy, fz = self._hitPos.x, self._hitPos.y, self._hitPos.z
    end
    cysoldierssortie_comp_base_bullet_entity.PlayHitEffectXYZ(self, fx, fy, fz)
end

function cysoldierssortie_comp_bullet_curve_attack_entity:DORotate()
    local rotationTween = self.transform:DORotate(
            config.rotationAxis * 360,
            360 / config.rotationSpeed,
            Tweening.RotateMode.LocalAxisAdd
    )

    rotationTween:SetLoops(-1, Tweening.LoopType.Restart)
    rotationTween:SetEase(config.rotationEasing)
    rotationTween:SetRecyclable(true)
    rotationTween:SetLink(self.gameObject)

    return rotationTween
end


-- 发射前：确保粒子隐藏/禁用，避免延迟期间误播
function cysoldierssortie_comp_bullet_curve_attack_entity:PrepareParticlesForDelayedPlay()
    if not self._modelRoot then return end
    local psList = self._modelRoot:GetComponentsInChildren(typeof(ParticleSystem), true)
    if psList and psList.Length and psList.Length > 0 then
        for i=0, psList.Length-1 do
            local ps = psList[i]
            if ps then
                ps:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
                ps:Clear(true)
                -- 同时禁用Renderer以彻底隐藏（避免某些材质自发光残留）
                local rend = ps:GetComponent(typeof(CS.UnityEngine.Renderer))
                if rend then rend.enabled = false end

                ---- 关闭TrailRenderer避免残影
                --local trail = ps:GetComponent(typeof(TrailRenderer))
                --if trail then trail.enabled = false end
            end
        end
    end
    local trailList = self._modelRoot:GetComponentsInChildren(typeof(TrailRenderer), true)
    if trailList and trailList.Length and trailList.Length > 0  then
        for i=0, trailList.Length-1 do
            local trail = trailList[i]
            if trail then
                trail:Clear()
                trail.enabled = false
            end
        end
    end
end

function cysoldierssortie_comp_bullet_curve_attack_entity:ReleaseEntity()
    cysoldierssortie_comp_base_bullet_entity.ReleaseEntity(self)
end

function cysoldierssortie_comp_bullet_curve_attack_entity.__delete(self)
    self:Dispose()
    cysoldierssortie_comp_base_bullet_entity.__delete(self)
end

function cysoldierssortie_comp_bullet_curve_attack_entity:ResetAndPlayParticle()
    if not self._modelRoot then
        return
    end
    local psList = self._modelRoot:GetComponentsInChildren(typeof(ParticleSystem), true)
    if psList and psList.Length and psList.Length > 0 then
        for i=0, psList.Length-1 do
            local ps = psList[i]
            if ps then
                -- 取消隐藏，开始播放
                local rend = ps:GetComponent(typeof(CS.UnityEngine.Renderer))
                if rend then rend.enabled = true end
                ps:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
                ps:Clear(true)
                ps:Play(true)
            end
        end
    end
    local trailList = self._modelRoot:GetComponentsInChildren(typeof(TrailRenderer), true)
    if trailList and trailList.Length and trailList.Length > 0  then
        for i=0, trailList.Length-1 do
            local trail = trailList[i]
            if trail then
                trail:Clear()
                trail.enabled = true
            end
        end
    end
end

---- 子弹回收前：显式停止与清理粒子（避免高射速下叠加）
--local __Release_Super = cysoldierssortie_comp_bullet_curve_attack_entity.Release
--function cysoldierssortie_comp_bullet_curve_attack_entity:Release()
--    if self._modelRoot then
--        local psList = self._modelRoot:GetComponentsInChildren(typeof(ParticleSystem), true)
--        if psList and psList.Length and psList.Length > 0 then
--            for i=0, psList.Length-1 do
--                local ps = psList[i]
--                if ps then
--                    ps:Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear)
--                    ps:Clear(true)
--                end
--            end
--        end
--    end
--    __Release_Super(self)
--end

return cysoldierssortie_comp_bullet_curve_attack_entity