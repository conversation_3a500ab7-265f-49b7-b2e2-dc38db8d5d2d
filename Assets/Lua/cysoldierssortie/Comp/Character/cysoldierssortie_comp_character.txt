local cysoldierssortie_comp_character = bc_Class("cysoldierssortie_comp_character") --类名用小游戏名加后缀保证全局唯一
local Vector3 = CS.UnityEngine.Vector3
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local Quaternion = CS.UnityEngine.Quaternion
local require = require
local cysoldierssortie_config_feedback = require("cysoldierssortie_config_feedback")
local math = math
local GCPerf = GCPerf
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local KillTimer = cysoldierssortie_KillTimer
local bc_IsNotNull = bc_IsNotNull
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_character_state = cysoldierssortie_character_state
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_comp_name = cysoldierssortie_comp_name
local LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem
local LookAtTargetSystemInstance
local minigame_buff_mgr= require "minigame_buff_mgr"
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local bc_Time = bc_Time
local cysoldierssortie_common_unit_prop = cysoldierssortie_common_unit_prop
local log = log
local cysoldierssortie_CharacterOtherEffect = cysoldierssortie_CharacterOtherEffect
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local minigame_mgr = require "minigame_mgr"
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_player_action_state = cysoldierssortie_player_action_state
local SetTransformByTransform = ApiHelper.SetTransformByTransform
local cysoldierssortie_dotween_extern = require("cysoldierssortie_dotween_extern")
local bc_DOTweenHelper = bc_DOTweenHelper
local Tweening = CS.DG.Tweening
local CreateLinearKnockback = ApiHelper.CreateLinearKnockback
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local bc_CS_Vector3 = bc_CS_Vector3
local isAsync = isAsync
local debug = debug
local xpcall =  xpcall
local cysoldierssortie_damage_type = cysoldierssortie_damage_type
local cysoldierssortie_EventName = cysoldierssortie_EventName

--动态血量DEBUG 
local DEBUG_DYNAMIC_HP = false

-------------------------------------------------- 用于小兵大作战，攻击动画和攻击状态同步
---@type boolean
cysoldierssortie_comp_character.troopClash_IgnoreBattleUpdate = nil
---@type boolean 忽略AI状态机更新
cysoldierssortie_comp_character.ignoreUpdateAI = nil
---@type boolean 是否正在播放攻击动画，控制是否能中断攻击状态
cysoldierssortie_comp_character.AtkingFlag = nil
---@type number 攻击动画时长，单位S
cysoldierssortie_comp_character.AtkClipLength = nil
cysoldierssortie_comp_character.AtkTimer = nil
---末尾X秒内可以切换状态
local endAtkTime = 0.15
--------------------------------------------------

function cysoldierssortie_comp_character:__init()
    local res = xpcall(function()
        LookAtTargetSystemInstance = LookAtTargetSystem.Instance
    end,debug.traceback)
    
    ---@type cysoldierssortie_mgr_cardBuff
    self.cardBuffMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.CardBuffMgr)
end


function cysoldierssortie_comp_character:CreateData(data)
    --self._damageCoefficient = data.DamageCoefficient
    self._initAttack=data.Attack
    self._attack = data.Attack
    self._heroFlag = data.UnitType == cysoldierssortie_unit_type.Hero

    if data.UnitType and (data.UnitType == cysoldierssortie_unit_type.Soldier or data.UnitType == cysoldierssortie_unit_type.Soldier2) and data.AtkIncreaseRatioByLevel then
        self._attack = self._attack +  self._attack * (data.AtkIncreaseRatioByLevel/100)
    end
    self._attackRange = data.AttackRange
    self._heroId = data.HeroID
    ---@type number 记录原始血量，用于血量加成计算
    self._oriHp = data.HP
    if self.cardBuffMgr then
        self._hp = self.cardBuffMgr:GetHpMax(self._oriHp, self._heroFlag)
    else
        self._hp = data.HP
    end
    -- 记录当前最大生命值，用于血量加成计算
    self._lastHpMax = self._hp
    self._star = data.Star or 0
    self._curHp = self._hp
    self._character_entity = self._character_entity
    self._position = Vector3.zero
    self.transform = self.transform
    self._characterState = self._characterState or cysoldierssortie_character_state.Idle
    self._player = data.player
    self._targetObjs = self._targetObjs
    self._weaponRoot = self._weaponRoot
    self._weaponSpawnPoint = self._weaponSpawnPoint
    self._unit_type = data.UnitType
    self._moveSpeed = data.MoveSpeed
    self._enemyRange = data.EnemyRange
    self.comps = self.comps or {}
    self._localPos = data.LocalPos
    self._unitID = data.UnitID
    self._bloodOffset = data.BloodOffset
    self._level = data.Level or 1
    self._hpScale = data.HpScale or 1
    
    self._rewardCoin = data.RewardCoin or 0
    if data.SoldierDpsCoe then
        self._soldierDpsCoe = data.SoldierDpsCoe / 100   --士兵dps系数
    else
        self._soldierDpsCoe=0
    end

    self._dynamicAddHp = 0
    
    if self._unit_type == cysoldierssortie_unit_type.Drone then--神兽
        self._isDrone=true
    else
        self._isDrone=false
    end

    if self._unit_type == cysoldierssortie_unit_type.Building then--建筑
        self._isBuilding=true
    else
        self._isBuilding=false
    end

    self._targetGo = nil
    if data.Scale then
        self._scale = data.Scale > 0 and data.Scale or 1
    end

    --即使是英雄，通过道具获得也算在士兵集合中
    self._lst_group_type = data.LstGroupType
    self._hitDeceleration = data.HitDeceleration
    
    --当前实体是否显示
    self._view_actor = true
    
    --唯一sid 先随机写
    self._sid = data.Sid or  math.random(1000, 100000)
    
    self._weapons = data.Weapons
    
    self._disToPlayerZ = 1000
    
    self._curAnimState = nil
    
    self._skillReleaseTrans = nil
    
    --冻结旋转,某些情况下 比如释放某种技能此时角色的旋转不允许转向
    self._freezeRotate = false
    
    self._boundSize = 0.5
    self._boundHeight = 1
    
    self._feedBackData = {}
    self._feedBackData.feedback_use = {}
    
    self._patrol = data.Patrol
    
    self._animationStates = nil

    -- 被动技能
    self._passiveSkills = data.PassiveSkills

    self.ignoreUpdateAI = false

    self.cysoldierssortie_TroopClash = cysoldierssortie_TroopClash
    self.cysoldierssortie_KingShot = cysoldierssortie_KingShot or cysoldierssortie_KingShot_HOME
    --- 有些被动buff一定需要SpawnPointComp，小兵大作战这里特殊处理
    if self.cysoldierssortie_TroopClash then
        self.SpawnPointComp = self
    end
    
    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    if self._player and self.cardBuffMgr then
        -- 注册增加最大生命值的卡牌系统事件, 只有玩家需要注册
        self.eventMgr:RegisterEvt(self, cysoldierssortie_EventName.CardBuffAddMaxHpListener)
    end
end

function cysoldierssortie_comp_character:UpdateBoundSize(size,height)
    self._boundSize = size
    self._boundHeight = height
end

function cysoldierssortie_comp_character:SetFreezeRotate(freeze)
    self._freezeRotate = freeze
end

local MaxQualityLimit = 5 --击退质量限制
local MaxForce = 5 --击退力度
local MaxDuration = 1.2 --击退时间

-- 基础直线击退效果
local KnockBackDir  = {}
function cysoldierssortie_comp_character:CreateLinearKnockback()
    local qualityPercent = math.min(1, self._boundSize / MaxQualityLimit)
    qualityPercent = 1 - qualityPercent
    if qualityPercent < 0.1 then
        return
    end
    local force =  qualityPercent * MaxForce
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    local player = curLevel.playerLua
    local playerPosX,playerPosY,playerPosZ = player:GetPositionXYZ()
    local posX,posY,posZ = GetTransformPositionXYZ(self.transform)
    KnockBackDir.x = posX - playerPosX
    KnockBackDir.y = 0
    KnockBackDir.z = posZ - playerPosZ
    local normalizeX,normalizeY,normalizeZ = cysoldierssortie_lua_util:Normalize(KnockBackDir.x,KnockBackDir.y,KnockBackDir.z)
    KnockBackDir.x = normalizeX
    KnockBackDir.y = normalizeY
    KnockBackDir.z = normalizeZ
    
    local res = xpcall(function()
        ApiHelper.CreateLinearKnockback(self.transform,force,MaxDuration,KnockBackDir.x,KnockBackDir.y,KnockBackDir.z)
    end,debug.traceback)
    if not res then
        -- 计算击退方向
        local direction = KnockBackDir
        -- 计算目标位置
        local csharpDir = bc_CS_Vector3(direction.x,direction.y,direction.z)
        local targetPos = self.transform.position + csharpDir * force
        -- 执行击退动画
        bc_DOTweenHelper.MOVE(self.transform,targetPos,MaxDuration,nil,Tweening.Ease.OutQuad)
    end
end

function cysoldierssortie_comp_character:GetStarLvAddCoefficient(weaponData)
    if not self._weaponAddCoeByStarLvs then
        self._weaponAddCoeByStarLvs = {}
    end
    
    if  self._weaponAddCoeByStarLvs[weaponData._skillID] then
        return self._weaponAddCoeByStarLvs[weaponData._skillID]
    end
    local coe  = nil
    if weaponData._starRatingIncrease then
        if self.heroData then
            local starLv =   math.floor(self.heroData.numProp.starLv / 5)
            if starLv then
                local array = weaponData._starRatingIncrease.data
                if array and array[starLv] and #array >= starLv then
                    coe = array[starLv] / 10000
                    if coe then
                        self._weaponAddCoeByStarLvs[weaponData._skillID] = coe
                    end 
                end
            end
        end
    end
    return coe
end

function cysoldierssortie_comp_character:CreateEntity(parent,playEffect)
    --local character_go = NeeGame.PoolObject(cysoldierssortie_PoolObjectName.Hero, parent)
   -- character_go.transform.localRotation = Quaternion.identity
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not poolMgr then
        return
    end
    self._character_entity = poolMgr:AcquireObj("cysoldierssortie_comp_character_entity",parent,self)
    if playEffect then
        self._character_entity:PlayNewGetEffect()
    end
    
    --添加组件
    local unit_type = self._unit_type
    local cysoldierssortie_config_character_comps
    if self.cysoldierssortie_TroopClash then
        cysoldierssortie_config_character_comps = require("troopclash_config_character_comps")
    elseif self.cysoldierssortie_KingShot then
        cysoldierssortie_config_character_comps = require("kingshot_config_character_comps")
    else
        cysoldierssortie_config_character_comps = require("cysoldierssortie_config_character_comps")
    end
    
    local comps =  cysoldierssortie_config_character_comps.UnitTypeCompsConfig[unit_type]
    if comps then
        for i=1,#comps do
            cysoldierssortie_config_character_comps.CharacterCompsConfig[comps[i]]._addComp(self)
        end
    end
    
    if self.cysoldierssortie_TroopClash and self._unit_type == cysoldierssortie_unit_type.BossEnemy then
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        levelMgr.sceneMgr.uiMgr:AddBossGuide(self)
    end
end

function cysoldierssortie_comp_character:SetPosAndParent(pos,parent)
    self._localPos = pos
    if not self._character_entity then
        return
    end

    self._character_entity.transform:SetParent(parent)
    self._character_entity.transform.localPosition = pos
end

function cysoldierssortie_comp_character:GetPosition()
    if self._character_entity then
        return self._character_entity.transform.position
    end
end

function cysoldierssortie_comp_character:LevelUp(newLevel,isPlayEffect)
    local levelComp = self.comps[cysoldierssortie_comp_name.Level]
    if levelComp then
        levelComp:LevelUp(newLevel,isPlayEffect)
    end
end

function cysoldierssortie_comp_character:GetFsmState()
    local fsmComp = self.comps[cysoldierssortie_comp_name.AI]
    if fsmComp then
       return fsmComp._currentState
    end
    return cysoldierssortie_character_state.Idle
end

function cysoldierssortie_comp_character:ReplaceEntity(newLevel)
    if self._level >= 5 then
        return
    end
    
    self:RemoveComponent(cysoldierssortie_comp_name.Attack)

    if self._character_entity then
        --self._character_entity:ReleaseModel()
    end

    if not newLevel then
        self._level = self._level+1
    else
        self._level = newLevel
    end

    if self._unit_type == cysoldierssortie_unit_type.Hero or self._isDrone then

        local newUnitID =self._player:GetUnitIDByHeroID(self._heroId,self._level)
        if newUnitID then
            self._unitID=newUnitID
        end

    else
        local level_mgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        if level_mgr then
            local curLevel = level_mgr.curLevel
            if curLevel then
                self._unitID = curLevel:GetUseSoldier(self._level)
            end
        end
    end


    if self._player then
        local actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
        local newData = actor_instance_mgr:CreateCharacterData(self._unitID,nil,nil,nil,nil,self._player)
        newData.LocalPos = self._localPos
        self:CreateData(newData)
        self._player:CreateAttackRange(self._unitID,self._attackRange)
    end
    
    if self._character_entity then
        --self._character_entity:CreateModel()
    end

    local attack_comp = self:AddComponent(cysoldierssortie_comp_name.Attack, { character = self })
    
    if self:GetFsmState()==cysoldierssortie_character_state.Attack then
        local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
        if attackMgr:EnableProxy(self._unitID,self._unit_type) then
            if attackMgr then
                attackMgr:RegisterAttackCompProxy(attack_comp)
            end
        end
    end
    self:ResetCooldown()
end

function cysoldierssortie_comp_character:SetViewActor(state)
    self._view_actor = state
    if state then
        local autoDeadTime = 40
        if self._unit_type == cysoldierssortie_unit_type.NormalEnemy then
            --self._autoDelayDeadTimer =  cysoldierssortie_DelayCallOnce(autoDeadTime,function()
            --    self:Dead()
            --end)
        end
        local entity = self._character_entity
        if entity then
            if not entity._cull then
                if entity._entity then
                    EntityHybridUtility.SetEnable(entity._entity,true)
                else
                    if bc_IsNotNull(entity.modelGo) then
                        entity.modelGo:SetActive(true)
                    end
                end
            end
        end
    end
end

--- 监听卡牌buff中，玩家增加最大生命值事件。要加上之前的动态血量系统
---@param hpNum number 固定生命值
---@param hpRate number 百分比生命值
function cysoldierssortie_comp_character:CardBuffAddMaxHpListener(hpNum, hpRate, hpNumSolider)
    local newHpMax = 0
    --英雄
    if self._heroFlag then
        newHpMax = (self._oriHp + self._dynamicAddHp + hpNum) * (1 + hpRate * 0.01)
    else
        newHpMax = self._oriHp + self._dynamicAddHp + hpNumSolider
    end
    local offHpMax = math.ceil(newHpMax - self._lastHpMax)
    if offHpMax ~= 0 then
        self._curHp = self._curHp + offHpMax
        self._hp = self._hp + offHpMax
        self._lastHpMax = self._hp
        if offHpMax > 0 then
            self:ShowAddHpText(offHpMax)
        end
    end
end

--- 回血，当前血量不超过最大值
function cysoldierssortie_comp_character:AddHpByRegen(num)
    local offHp = math.max(num, self._hp - self._curHp)
    if offHp > 0 then
        self._curHp = self._curHp + offHp
        self:ShowAddHpText(offHp)
    end
end

function cysoldierssortie_comp_character:ShowAddHpText(offHp)
    local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
    if hp_comp then
        local pX, pY, pZ = GetTransformPositionXYZ(self.transform)
        hp_comp:SpawnHpNumAniText(offHp, false, pX, pY, pZ, 0, cysoldierssortie_damage_type.AddHp, false)
    end
end

function cysoldierssortie_comp_character:AddMaxHp(addNum)
    if DEBUG_DYNAMIC_HP then
        log.Error("[MINIGAME] Unit ID "..tostring(self._unitID).. " 士兵突击 增加前最大血量:"..tostring(self._hp))
        log.Error("[MINIGAME] Unit ID "..tostring(self._unitID).. " 士兵突击 动态血量增加。增加值:"..tostring(addNum))
    end
    self._dynamicAddHp = addNum
    self._curHp = self._curHp + addNum
    self._hp = self._hp + addNum
    self._lastHpMax = self._hp
    if DEBUG_DYNAMIC_HP then
        log.Error("[MINIGAME] Unit ID "..tostring(self._unitID).." 士兵突击 增加后最大血量:" .. tostring(self._hp))
    end
end

function cysoldierssortie_comp_character:ReduceDynamicHp()
    if not self._dynamicAddHp or self._dynamicAddHp<=0 then
        return
    end
    if not self._soldierDpsCoe or self._soldierDpsCoe <= 0 then
        return
    end
    local levelMgr  = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    local player = curLevel.playerLua
    local singleSoldierDps = player:GetSingleSoliderDps()
    local reduceHp = (singleSoldierDps * self._soldierDpsCoe)
    self._dynamicAddHp = self._dynamicAddHp - reduceHp
    if DEBUG_DYNAMIC_HP then
        log.Error("[MINIGAME] 士兵突击 动态血量减少，减少值:"..tostring(reduceHp).." 剩余动态血量:".. tostring(self._dynamicAddHp))
    end
    
    self._curHp = self._curHp - reduceHp
    self._hp = self._hp - reduceHp
    self._curHp = math.max(0,self._curHp)
    self._hp = math.max(0,self._hp)
    self._lastHpMax = self._hp
    if self._curHp<=0 then
        self:Dead()
    end
end

function cysoldierssortie_comp_character:BossHpDynamicAddOnce()
    if not (self._unit_type == cysoldierssortie_unit_type.BossEnemy or self._unit_type == cysoldierssortie_unit_type.NormalEnemy) then
        return
    end
    if self._rewardCoin and self._rewardCoin > 0 then
        return
    end
    if not self._soldierDpsCoe or self._soldierDpsCoe <= 0 then
        return
    end
    
    if not self._hpDynamic then
        local levelMgr  = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local curLevel = levelMgr.curLevel
        local player = curLevel.playerLua
        if player then
            local allSoldierDps =  player:GetAllSoldierDps() 
            allSoldierDps = allSoldierDps * self._soldierDpsCoe 
            self:AddMaxHp(allSoldierDps)
            player:AddDynamicHpActor(self)
        end
        self._hpDynamic = true
    end
end

function cysoldierssortie_comp_character:SkillUltraCamShakeFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not self._feedBackUse or self._feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.SKILL_ULTRA then
            self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.SKILL_ULTRA]
            self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.SKILL_ULTRA
            feedComp:CreateData(self._feedBackData,self._feedBackUse)
        end
        feedComp:StartFeedBack(self._feedBackUse)
    end
end

function cysoldierssortie_comp_character:RewardEnemyDeadFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not self._feedBackUse or self._feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_ENEMY_DEAD then
            self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_ENEMY_DEAD]
            self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_ENEMY_DEAD
            feedComp:CreateData(self._feedBackData,self._feedBackUse)
        end
        feedComp:StartFeedBack(self._feedBackUse)
    end
end

function cysoldierssortie_comp_character:RewardBossDeadFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not self._feedBackUse or self._feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_BOSS_DEAD then
            self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_BOSS_DEAD]
            self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.REWARD_BOSS_DEAD
            feedComp:CreateData(self._feedBackData,self._feedBackUse)
        end
        feedComp:StartFeedBack(self._feedBackUse)
    end
end

function cysoldierssortie_comp_character:SoldierBeHitFeedBack()
    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not self._feedBackUse or self._feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.SOLDIER_BEHIT then
            self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.SOLDIER_BEHIT]
            self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.SOLDIER_BEHIT
            feedComp:CreateData(self._feedBackData,self._feedBackUse)
        end
        feedComp:StartFeedBack(self._feedBackUse)
    end
end

function cysoldierssortie_comp_character:CreateSkillCountDownUI()
    local hpComp = self.comps[cysoldierssortie_comp_name.HP]
    if hpComp then 
        local hpRoot = hpComp._hpSliderParent
        if not bc_IsNotNull(hpRoot) then
            return false
        end
        local skillCountDownRoot = hpRoot:Find("Avatar")
        if skillCountDownRoot then
            skillCountDownRoot:SetActive(true)
            return skillCountDownRoot
        end
    end
    return false
end

function cysoldierssortie_comp_character:CreateSkillUltraShow()
    local attackComp = self.comps[cysoldierssortie_comp_name.Attack]
    if attackComp then
        attackComp:CreateSkillUltraShow()
    end
end

--受惊行为
function cysoldierssortie_comp_character:SetScared()
    local moveComp = self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:SetScared()
    end
end

function cysoldierssortie_comp_character:ChangeMoveSpeedBuff(speed, time)
    local moveComp = self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:ChangeMoveSpeedBuff(speed, time)
    end
end

function cysoldierssortie_comp_character:DamageReduceByDisBuff(attack)
    local damageReduceByDisBuff,buffItem  = minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.DamageReduceByDis,self)
    if damageReduceByDisBuff and self._disToPlayerZ then
        if buffItem  then
            local damageReducePercent = buffItem.cfg.strParam1 / 10000
            local damageReduceMaxPercent = buffItem.cfg.strParam1 / 10000
            local  reduceDamage =  self._disToPlayerZ * damageReducePercent
            reduceDamage = math.min(damageReduceMaxPercent,reduceDamage)
            attack = attack - ( attack * reduceDamage)
            return attack
        end
    end
    return attack
end

local DropCoinInterval = 0.6 --虚假掉金币间隔
local SplitDropInterval = 0  --分帧掉金币间隔
---@param damageSource table 传入伤害来源:character
function cysoldierssortie_comp_character:BeHit(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,damageType,isUltra,damageSource)
    if self._isDrone then
        return
    end
    if self._player and self._player.isInvincible then
        return
    end
    attack=minigame_buff_mgr.GetSkillBuffAttribute(minigame_buff_mgr.BuffType.ReduceHarm,self,attack)
    local newAttack=attack
    -- 插入卡牌buff计算
    if self.cardBuffMgr then
        -- 减伤buff放在动态难度计算之前
        newAttack = self:DamageReduceByDisBuff(newAttack)
        if self._player then
            --玩家挨打
            newAttack = self.cardBuffMgr:CalcDamageByEnemy(newAttack, self._heroFlag)
        else
            -- 怪物挨打。找到源头，英雄和小兵是不同的属性逻辑。小兵的被合批了，没有damageSource，所以这里不用考虑小兵的伤害来源
            -- 忽略神兽
            if damageSource and not damageSource._isDrone then
                local tmpCritFlag = nil
                newAttack, tmpCritFlag = self.cardBuffMgr:CalcDamageByPlayer(newAttack, damageSource._heroFlag)
                if tmpCritFlag and not isCritical then
                    isCritical = true
                end
                -- 卡牌buff的攻击回血判断
                if damageSource._player and damageSource._heroFlag then
                    local regen = self.cardBuffMgr:CalcHpRegen(true)
                    if regen > 0 then
                        self:AddHpByRegen(regen)
                    end
                end
            end
        end
    end
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    if  not self._player then
        newAttack =  levelMgr:GetAttackLimit(attack)
    else
        newAttack=  levelMgr:GetHpLimit(attack)
    end

    if self.cysoldierssortie_TroopClash or self.cysoldierssortie_KingShot then
        --有时候缺少飘字坐标
        if takeDamagePointX == nil or takeDamagePointY == nil or takeDamagePointZ == nil then
            takeDamagePointX, takeDamagePointY, takeDamagePointZ = GetTransformPositionXYZ(self.transform)
        end
        if self._player == nil then
            levelMgr:EnemyBeHit(self)
        end
    end

    if self.cysoldierssortie_KingShot and self._isBuilding then
        levelMgr:BuildingBeHit(self)
    end

    self:BossHpDynamicAddOnce()
    
    -- 距离减伤buff，存在卡牌buff系统时，会在动态难度之前计算
    if not self.cardBuffMgr then
        newAttack = self:DamageReduceByDisBuff(newAttack)
    end
    
    self._curHp = self._curHp - newAttack
    local minigame_mgr = require "minigame_mgr"
    minigame_mgr.AddHeroHitHurt(self,attack)

    if self._curHp <= 0 and  self._characterState ~= cysoldierssortie_character_state.Die then
        self._curHp = 0
        -- 死亡也要显示伤害数字
        if self.cysoldierssortie_TroopClash then
            local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
            if hp_comp then
                hp_comp:SpawnHpNumAniText(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,1,damageType,isUltra)
            end
        end
        self:Dead()
    else
        if not self._dropCoinTimer or bc_Time.time > self._dropCoinTimer then
            self._dropCoinTimer = bc_Time.time + DropCoinInterval
            self:DropCoin(true,SplitDropInterval)
        end
        minigame_buff_mgr.CheckCondition(self,minigame_buff_mgr.ConditionType.BeHit)
        local buff = minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.Scared,self)
        if buff then
            self:SetScared()    
        end
        -- 小兵大作战专用的逃跑
        if self.cysoldierssortie_TroopClash then
            minigame_buff_mgr.CheckCondition(self, minigame_buff_mgr.ConditionType.BeHit)
            local buffFlag, buffItem = minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.Escape, self)
            if buffFlag then
                buffItem.buff_new:Refresh()
            end
        end
        if self._autoDelayDeadTimer then
            KillTimer(self._autoDelayDeadTimer)
        end
        
        --受击减速
        local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
        if moveComp then
            moveComp:ChangeMoveSpeed(cysoldierssortie_common_unit_prop.DecelerationTime,cysoldierssortie_common_unit_prop.DecelerationPercent)
        end
        
        --反馈系统
        if not self._player then
            local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
            if feedComp then
                if not self._feedBackUse or self._feedBackUse~= cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT then
                    self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse[cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT]
                    self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT
                    feedComp:CreateData(self._feedBackData,self._feedBackUse)
                end
                feedComp:StartFeedBack(self._feedBackUse)
            end
        else
            self:SoldierBeHitFeedBack()
        end
    end
    
    if not self.comps[cysoldierssortie_comp_name.HP] then
        return
    end
    if not self._view_actor then
        return 
    end
    
    local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
    if hp_comp then
        hp_comp:SpawnHp()
        hp_comp:SpawnHpNumAniText(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,1,damageType,isUltra)
    end
end

function cysoldierssortie_comp_character:SpawnHp()
    if self._player then
        -- 小兵大作战，小兵单位挨打再显示血条
        -- 8 8 士兵突击也想要挨打再显示
        if self._unit_type ~= cysoldierssortie_unit_type.Hero then
            return
        end
        local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
        if hp_comp then
            hp_comp:SpawnHp()
        end
    end
end

local fakeHpAnimMaxNum = 2
local spawnInterval = 0.0333
function cysoldierssortie_comp_character:FakeSpawnHpAnim(sumDamage,attack)
    if not self._fakeSpawnTimer or bc_Time.time> self._fakeSpawnTimer then
        self._fakeSpawnTimer = spawnInterval + bc_Time.time
        local hpComp = self.comps[cysoldierssortie_comp_name.HP]
        if hpComp then
            local spawnCount = math.floor(sumDamage/attack)
            spawnCount = math.min(fakeHpAnimMaxNum,spawnCount)
            local x,y,z = GetTransformPositionXYZ(self._skillReleaseTrans)
            for i=1,spawnCount do
                hpComp:SpawnHpNumAniText(attack,false,x,y,z,2)
            end
        end
    end
end

function cysoldierssortie_comp_character:GetAnimator()
    if not self._character_entity then
        return nil
    end
    if not self._character_entity._animator then
        return nil
    end
    return self._character_entity._animator
end

function cysoldierssortie_comp_character:RefreshAttackInterval(run)
    if self.cysoldierssortie_TroopClash then
        return false
    end

    local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
    local releaseSkillTime,animSpeed,loopInterval,runLoopInterval = attackMgr:GetReleaseSkillTime(self._unitID,self._heroId)
    if (not loopInterval) and (not runLoopInterval) then
        return false
    end
    
    self._animator = self:GetAnimator()
    if not bc_IsNotNull(self._animator) then
        return false
    end
    --self._animator:SetAttackLoopInterval(true,loopInterval)
    self._animator:SetAttackLoopInterval(true,0,run)
    cysoldierssortie_DelayCallOnce(bc_Time.deltaTime,function()
        if not run then
            self._animator:SetAttackLoopInterval(true,loopInterval,run)
        else
            self._animator:SetAttackLoopInterval(true,runLoopInterval,run)
        end
      
    end)
    return true
end

function cysoldierssortie_comp_character:PlayAnim(state)
    if self._player then
        local curLevel = self._player.curLevel
        if curLevel then
            local runnerMode = curLevel:IsRunnerMode()
            local enterRunnerTowerDefence =curLevel:IsEnterAutoTowerDefence()
            if runnerMode and not enterRunnerTowerDefence then
                if  self._player and state == cysoldierssortie_hero_anim_set.Ability then
                    local actionState = self._player:GetActionState()
                    if actionState and actionState == cysoldierssortie_player_action_state.Move then
                        state = cysoldierssortie_hero_anim_set.Run_Skill01_Loop
                    end
                    state = cysoldierssortie_hero_anim_set.Run_Skill01_Loop
                end
            end
        end
    end

    if self._curAnimState == state then
        return
    end
    self._animator = self:GetAnimator()

    if state == cysoldierssortie_hero_anim_set.Ability   then
        local refresh = self:RefreshAttackInterval()
        if refresh then
            self._curAnimState = state
            return
        end
    end
    
    --ecs兼容attack interval 边跑边打
    if self._character_entity._entity then
        if state == cysoldierssortie_hero_anim_set.Run_Skill01_Loop then
            local refresh = self:RefreshAttackInterval(true)
            if refresh then
                self._curAnimState = state
                return
            end
        end
    end
    
    if bc_IsNotNull(self._animator) then
        self._animator:ResetTrigger(self._curAnimState or cysoldierssortie_hero_anim_set.Stand)
        self._animator:SetTrigger(state)
        self._curAnimState = state
    end
end

local showAnimCD = 5
function cysoldierssortie_comp_character:PlayShowAnimClip()
    if self._unit_type == cysoldierssortie_unit_type.BossEnemy then
        if minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.UnableToMove,self) then
            if not self._animShowCD or self._animShowCD <= bc_Time.time then
                self._animShowCD = bc_Time.time + showAnimCD
                self:PlayAnim(cysoldierssortie_hero_anim_set.Show) 
            end
        end
    end
end

function cysoldierssortie_comp_character:PlaySpawnAnimClip()
    if self._unit_type == cysoldierssortie_unit_type.NormalEnemy then
        if minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.UnableToMove,self) then
            self:PlayAnim(cysoldierssortie_hero_anim_set.Spawn)
        end
    end
end

function cysoldierssortie_comp_character:ResetAnim()
    --动画逻辑
    --[[self._animator = self:GetAnimator()
    if bc_IsNotNull(self._animator) then
        self._animator:ResetTrigger(cysoldierssortie_hero_anim_set.Ability)
        self._curAnimState = cysoldierssortie_hero_anim_set.Stand
    end]]--
end

function cysoldierssortie_comp_character:CreateNavAgent()
    local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:CreateNavAgent()
    end
end

function cysoldierssortie_comp_character:Patrol()
    if not self._patrol then
        return
    end
    local moveComp =  self.comps[cysoldierssortie_comp_name.Move]
    if moveComp then
        moveComp:Patrol()
    end
end

function cysoldierssortie_comp_character:InitAnimState()
    self:PlaySpawnAnimClip()
    local fsmState = self:GetFsmState()
    if fsmState == cysoldierssortie_character_state.Move then
        self:PlayAnim(cysoldierssortie_hero_anim_set.Run)
    elseif fsmState == cysoldierssortie_character_state.Attack then
        if self._character_entity._gpu_anim then
            local animator = self._character_entity._animator
            if animator then
                local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                local releaseSkillTime,animSpeed,loopInterval =  attackMgr:GetReleaseSkillTime(self._unitID,self._heroId)
                if animSpeed then
                    animator:SetAnimatorSpeed(animSpeed)
                end
                if loopInterval then
                    animator:SetAttackLoopInterval(true,loopInterval)
                end
            end
        end
        self:PlayAnim(cysoldierssortie_hero_anim_set.Ability)
    end
end

local DropCoinNumBatch = 10
local BossDropCoinNumBatch = 1 --Boss每几个金币掉一个表现
function cysoldierssortie_comp_character:Dead(anim,recyclePos,dontCheckBuff)
    if self._player and self.cardBuffMgr then
        -- 死亡后，取消注册增加最大生命值的卡牌系统事件
        self.eventMgr:UnRegisterEvt(self, cysoldierssortie_EventName.CardBuffAddMaxHpListener)
    end

    local gameOverFlag = false
    local level_mgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)

    if self.cysoldierssortie_TroopClash then
        if self._unit_type == cysoldierssortie_unit_type.BossEnemy then
            level_mgr.sceneMgr.uiMgr:RemoveBossGuide(self)
        end
    end

    recyclePos = recyclePos == nil and true or recyclePos

    if self._autoDelayDeadTimer then
        KillTimer(self._autoDelayDeadTimer)
    end
    self._characterState = cysoldierssortie_character_state.Die

    if self._rewardCoin and self._rewardCoin > 0 then
        if self._unit_type==cysoldierssortie_unit_type.BossEnemy then
            self:RewardBossDeadFeedBack()
        else
            self:RewardEnemyDeadFeedBack()
        end
    end

    local unit_type = self._unit_type
    local cysoldierssortie_config_character_comps
    if self.cysoldierssortie_TroopClash then
        cysoldierssortie_config_character_comps = require("troopclash_config_character_comps")
    elseif self.cysoldierssortie_KingShot then
        cysoldierssortie_config_character_comps = require("kingshot_config_character_comps")
    else
        cysoldierssortie_config_character_comps = require("cysoldierssortie_config_character_comps")
    end
    local comps =  cysoldierssortie_config_character_comps.UnitTypeCompsConfig[unit_type]
    if comps then
        for i=1,#comps do
            self:RemoveComponent(comps[i])
        end
    end

    if self._player then
        self._player:OnHeroDead(self)
        if self._unit_type == cysoldierssortie_unit_type.Soldier or self._unit_type == cysoldierssortie_unit_type.Soldier2 then
            self._player:ReduceDynamicHp()
        end
        if recyclePos then
            self._player:RecycleSoldierPos(self._localPos,self.transform.parent)
        end
        if self.cysoldierssortie_TroopClash then
            --玩家死亡也有血迹
            if level_mgr.sceneMgr.BattleFlagBind.value then
                self:PlayBombBlood()
            end
        end
    else
        if self.cardBuffMgr then
            self.cardBuffMgr:EnemyKilled(1)
        end
        local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
        if attackMgr then
            attackMgr:RemoveDamageBatch(self)
        end
        self:RemoveComponent(cysoldierssortie_comp_name.EarlyWarning)
        self:PlayBombBlood()
        self:CreateLinearKnockback()
        
        if level_mgr then
            local curLevel = level_mgr.curLevel
            if curLevel then
                curLevel.playerLua:RemoveRangeEnemy(self._character_entity.gameObject)
                gameOverFlag = curLevel:RaduceEnemyCount(1,self,dontCheckBuff)
            end
        end
        
        local actor_instance_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
        if actor_instance_mgr then
            actor_instance_mgr:DequeueActor(self)
        end

        self:DropCoin()
    end

    self._character_entity:Dead(anim,gameOverFlag)
    if self._player then
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterSoldier(self.transform)
        end, debug.traceback)
    else
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterEnemey(self.transform)
        end, debug.traceback)
    end
    self.isDelKey=true
    minigame_buff_mgr.DelAllCharacterBuff(self)
end

-- 无尽模式冲锋状态下的死亡。死亡后不回收了，等待这一波结束后直接销毁
function cysoldierssortie_comp_character:EndlessRushDie(oriPos)
    if self._player then
        return
    end
    local flyDir = nil
    if oriPos then
        flyDir = (self.transform.position - oriPos).normalized
    end
    self._character_entity:RemoveNavMeshAgent()
    self._character_entity:EndlessRushDie(flyDir)

    if self._autoDelayDeadTimer then
        KillTimer(self._autoDelayDeadTimer)
    end
    self._characterState = cysoldierssortie_character_state.Die

    local feedComp = self.comps[cysoldierssortie_comp_name.FeedBack]
    if feedComp then
        if not self._feedBackUse or self._feedBackUse ~= cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT then
            self._feedBackData.feedback_use = cysoldierssortie_config_feedback.FeedBackUse
            [cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT]
            self._feedBackUse = cysoldierssortie_config_feedback.FeedBackUseDefine.ENEMY_BEHIT
            feedComp:CreateData(self._feedBackData, self._feedBackUse)
        end
        feedComp:StartFeedBack(self._feedBackUse)
    end

    self:PlayBombBlood()
    -- self:CreateLinearKnockback()
    local level_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    if level_mgr then
        local curLevel = level_mgr.curLevel
        if curLevel then
            curLevel.playerLua:RemoveRangeEnemy(self._character_entity.gameObject)
        end
    end

    self.isDelKey = true
    minigame_buff_mgr.DelAllCharacterBuff(self)
end

local baseCircleRadius = 1.5 --基础圈大小
local IncreasingRadius = 2 --每增加一圈的半径增长
local spacing = 1.5 --每个单位间隔
function cysoldierssortie_comp_character:DropCoin(splitFrame,splitFrameInterval)
    if self._rewardCoin and self._rewardCoin > 0 then
        local dropNum
        if self._unit_type==cysoldierssortie_unit_type.BossEnemy and not splitFrame then
            dropNum =  math.ceil( self._rewardCoin / BossDropCoinNumBatch)
        else
            dropNum = math.ceil( self._rewardCoin / DropCoinNumBatch)
        end
        
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            if not splitFrame then
                for i =1,dropNum do
                    local function callback(rewardCoinEntity)
                        if rewardCoinEntity then
                            SetTransformPositionByTransform(rewardCoinEntity.transform,self.transform)
                            local localPosX,localPosZ = nil
                            if self._unit_type == cysoldierssortie_unit_type.BossEnemy then
                                localPosX,localPosZ = cysoldierssortie_lua_util:GetCirclePos(i,baseCircleRadius,IncreasingRadius,spacing)
                            end
                            rewardCoinEntity:SequencePlayer(self._boundSize,self._boundHeight,localPosX,localPosZ)
                        end
                    end
                    if isAsync then
                        poolMgr:AcquireObjAsync("cysoldierssortie_comp_reward_coin_entity",callback,poolMgr.transform)
                    else
                        local rewardCoinEntity =  poolMgr:AcquireObj("cysoldierssortie_comp_reward_coin_entity",poolMgr.transform)
                        callback(rewardCoinEntity)
                    end
                end
            else
                for i =1,dropNum do
                    splitFrameInterval = splitFrameInterval or 0.05
                    cysoldierssortie_DelayCallOnce(splitFrameInterval*i,function()
                        local function callback(rewardCoinEntity)
                            if rewardCoinEntity then
                                SetTransformPositionByTransform(rewardCoinEntity.transform,self.transform)
                                rewardCoinEntity:SequencePlayer(self._boundSize,self._boundHeight)
                            end
                        end
                        if isAsync then
                            poolMgr:AcquireObjAsync("cysoldierssortie_comp_reward_coin_entity",callback,poolMgr.transform)
                        else
                            local rewardCoinEntity =  poolMgr:AcquireObj("cysoldierssortie_comp_reward_coin_entity",poolMgr.transform)
                            callback(rewardCoinEntity)
                        end
                    end)
                end
            end
        end
    end
end

--无限远索敌
function cysoldierssortie_comp_character:UnLimitRangeLookAtTarget()
    if self._player then
        local res = xpcall(function()
            LookAtTargetSystemInstance:UnregisterSoldier(self.transform)
            LookAtTargetSystemInstance:RegisterSoldier(self.transform,1000)
        end, debug.traceback)
    end
end

function cysoldierssortie_comp_character:GetTargetGo()
    local res = false
    if self._player then
        res = xpcall(function()
            self._targetGo = LookAtTargetSystemInstance:GetSoldierTarget(self.transform)
        end,debug.traceback)
    else
        res = xpcall(function()
            self._targetGo = LookAtTargetSystemInstance:GetEnemyTarget(self.transform)
        end,debug.traceback)
    end
    return self._targetGo
end

local EffectParamCache = {}
local maxEffectSize = 5
function cysoldierssortie_comp_character:PlayBombBlood()
    local effect_path = cysoldierssortie_CharacterOtherEffect.EnemyBombBloodEffect
    EffectParamCache.auto_release = true
    EffectParamCache.delay_release = 2.5
    EffectParamCache.effect_path = effect_path
    EffectParamCache.scale = math.min(maxEffectSize, self._boundSize * 1.2)
    EffectParamCache.callBack = function(go)
        if bc_IsNotNull(self.transform) then
            SetTransformPositionByTransform(go.transform,self.transform)
        end
    end
    EffectParamCache.maxWeightLimit = true
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    effect_mgr:CreateEffect(EffectParamCache)
    
    effect_path = cysoldierssortie_CharacterOtherEffect.EnemyMonsterDieEffect
    EffectParamCache.auto_release = true
    EffectParamCache.delay_release = 1
    EffectParamCache.effect_path = effect_path
    EffectParamCache.scale = math.min(maxEffectSize, self._boundSize * 2)
    EffectParamCache.callBack = function(go)
        if bc_IsNotNull(self.transform) then
            SetTransformPositionByTransform(go.transform,self._weaponRoot)
        end
    end
    EffectParamCache.maxWeightLimit = true
    effect_mgr:CreateEffect(EffectParamCache)
end

function cysoldierssortie_comp_character:SelfHarmBuff()
    --自残buff
    local selfHarmBuff,buffItem = minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.SelfHarm,self)
    if selfHarmBuff then
        local leftTimerPercent =  minigame_mgr.GetRewardLevelModeTimerSlider()
        if buffItem then
            local startSelfHarmPercent =  buffItem.cfg.strParam1 / 100
            local perSecondLooseHpPercent = buffItem.cfg.strParam2 / 100
            if leftTimerPercent <= startSelfHarmPercent then
                if not self._selfHarmTimer or bc_Time.time > self._selfHarmTimer then
                    if not self._selfHarmTimer then
                        self._startSelfHarmHp = self._curHp
                    end
                    self._selfHarmTimer = bc_Time.time + 1
                    local selfHarmDamage = self._startSelfHarmHp * perSecondLooseHpPercent
                    self:BeHit(selfHarmDamage)
                end
            end
        end
    end
end

function cysoldierssortie_comp_character:UpdateHp()
    if not self.comps[cysoldierssortie_comp_name.HP] then
        return
    end
    if self._curHp<=0 then
        return
    end
    
    self:SelfHarmBuff()
    
    if not self._player and self._curHp >= self._hp then
        return
    end

    if not self._character_entity then
        return
    end
    
    self._hpPoint = self._hpPoint or self._character_entity._hpPoint
    local hp_comp = self.comps[cysoldierssortie_comp_name.HP]
    if hp_comp then
        if GCPerf then
            local pX, pY, pZ = GetTransformPositionXYZ(self._hpPoint.transform)
            hp_comp:UpdateXYZ(pX, pY, pZ,self._curHp,self._hp,self._curHp)
        else
            hp_comp:Update(self._hpPoint.transform.position,self._curHp,self._hp,self._curHp)
        end
    end
end

function cysoldierssortie_comp_character:UpdateAI()
    if self.cysoldierssortie_TroopClash and self.ignoreUpdateAI then
        return
    end

    if not self.comps[cysoldierssortie_comp_name.AI] then
        return
    end

    local ai_comp = self.comps[cysoldierssortie_comp_name.AI]
    if ai_comp then
        ai_comp:Update()
    end
end

function cysoldierssortie_comp_character:Fire()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    attack_comp:Fire()
end

function cysoldierssortie_comp_character:GetAttackComp()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return nil
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    return attack_comp
end

function cysoldierssortie_comp_character:StopFire()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    attack_comp:StopFire()
end

function cysoldierssortie_comp_character:Move(position)
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:Move(position)
end

function cysoldierssortie_comp_character:MoveXYZ(px, py, pz)
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:ExeMove(px, py, pz)
end

function cysoldierssortie_comp_character:UpdateEarlyWaning()
    if not self.comps[cysoldierssortie_comp_name.EarlyWarning] then
        return
    end
    local early_waning = self.comps[cysoldierssortie_comp_name.EarlyWarning]
    early_waning:UpdateWaningView()
end

function cysoldierssortie_comp_character:IsForceUpdate()
    if self.comps[cysoldierssortie_comp_name.EarlyWarning] then
        return true
    end
    return false
end

function cysoldierssortie_comp_character:StopMove()
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:StopMove()
end

function cysoldierssortie_comp_character:StartMove()
    if not self.comps[cysoldierssortie_comp_name.Move] then
        return
    end
    local move_comp = self.comps[cysoldierssortie_comp_name.Move]
    move_comp:StartMove()
end

function cysoldierssortie_comp_character:AttackCountdown()
    if not self.comps[cysoldierssortie_comp_name.Attack] then
        return 0
    end
    local attack_comp = self.comps[cysoldierssortie_comp_name.Attack]
    return attack_comp:Countdown()
end

--添加一个组件
function cysoldierssortie_comp_character:AddComponent(compName,data)
    if self.comps[compName] then
        return
    end
    local comp =  require(cysoldierssortie_comp_register[compName])
    local comp_instance = comp.New(data)
    self.comps[compName] = comp_instance
    return self.comps[compName]
end

function cysoldierssortie_comp_character:RemoveComponent(compName)
    if self.comps[compName] then
        local comp_instance =  self.comps[compName]
        comp_instance.Delete(comp_instance)
        self.comps[compName] = nil
    end
end

function cysoldierssortie_comp_character:ResetCooldown()
    if not self.comps[cysoldierssortie_comp_name.AI] then
        return 0
    end
    local fsm_comp = self.comps[cysoldierssortie_comp_name.AI]
    fsm_comp:ChangeState(cysoldierssortie_character_state.Idle)
end

-------------------------------------------------- 用于小兵大作战，攻击动画和攻击状态同步
function cysoldierssortie_comp_character:WeaponTriggerFire(weaponEntity)
    local duration = weaponEntity._weaponData._skillLoop + weaponEntity._weaponData._attent
    if self._player then
        self._player:CharacterFire(self, duration)
    else
        self.AtkingFlag = true
        --获取Skill配置表中的，动画时长
        self.AtkClipLength = duration
        self.AtkTimer = bc_Time.time
    end
end

---检测攻击动画是否结束，用于判断是否可以切换状态
function cysoldierssortie_comp_character:IsAtkingOver()
    if self.AtkingFlag then
        local tmpMod = math.mod(bc_Time.time - self.AtkTimer, self.AtkClipLength)
        self.AtkingFlag = tmpMod < self.AtkClipLength - endAtkTime
    end
    return not self.AtkingFlag
end
--------------------------------------------------

return cysoldierssortie_comp_character