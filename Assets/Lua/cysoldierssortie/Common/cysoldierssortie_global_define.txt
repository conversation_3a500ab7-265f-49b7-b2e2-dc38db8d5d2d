require("extern")
require("cysoldierssortie_perfect_base")
require("cysoldierssortie_common")
require("bc_class")
require("cysoldierssortie_emmy_debug")
        :emmyDebug(cysoldierssortie_Common.riderPort, cysoldierssortie_Common.riderPath)

require("cysoldierssortie_luadataConverter")
require("bc_base_global")

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
if __UNITY_2022_3_OR_NEWER and NeeGame.GetAsyncPoolVersion then
    if not PlayerPrefs.HasKey("sw_instantiate_async") then
        PlayerPrefs.SetInt("sw_instantiate_async", 1) -- 如果没有设置，则默认开启异步
    end
    local val = require("val")
    isAsync = val.IsTrue("sw_instantiate_async", 1)
    print("ld: check async state", isAsync)
else
    PlayerPrefs.SetInt("sw_instantiate_async", 0) -- 如果没有设置，则默认开启异步
end
local ApiHelper = CS.XLuaUtil.LuaApiHelper

local math = math

local EntityManager = require "entity_manager"
cysoldierssortie_urp_ecs = EntityManager.CysoldierssortieEcs
cysoldierssortie_ecs_batch_loader = true

cysoldierssortie_mgrTable = {}
cysoldierssortie_mgrTableCache = {}

cysoldierssortie_luaMonoExt = CS.CasualGame.lib_ChuagnYi.LuaMonoExt

GCPerf = true

---@type boolean 是否为小兵大作战模式
cysoldierssortie_TroopClash = false

---@type boolean 是否为kingshot模式
cysoldierssortie_KingShot = false

---@type boolean 是否为kingshot 剧情模式
cysoldierssortie_KingShot_HOME = false

---@type boolean 是否为首充活动——普通模式
cysoldierssortie_firstRecharge_Normal = false
---@type boolean 是否为首充活动——无尽模式
cysoldierssortie_firstRecharge_Endless = false
---@type string 首充活动的阵容保存标签
cysoldierssortie_firstRecharge_SlotSaveTag = "cysoldierssortie_Slot_firstRecharge"

--- 首充活动试玩英雄ID
cysoldierssortie_CampaignFR_TrialHeroID = 104

---@enum cysoldierssortie_cardBuffType 卡牌类型
cysoldierssortie_cardBuffType = {
    Common = 0, --普通卡牌
    Hero = 1,   --英雄特定卡牌，绑定到英雄模型id上
}

---@enum cysoldierssortie_cardEffectType 卡牌中的属性类型
cysoldierssortie_cardEffectType = {
    AtkNum = 1,                      -- 攻击力固定值
    AtkRate = 2,                     -- 攻击力百分比加成
    DefNum = 3,                      -- 防御力固定值
    DefRate = 4,                     -- 防御力百分比加成
    HpNum = 5,                       -- 生命固定值
    HpRate = 6,                      -- 生命百分比加成
    AtkMulti = 7,                    -- 增伤百分比加成
    DefMulti = 8,                    -- 减伤百分比加成
    Crit = 9,                        -- 暴击率
    CritDmg = 10,                    -- 暴击伤害加成
    CDMulti = 11,                    -- 冷却加成
    ExpRange = 12,                   -- 经验拾取范围加成
    ExpMulti = 13,                   -- 经验加成
    AddHpByAtk = 14,                 -- 命中回复生命值
    MoveSpeed = 15,                  -- 移动速度加成
    AtkNum_Soldier = 101,            -- 士兵攻击力固定值
    HpNum_Soldier = 102,             -- 士兵生命固定值
    AtkNumByKill = 201,              -- 击杀增加攻击力
    DefNumByKill = 202,              -- 击杀增加防御力
    HpNumByKill = 203,               -- 击杀增加生命值
    CritByKill = 204,                -- 击杀增加暴击率
    CritDmgByKill = 205,             -- 击杀增加暴击伤害
    CDMultiByKill = 206,             -- 击杀减少冷却
    ReduceDefNumAndAddAtkRate = 207, -- 减少防御并增加攻击
    ReduceAtkNumAndAddDefRate = 208, -- 减少攻击并增加防御
    ReduceCDMultiAndAddCrit = 209,   -- 减少冷却并增加暴击率
    ReduceHpNumAndAddCritDmg = 210,  -- 减少生命并增加暴击伤害
    ReduceCritAndAddCDMulti = 211,   -- 减少暴击率并增加冷却
    ReduceCritDmgAndAddHpNum = 212,  -- 减少暴击伤害并增加生命
}

---@enum cysoldierssortie_ExtensionType 玩法扩展
cysoldierssortie_ExtensionType = {
    ThreeChoices = 1, --三选一
}

-- 与entertable交互用
cysoldierssortie_MsgCode = {
    STAGE_SUCCESS = 1, --关卡成功
    STAGE_FAIL = 2, --关卡失败
    STAGE_RESET = 3, --重置关卡
    STAGE_PAUSE = 4, --暂停
    STAGE_CONTINUE = 5, --继续
    STAGE_EXIT = 6, --退出关卡
}

--一些全局事件，使用请参考cysoldierssortie_event
cysoldierssortie_EventName = {
    --
    InputMgrOnBeginDrag = "InputMgrOnBeginDrag",
    InputMgrOnEndDrag = "InputMgrOnEndDrag",
    InputMgrOnPointerDown = "InputMgrOnPointerDown",
    InputMgrOnPointerUp = "InputMgrOnPointerUp",
    InputMgrOnPointerClick = "InputMgrOnPointerClick",
    InputMgrOnDrag = "InputMgrOnDrag",
    --
    JoystickDragOutput = "JoystickDragOutput",
    --

    SwitchGuns = "SwitchGuns",--切换枪支
    
    OnGameStart = "OnGameStart",
    OnGameReload = "OnGameReload",
    OnGameWin = "OnGameWin",
    OnGameLose = "OnGameLose",
    -- more

    CSUpdate = "CSUpdate",
    -- 卡牌buff系统，增加最大生命值
    CardBuffAddMaxHpListener = "CardBuffAddMaxHpListener",
    -- 卡牌buff系统，移速变更，目前只用在小兵大作战
    CardBuffMoveSpeedListener = "CardBuffMoveSpeedListener",
    -- 卡牌buff系统，冷却缩减变化
    CardBuffCDChangedListener = "CardCDChangedListener",
    -- 卡牌buff系统，经验拾取范围变化
    CardBuffExpCollectRangeListener = "CardBuffExpCollectRangeListener",
    -- 卡牌buff系统，级别变化或者经验值变化。ExpMax为-1时表示达到等级上限
    CardBuffLevelChangedListener = "CardBuffExpChangedListener",
}

--layer层级,改成自己想要的名字后，通过读取此table来获取
cysoldierssortie_LayerName = {
    Default = 0,
    L12 = 12,       
    Bullet = 13,
    Player = 14,
    Enemy = 15,--敌人 滚桶 雪球 --可被玩家攻击的
    Prop = 16,--不可被玩家攻击的 但是可以和玩家交互的 道具门 障碍物
    Boom = 17,--爆炸物，敌人和英雄都能伤害
}

cysoldierssortie_unit_type = {
    NormalEnemy = 1,
    BossEnemy = 2,
    Soldier = 3,
    Hero = 4,
    Soldier2 = 5,
    Drone = 6,
    Building = 7,
}

cysoldierssortie_lst_group_type = {
    SoldierLst = 1,
    EnemyLst = 2,
    HeroLst = 3,
}

cysoldierssortie_unit_layer = 
{
    [cysoldierssortie_unit_type.NormalEnemy] = cysoldierssortie_LayerName.Enemy,
    [cysoldierssortie_unit_type.BossEnemy] = cysoldierssortie_LayerName.Enemy,
    [cysoldierssortie_unit_type.Soldier] = cysoldierssortie_LayerName.Player,
    [cysoldierssortie_unit_type.Hero] = cysoldierssortie_LayerName.Player,
    [cysoldierssortie_unit_type.Drone] = cysoldierssortie_LayerName.Player,
    [cysoldierssortie_unit_type.Building] = cysoldierssortie_LayerName.Player,
}

cysoldierssortie_unit_target_layer_str = {
    [cysoldierssortie_unit_type.NormalEnemy] = "L14",
    [cysoldierssortie_unit_type.BossEnemy] = "L14",
    [cysoldierssortie_unit_type.Soldier] = "L15",
    [cysoldierssortie_unit_type.Hero] = "L15",
    [cysoldierssortie_unit_type.Drone] = "L15",
    [cysoldierssortie_unit_type.Building] = "L15",
}

cysoldierssortie_unit_target_layer_Int = {
    [cysoldierssortie_unit_type.NormalEnemy] = 14,
    [cysoldierssortie_unit_type.BossEnemy] = 14,
    [cysoldierssortie_unit_type.Soldier] = 15,
    [cysoldierssortie_unit_type.Hero] = 15,
    [cysoldierssortie_unit_type.Drone] = 15,
    [cysoldierssortie_unit_type.Building] = 15,
}


--单位ID
cysoldierssortie_attack_speed_set =
{   --士兵1
    [101] = {
        anim_speed = 1.2,
        release_skill_time = 0.15,
    },
    --士兵2
    [102] = {
        anim_speed = 1.4,
        release_skill_time = 0.1,
    },
    --士兵3
    [103] = {
        anim_speed = 1.7,
        release_skill_time = 0.08,
    },
    --士兵4
    [104] = {
        anim_speed = 2,
        release_skill_time = 0.05,
    },
    --士兵5
    [105] = {
        anim_speed = 12,
        release_skill_time = 0.02,
    },
}

--模型ID
cysoldierssortie_model_attack_speed_set = 
{
    --洋葱头
    [113] = {
        anim_speed = 1,
        release_skill_time = 0.5,
    },
    --菲尔瑞
    [111] = {
        anim_speed = 1.5,
        release_skill_time = 0.1,
    },
    --章鱼怪
    [212] = {
        anim_speed = 1,
        release_skill_time = 0.4,
    },
    --船长
    [219] = {
        anim_speed = 1,
        release_skill_time = 0.4,
    },
    --龙刹
    [101] = {
        anim_speed = 1,
        release_skill_time = 0.2,
        loopInterval = 1.65
    }
}

cysoldierssortie_model_attack_speed_set_ecs =
{
    --洋葱头
    [113] = {
        anim_speed = 1,
        release_skill_time = 0.5,
    },
    --菲尔瑞
    [111] = {
        anim_speed = 1.5,
        release_skill_time = 0.1,
    },
    --章鱼怪
    [212] = {
        anim_speed = 1,
        release_skill_time = 0.4,
    },
    --船长
    [219] = {
        anim_speed = 1,
        release_skill_time = 0.4,
    },
    --龙刹
    [101] = {
        anim_speed = 1,
        release_skill_time = 0.2,
        loopInterval = 0.65
    },
    --雷克萨斯
    [102] = {
        anim_speed = 1,
        release_skill_time = 0.2,
        runLoopInterval = 0.3
    },
    --莫妮卡
    [103] = 
    {
        anim_speed = 1,
        release_skill_time = 0.2,
        runLoopInterval = 0.3
    },
}

cysoldierssortie_scene_render_data = {
    --普通白天
    [1] = 
    {
        dtDirLight1 = 
        {
            rotation = {x=43.954,y=-43.846,z=-54.024},
            color = {r = 1, g = 1, b = 1, a = 1},
            intensity = 0.65,
            shadowStrength = 1,
        },
        dtDirLight2 =
        {
            rotation = {x=6.292,y=-427.001,z=-309.513},
            color = {r = 0.9292, g = 1, b = 0.9888, a = 1},
            intensity = 0.5,
            shadowStrength = 1,
        },
        dtAmbientMode = 3,
        dtAmbientColor = {r = 0.4705,g=0.4705,b=0.4705,a=1.0},
        dtUseFog = false,
        dtDefaultReflectionMode = 0,
        dtCameraType = 1,
        dtCameraBgColor = {r = 0.192, g = 0.302, b = 0.475, a = 0.000}
    },
    --普通夜晚
    [2] =
    {
        dtDirLight1 =
        {
            rotation = {x=40.434,y=-415.84,z=-121.295},
            color = {r = 0.4, g = 0.6549, b = 0.8196, a = 1},
            intensity = 1,
            shadowStrength = 1,
        },
        dtDirLight2 =
        {
            rotation = {x=6.292,y=-427.001,z=-309.513},
            color = {r = 0.9292, g = 1, b = 0.9888, a = 1},
            intensity = 0.6,
            shadowStrength = 1,
        },
        dtAmbientMode = 3,
        dtAmbientColor = {r = 0.1960,g=0.2158,b=0.3568,a=1.0},
        dtUseFog = true,
        dtDefaultReflectionMode = 0,
        dtCameraType = 1,
        dtCameraBgColor = {r = 0.192, g = 0.302, b = 0.475, a = 0.000}
    },
    [3] =
    {
        dtDirLight1 =
        {
            rotation = {x=43.954,y=-43.846,z=-54.024},
            color = {r = 1, g = 1, b = 1, a = 1},
            intensity = 0.9,
            shadowStrength = 1,
        },
        dtDirLight2 =
        {
            rotation = {x=6.292,y=-427.001,z=-309.513},
            color = {r = 0.9292, g = 1, b = 0.9888, a = 1},
            intensity = 0.5,
            shadowStrength = 1,
        },
        dtAmbientMode = 3,
        dtAmbientColor = {r = 0.4705,g=0.4705,b=0.4705,a=1.0},
        dtUseFog = false,
        dtDefaultReflectionMode = 0,
        dtCameraType = 1,
        dtCameraBgColor = {r = 0.192, g = 0.302, b = 0.475, a = 0.000}
    },
    --雾气比较大的夜晚
    [4] =
    {
        dtDirLight1 =
        {
            rotation = {x=40.434,y=-415.84,z=-121.295},
            color = {r = 0.4, g = 0.6549, b = 0.8196, a = 1},
            intensity = 1,
            shadowStrength = 1,
        },
        dtDirLight2 =
        {
            rotation = {x=6.292,y=-427.001,z=-309.513},
            color = {r = 0.9292, g = 1, b = 0.9888, a = 1},
            intensity = 0.6,
            shadowStrength = 1,
        },
        dtAmbientMode = 3,
        dtAmbientColor = {r = 0.1960,g=0.2158,b=0.3568,a=1.0},
        
        dtUseFog = true,
        dtFogIntensity = 0.889,
        dtFogStartColor = {r = 0.3525, g =0.4776, b = 0.5377, a = 0.7450},
        dtFogEndColor = {r = 0.3891, g = 0.4486, b = 0.5188, a = 1},
        dtHeightFogIntensity = 1,
        dtHeightFogStart = 0.6,
        dtHeightFogEnd = 100,
        dtHeightFogFallOff = 68,
        dtDistanceFogIntensity = 0.8,
        dtDistanceFogStart = 22.16,
        dtDistanceFogEnd = 117,
        dtDistanceFogFallOff = 3,
        
        
        dtDefaultReflectionMode = 0,
        dtCameraType = 1,
        dtCameraBgColor = {r = 0.192, g = 0.302, b = 0.475, a = 0.000}
    },
    
}

cysoldierssortie_damage_type = 
{
    Normal = 0,
    Physic = 1,
    Magic = 2,
    -- 加血，目前只用在首充无尽模式的恢复
    AddHp = 3,
}

cysoldierssortie_scene_data = {
    [1] = {
        path = "cysoldierssortie/prefab/scene_1.prefab",
        cameraData = 
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_1_nav_mesh_data/scene_1_nav_mesh_data/scene_1_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [2] = {
        path = "cysoldierssortie/prefab/scene_1_1.prefab",
        cameraData =
        { 
            xOffset = 0,    --相机水平偏移
            height = 20.5, --相机高度
            distance = -16,   --相机距离
            rotate_x = 39.4,
            fov = 50,
            follow = true,
            follow_distance_threshold = 1
        },
        mapWidth = 17,
        navMeshDataPath = "cysoldierssortie/data/scene_1_1_nav_mesh_data/scene_1_1_nav_mesh_data/scene_1_1_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [3] = {
        path = "cysoldierssortie/prefab/scene_1_3.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 12,
        navMeshDataPath = "cysoldierssortie/data/scene_1_3_nav_mesh_data/scene_1_3_nav_mesh_data/scene_1_3_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,

    },
    [4] = {
        path = "cysoldierssortie/prefab/scene_1_4.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_1_4_nav_mesh_data/scene_1_4_nav_mesh_data/scene_1_4_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [5] = {
        path = "cysoldierssortie/prefab/scene_1_5.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_1_5_nav_mesh_data/scene_1_5_nav_mesh_data/scene_1_5_nav_mesh_data.asset",
        cull_offset = 45,
        renderIndex = 1,
        maxViewEnemyNum = 60,
    },
    [6] = {
        path = "cysoldierssortie/prefab/scene_1_3_02.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 12,
        navMeshDataPath = "cysoldierssortie/data/scene_1_3_nav_mesh_data/scene_1_3_nav_mesh_data/scene_1_3_nav_mesh_data.asset",
        renderIndex = 2,
        maxViewEnemyNum = 100,
        fogMask =  
        {
          fogMaskEnable = false,
          fogMaskCenter = {x = 0,y = 65},
          fogMaskSize = {x = 100, y = 177},
          fogMaskTexPath = "cysoldierssortie/shader/enginefog/textures/test.png"
        },
    },
    [7] = {
        --path = "cysoldierssortie/prefab/scene_1_3_02.prefab",
        mapModule = 
        { 
          moduleWidth = 24.2,            --单个模组宽度
          loopModule = 
          {
              [1] = "cysoldierssortie/prefab/scene_2_1.prefab",
              [2] = "cysoldierssortie/prefab/scene_2_2.prefab",
              [3] = "cysoldierssortie/prefab/scene_2_3.prefab",
          },            -- 循环模组
          --最后一个地块模组
          finalModule = "cysoldierssortie/prefab/scene_3.prefab",
          finalModuleWidth = 29
        },
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        --冲成功模块
        sprintModule =
        {
            xStart = 0 --冲刺起始位置x点
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [8] = {
        --path = "cysoldierssortie/prefab/scene_1_3_02.prefab",
        mapModule =
        {
            moduleWidth = 24.2,            --单个模组宽度
            loopModule =
            {
                [1] = "cysoldierssortie/prefab/scene_2_1.prefab",
                [2] = "cysoldierssortie/prefab/scene_2_2.prefab",
                [3] = "cysoldierssortie/prefab/scene_2_3.prefab",
            },            -- 循环模组
            --最后一个地块模组
            finalModule = "cysoldierssortie/prefab/scene_3.prefab",
            finalModuleWidth = 29
        },
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 1,
        DontAllowControl = true, --禁止控制
        maxViewEnemyNum = 100,
    },
    [9] = {
        --path = "cysoldierssortie/prefab/scene_1_3_02.prefab",
        mapModule =
        {
            moduleWidth = 24.2,            --单个模组宽度
            loopModule =
            {
                [1] = "cysoldierssortie/prefab/scene_2_1.prefab",
                [2] = "cysoldierssortie/prefab/scene_2_2.prefab",
                [3] = "cysoldierssortie/prefab/scene_2_3.prefab",
            },            -- 循环模组
            --最后一个地块模组
            finalModule = "cysoldierssortie/prefab/scene_3.prefab",
            finalModuleWidth = 29,
            freeMoveXExtend = 10, --自由移动最大水平宽度的一半
            freeMoveZExtend = 25, --自由移动最大纵向宽度的一半
        },
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        --自由移动视角
        freedomMoveCameraData =
        {
            height = 28,
            rotate_x = 46.416,
            fov = 46,
            distance = -22.7,
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 1,
        DontAllowControl = false, --禁止控制
        maxViewEnemyNum = 100,
    },
    [11] = {
        path = "cysoldierssortie/prefab/scene_1.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_11_nav_mesh_data/scene_11_nav_mesh_data/scene_11_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [12] = {
        path = "cysoldierssortie/prefab/scene_4.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = true, --相机是否能跟随
            follow_distance_threshold = 0.05 --超过多少m开启跟随
        },
        mapWidth = 16,
        mapLeftWidth = 9,
        mapRightWidth = 7,
        camMoveLeftPosXLimit = -3,
        camMoveRightPosXLimit = 1,
        navMeshDataPath = "cysoldierssortie/data/scene_4/scene_4/scene_4_nav_mesh_data.asset",
        renderIndex = 1,
        maxViewEnemyNum = 100,
    },
    [13] = {
        path = "cysoldierssortie/prefab/scene_4_1.prefab",
        cameraData =
        {
            xOffset = -1.5,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = true, --相机是否能跟随
            follow_distance_threshold = 0.05 --超过多少m开启跟随
        },
        mapWidth = 12,
        mapLeftWidth = 5.9,
        mapRightWidth = 4.3,
        camMoveLeftPosXLimit = -3,
        camMoveRightPosXLimit = 1,
        navMeshDataPath = "cysoldierssortie/data/scene_4_1/scene_4_1/scene_4_1_nav_mesh_data.asset",
        renderIndex = 3,
        maxViewEnemyNum = 100,
        playerInitPos = 
        {
            x =  -1.5,
            y = 0,
            z = 0,
        }
    },
    [14] = {
        path = "cysoldierssortie/prefab/scene_4_3.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_4_3/scene_4_3/scene_4_3_nav_mesh_data.asset",
        renderIndex = 3,
        maxViewEnemyNum = 100,
    },
    [15] = {
        mapModule =
        {
            moduleWidth = 20.6,            --单个模组宽度
            loopModule =
            {
                [1] = "cysoldierssortie/prefab/scene_4_4.prefab",
            },            -- 循环模组
            --最后一个地块模组
            finalModule = "cysoldierssortie/prefab/scene_4_4.prefab",
            finalModuleWidth = 20.6,
            bgModule = 
            {
                path = "cysoldierssortie/prefab/minigamebg1.prefab",
                xRotation = 77.005,
                yRotation = 180,
                zRotation = 0,
                xPos = 0.6772146,
                yPos = -38.45105,
                zPos = 91.54175
            },
        },
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        --冲成功模块
        sprintModule = 
        {
            xStart = 0 --冲刺起始位置x点
        },
        mapWidth = 9,
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 3,
        maxViewEnemyNum = 100,
    },
    [17] = {
        path = "cysoldierssortie/prefab/scene_1_7.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_1_nav_mesh_data/scene_1_nav_mesh_data/scene_1_nav_mesh_data.asset",
        renderIndex = 3,
        maxViewEnemyNum = 100,
    },
    [18] = {
        path = "cysoldierssortie/prefab/scene_5.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 4,
        maxViewEnemyNum = 100,
        fogMask =
        {
            fogMaskEnable = true,
            fogMaskCenter = {x = 0,y = 65},
            fogMaskSize = {x = 100, y = 177},
            fogMaskTexPath = "cysoldierssortie/art/scene/texture/fogmask_scene_5.png"
        },
        DontAllowControl = true,
        enterAutoTowerDefenceDis = 90,--进入固定塔防的距离
    },
    [19] = {
        path = "cysoldierssortie/prefab/scene_6.prefab",
        cameraData =
        {
            xOffset = 0,    --相机水平偏移
            height = 14.28, --相机高度
            distance = -12,   --相机距离
            rotate_x = 41.005,
            fov = 61,
            follow = false, --相机是否能跟随
            follow_distance_threshold = 3 --超过多少m开启跟随
        },
        mapWidth = 9, -- 地图宽度限制,可移动范围
        navMeshDataPath = "cysoldierssortie/data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data/scene_common_runner_nav_mesh_data.asset",
        renderIndex = 4,
        maxViewEnemyNum = 100,
        fogMask =
        {
            fogMaskEnable = true,
            fogMaskCenter = {x = 0,y = 65 - 47.42},
            fogMaskSize = {x = 100, y = 177},
            fogMaskTexPath = "cysoldierssortie/art/scene/texture/fogmask_scene_5.png"
        },
        -- DontAllowControl = true,
        -- enterAutoTowerDefenceDis = 90,--进入固定塔防的距离
    },
}

-- 关卡模式
cysoldierssortie_LevelMode = {
    --士兵塔防模式
    SoldierTowerDefenceMode = 1,
    --士兵跑酷
    SoldierRunnerMode = 2,
    --英雄塔防模式
    HeroTowerDefenceMode = 3,
    --英雄跑酷
    HeroRunnerMode = 4,
    --小兵大作战
    TroopClash = 5,
    --KingShot
    KingShot = 6,
    --KingShot 剧情模式
    KingShot_HOME = 9,
}

cysoldierssortie_skill_config = 
{
    --skillID
    [16] = 
    {
        --半径 
        radius = 1.5,
        --每个特效包围盒大小  *100  比如  300/100 = 3
        damage_size = 300,
        --一个圆上有多少个特效
        effect_count = 4,
        --持续时间
        duration = 1,
        --自转速度
        rotationSpeed = 450,
    }
}

cysoldierssortie_soldier_pos_config = 
{
    [cysoldierssortie_LevelMode.HeroTowerDefenceMode] = 
    {
        baseRadius = 3,        --第一个圆的半径
        increasingRadius = 1,   --后面每个圆的半径增量
        spacing =    --每个圈的士兵士兵的间隔 
        {
            [1] = 0.6,
            [2] = 0.6,
            [3] = 0.6,
            [4] = 0.6,
            [5] = 0.6,
            [6] = 0.6,
        },
        startAngle = math.pi,    --开始生成士兵的角度,顺时针
        maxGenerateGroup = 2
    },
    [cysoldierssortie_LevelMode.HeroRunnerMode] =
    {
        baseRadius = 3,        --第一个圆的半径
        increasingRadius = 1,   --后面每个圆的半径增量
        spacing =    --每个圈的士兵士兵的间隔 
        {
            [1] = 0.6,
            [2] = 0.6,
            [3] = 0.6,
            [4] = 0.6,
            [5] = 0.6,
            [6] = 0.6,
        },
        startAngle = math.pi,    --开始生成士兵的角度,顺时针
        maxGenerateGroup = 2
    },
    [cysoldierssortie_LevelMode.SoldierTowerDefenceMode] =
    {
        baseRadius = 1.5,        --第一个圆的半径
        increasingRadius = 1.2,   --后面每个圆的半径增量
        spacing =    --每个圈的士兵士兵的间隔 
        {
            [1] = 1,
            [2] = 0.6,
            [3] = 0.6,
            [4] = 0.6,
            [5] = 0.6,
            [6] = 0.6,
        },
        startAngle = math.pi,    --开始生成士兵的角度,顺时针
        maxGenerateGroup = 3
    },
    [cysoldierssortie_LevelMode.SoldierRunnerMode] =
    {
        baseRadius = 1.5,        --第一个圆的半径
        increasingRadius = 1.2,   --后面每个圆的半径增量
        spacing =    --每个圈的士兵士兵的间隔 
        {
            [1] =1,
            [2] = 0.8,
            [3] = 0.6,
            [4] = 0.6,
            [5] = 0.6,
            [6] = 0.6,
        },
        startAngle = math.pi,    --开始生成士兵的角度,顺时针
        maxGenerateGroup = 3
    },
}

--tags,改成自己想要的名字后，通过读取此table来获取
cysoldierssortie_TagName = {
    missileBullet = "tag1",
    invicibleTag = "tag2",
    RocketBullet = "tag3",
    PropEnemy="tag4",--滚桶 气球 雪球 
    propsDoor_blue="tag5",--蓝门
    propsDoor_red="tag6",--红们
    Obstacle = "tag7",--障碍物 导致玩家死亡的障碍物
    AttackRange = "tag8",--攻击范围的触发提
    FireBullet = "tag9",--攻击范围的触发提
    Enemy_zombie="tag10",--普通僵尸
    Enemy_BombZombie="tag11",--自爆僵尸
    Enemy_SmallDog="tag12",--小狗
    Enemy_Lord="tag13",--领主
    Enemy_BigDog="tag14",--大狗
    Enemy_Charizard="tag15",--喷火龙
    
    BossWeapon="tag16",--boss武器  --领主大刀 喷火龙火焰
    Player = "Player",
    -- more
    CardBuffExp = "CardBuffExp", --卡牌buff系统的经验球
}


--mgrName,改成自己想要的名字后，通过读取此table来获取
cysoldierssortie_MgrName = {
    total = "cysoldierssortie_mgr_total",
    --
    config = "cysoldierssortie_mgr_config",
    fx = "cysoldierssortie_mgr_fx",
    input = "cysoldierssortie_mgr_input",
    level = "cysoldierssortie_mgr_level",
    rider = "cysoldierssortie_mgr_riderDebug",
    vibration = "cysoldierssortie_mgr_vibration",
    saver = "cysoldierssortie_mgr_saver",
    cam = "cysoldierssortie_mgr_cam",
    scene = "cysoldierssortie_mgr_scene",
    --unitySettings = "cysoldierssortie_mgr_unitySettings",
    --
    lang = "cysoldierssortie_langMgr",
    event = "cysoldierssortie_event",
    json = "cysoldierssortie_json",

    bullet = "cysoldierssortie_mgr_bullet",
    ui = "cysoldierssortie_mgr_ui",
    enemy = "cysoldierssortie_mgr_enemy",
    AniMesh = "cysoldierssortie_mgr_AniMesh",
    
    PoolMgr = "cysoldierssortie_mgr_pool",
    --
    SlotMgr = "cysoldierssortie_comp_hero_slot_mgr",
    --
    ActorInstanceMgr = "cysoldierssortie_mgr_actorInstance",
    --
    EffectMgr = "cysoldierssortie_mgr_effect",
    --
    AttackMgr = "cysoldierssortie_mgr_attack",
    --
    TimerMgr = "cysoldierssortie_mgr_timer",
    --
    PerformanceMgr = "cysoldierssortie_performance_mgr",
    --小兵大作战入口
    TroopClashTotal = "troopclash_lifescope",
    -- 凋落物
    DropMgr = "cysoldierssortie_mgr_drop",
    -- 通用抽卡系统
    CardBuffMgr = "cysoldierssortie_mgr_cardBuff",
    ---kingshot入口
    ---@type kingshot_lifescope
    KingShotTotal = "kingshot_lifescope",
}

cysoldierssortie_event_name = 
{
    START_GAME = "STAR_GAME",   
}


cysoldierssortie_common_unit_prop = 
{
    DecelerationTime = 0.2,   --减速 0.3s
    DecelerationPercent = 0.3, --减速至原来速度的 70%
}


bullet_collider_size_set =
{
    ["art/effects/effects/effect_gongjian_kingshot/prefabs/effect_gongjian_kingshot.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=0.5,y=0.5,z=1}
    },
    ["art/effects/effects/effect_mini_monster_skill1/prefabs/effect_mini_monster_skill1.prefab"] = 
    {
        pos = {x=0,y=0,z=1.12},
        center = {x=0,y=1,z=0},
        size = {x=8,y=4,z=8}
    },
    ["art/effects/effects/effect_mini_atk_01/prefabs/effect_mini_atk_01.prefab"] =
    {
        pos = {x=0,y=0,z=1.12},
        center = {x=0,y=0,z=1.2},
        size = {x=0.3,y=0.3,z=2}
    },
    ["art/effects/effects/effect_yinghuo05_mini_skill02/prefabs/effect_yinghuo05_mini_skill02.prefab"] =
    {
        pos = {x=0,y=0,z=1.12},
        center = {x=0,y=0,z=1.2},
        size = {x=0.3,y=0.3,z=2}
    },
    ["art/effects/effects/effect_mini_atk_02/prefabs/effect_mini_atk_02.prefab"] =
    {
        pos = {x=0,y=0,z=4.5},
        center = {x=0,y=0,z=1.2},
        size = {x=0.5,y=0.5,z=2.5}
    },
    ["art/effects/effects/effect_mini_atk_03/prefabs/effect_mini_atk_03.prefab"] =
    {
        pos = {x=0,y=0,z=4.5},
        center = {x=0,y=0,z=1.2},
        size = {x=0.5,y=0.5,z=2.5}
    },
    ["cysoldierssortie/art/texture/bakeparticle/bake_atk_4/bake_effect_atk_4.prefab"] =
    {
        pos = {x=0,y=0,z=1.4},
        center = {x=0.01635415,y=0,z=1.267314},
        size = {x=0.5696796,y=0.3,z=2.934628}
    },
    ["cysoldierssortie/art/texture/bakeparticle/bake_atk_5/bake_effect_atk_5.prefab"] =
    {
        pos = {x=0,y=0,z=1.5},
        center = {x=0.01875047,y=0,z=1.576051},
        size = {x=0.8433676,y=0.3,z=3.552103}
    },
    ["cysoldierssortie/art/texture/bakeparticle/bake_atk_6/bake_effect_atk_6.prefab"] =
    {
        pos = {x=0,y=0,z=1.5},
        center = {x=0.07665047,y=0,z=2.379336},
        size = {x=0.43786,y=0.3,z=1.805}
    },
    ["cysoldierssortie/art/texture/bakeparticle/bake_atk_7/bake_effect_atk_7.prefab"] =
    {
        pos = {x=0,y=0,z=1.5},
        center = {x=0.01875047,y=0,z=1.576051},
        size = {x=0.8433676,y=0.3,z=3.552103}
    },
    ["art/effects/effects/effect_mini_generalatk/prefabs/effect_mini_generalatk.prefab"] =
    {
        pos = {x=0,y=0,z=2.08},
        center = {x=0,y=0,z=0.52},
        size = {x=3.457246,y=0.3,z=5.158675}
    },
    ["art/effects/effects/effect_feierrui02_mini_attack/prefabs/effect_feierrui02_mini_attack.prefab"] =
    {
        pos = {x=0,y=0,z=0.7},
        center = {x=0,y=0,z=0.7},
        size = {x=1.4,y=0.7,z=1.4}
    },
    ["art/effects/effects/effect_kelisiduo_mini_attack/prefabs/effect_kelisiduo_mini_attack.prefab"] =
    {
        pos = {x=0,y=0,z=2},
        center = {x=0,y=0,z=3},
        size = {x=1,y=1,z=3}
    },
    ["art/effects/effects/effect_longsha02_mini_skill02/prefabs/effect_longsha02_mini_skill02.prefab"] =
    {
        pos = {x=0,y=0,z=0.7},
        center = {x=0,y=0,z=0.5},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_maojia_mini_attack/prefabs/effect_maojia_mini_attack.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=1.2},
        size = {x=0.4,y=0.4,z=1.2}
    },
    ["art/effects/effects/effect_mini_shenshouatk1_1/prefabs/effect_mini_shenshouatk1_1.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_model_skill_shenshou_1/prefabs/effect_model_skill_shenshou_1.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_mini_shenshouatk2_1/prefabs/effect_mini_shenshouatk2_1.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_model_skill_wen/prefabs/effect_model_skill_wen.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1.4,y=0.7,z=1.4}
    },
    ["art/effects/effects/effect_skill_shenshou_2/prefabs/effect_skill_shenshou_2.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_feinikesi01_mini_skill01/prefabs/effect_feinikesi01_mini_skill01.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=2,y=1,z=1}
    },
    ["art/effects/effects/effect_andelie01_mini_skill01/prefabs/effect_andelie01_mini_skill01.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=2,y=1,z=2}
    },
    ["art/effects/effects/effect_feinikesi01_mini_attack/prefabs/effect_feinikesi01_mini_attack.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
    ["art/effects/effects/effect_feinikesi01_mini_attack_hit/prefabs/effect_feinikesi01_mini_attack_hit.prefab"] =
    {
        pos = {x=0,y=0,z=0},
        center = {x=0,y=0,z=0},
        size = {x=1,y=1,z=1}
    },
}

cysoldierssortie_CommonEffect = {
    UpgradeEffect = "art/effects/effects/effect_mini_upgrade/prefabs/effect_mini_upgrade.prefab",
}

cysoldierssortie_CharacterOtherEffect = {
    EnemyBombBloodEffect = "cysoldierssortie/prefab/enemy/enemybombblood.prefab",
    EnemyMonsterDieEffect = "art/effects/effects/effect_mini_monster_die/prefabs/effect_mini_monster_die.prefab",
}
cysoldierssortie_config_character_level =
{
    --对应MiniUnit ID
    [1] = 101,    
    [2] = 102,
    [3] = 103,
    [4] = 104,
    [5] = 105,
    [101] = 1,
    [102] = 2,
    [103] = 3,
    [104] = 4,
    [105] = 5,
}

cysoldierssortie_weapon_type = {
    SoldierAttack_1st = 1,
    SoldierAttack_2nd = 2,
    SoldierAttack_3rd = 3,
    SoldierAttack_4th = 4,
    SoldierAttack_5th = 5,
    EnemyNormalAttack = 6,
    HeroSuanTouAttack = 11,
    HeroKeLiSiDuoAttack = 12,
    HeroSaiWeiYaLaAttack = 14,
    HeroLongShaAttack = 15,
    HeroSelfRotationAttack = 18,
    HeroKeLiSiDuoSecAttack  = 17,
    HeroDroneAttack = 19,--神兽技能
    HeroSaiWeiYaLaUltraAttack = 20,
    EnemyBossCaptainUltra = 21,
    EnemyBossOcpusesUltra = 22,
    HeroBuffWeaponAttack = 23,
    HeroDrone2Attack =24,--神兽技能2
    HeroSaiweiyalaUltra2 = 25,
    HeroFireAttack = 26, --喷火
    HeroSelfRotateAttack2 = 27,
    HeroFeinikesiUltra = 28,
    HeroAndelieUltra = 29,
    EnemyBossStepUltra = 30,-- 沙滩裤踩地板
    HeroSaiweiyalaUltra3 = 35, -- kingshot用
    HeroCurveAttack = 36, -- kingshot用
}

cysoldierssortie_damage_entity_shape_type = 
{
    Sphere = 1,
    Rect = 2,
}

cysoldierssortie_attack_comp_proxy = {
    [101] = true,
    [102] = true,
    [103] = true,
    [104] = true,
    [105] = true,
}

cysoldierssortie_PoolObjectName = {
    GetRewardAniText="GetRewardAniText",
    HpNumAniText= "HpNumAniText",
    PlayerHpSlider= "PlayerHpSlider",
    MonsterHpSlider= "MonsterHpSlider",
    BossHpSlider= "BossHpSlider",
    ObstacleMapHpSlider= "ObstacleMapHpSlider",
    PlayerHpHero= "PlayerHpHero",
    NumText= "NumText",
    
    Hero = "Hero",
    
    TakeDamageEntity = "TakeDamageEntity",
    
    AttackRangeEntity = "AttackRangeEntity",
    BaseBulletEntity = "BaseBulletEntity",

    CriticalHpNumAniText= "CriticalHpNumAniText", 
    SkillHpNumAniText= "SkillHpNumAniText",
    
    RewardCoinEntity = "RewardCoinEntity",

    RewardCoin2D = "RewardCoin2D",

    HeroHpNumAniText = "HeroHpNumAniText",
    -- 英雄加血，目前只用在首充无尽模式的状态恢复
    HeroAddHpNumAniText = "HeroAddHpNumAniText",
    
    DelayedHpSlider= "DelayedHpSlider",
    AttackLock= "AttackLock",

    HpNumAniText_kingshot= "HpNumAniText_kingshot",
    SkillHpNumAniText_kingshot= "SkillHpNumAniText_kingshot",
    CriticalHpNumAniText_kingshot= "CriticalHpNumAniText_kingshot",
}

cysoldierssortie_comp_name = {
    HP = "HP",
    AI = "AI",
    Attack = "Attack",
    Level = "Level",
    Move = "Move",
    FeedBack = "FeedBack",
    EarlyWarning = "EarlyWarning"
}

cysoldierssortie_comp_register =
{
    [cysoldierssortie_comp_name.HP] = "cysoldierssortie_comp_hp",
    [cysoldierssortie_comp_name.AI] = "cysoldierssortie_comp_fsm",
    [cysoldierssortie_comp_name.Attack] = "cysoldierssortie_comp_attack",
    [cysoldierssortie_comp_name.Level] = "cysoldierssortie_comp_character_level",
    [cysoldierssortie_comp_name.Move] = "cysoldierssortie_comp_move",
    [cysoldierssortie_comp_name.FeedBack] = "cysoldierssortie_comp_feedback",
    [cysoldierssortie_comp_name.EarlyWarning] = "cysoldierssortie_comp_early_warning"
}

cysoldierssortie_character_state = {
    Idle = "Idle",
    Move = "Move",
    Attack = "Attack",
    Die = "Die",
    LookAt = "LookAt" --旋转
}

cysoldierssortie_player_action_state =
{
    Stand = 1,
    Move = 2
    
}
cysoldierssortie_hero_anim_set = {
    Ability = "Ability",
    Dead = "Dead",
    Run = "Run",
    Stand = "Stand",
    Walk = "Walk",
    ReSpawn = "ReSpawn",
    Show = "Show",
    Skill01 = "Skill01",
    Skill04 = "Skill04",
    Skill03 = "Skill03",
    Run_Skill01_Loop = "Run_Skill01_Loop",
    Spawn = "Spawn"
}

cysoldierssortie_enemy_pos_config = 
{
    baseXOffset = 0.8,   -- 基础X轴偏移量（左右列间距）
    baseZInterval = 0.8, -- 基础Z轴行间距
    xRandom = 0.3,       -- X轴随机偏移范围
    zRandom = 0.3        -- Z轴随机偏移范围
}

cysoldierssortie_IsDebug = false

--[[cydoodlemagic_log("涂鸦法师"); --使用默认绿色
cydoodlemagic_log(1,"涂鸦法师"," 真好玩");--使用红色
cydoodlemagic_log(2,"涂鸦法师"," 真好玩");--使用绿色
cydoodlemagic_log(3,"涂鸦法师"," 真好玩");--使用蓝色]]
function cysoldierssortie_log(...)
    if cysoldierssortie_IsDebug then


        local msg = ""
        local args = { ... } -- 将变长参数存储在表中

        local logcolor = "00FF00"
        local startIndex = 1

        if tonumber(args[1]) == 1 then
            if #args>1 then
                logcolor = "FF0000"
                startIndex = 2
            end

        elseif tonumber(args[1]) == 2 then
            if #args>1 then
                logcolor = "00FF00"
                startIndex = 2
            end

        elseif tonumber(args[1]) == 3 then
            if #args>1 then
                logcolor = "0000FF"
                startIndex = 2
            end

        else
            logcolor = "00FF00"
            startIndex = 1
        end

        for i = startIndex, #args do
            msg = msg .. tostring(args[i])
        end
        local str = tostring(msg)
        local logColor = string.format("士兵突击LogInfo: <color=#%s>%s</color>", logcolor, str)
        print(logColor)
        --print(msg)
        --"FF0000"  红色
        --"00FF00"  绿色
        --"0000FF"   蓝色


    end
end

--音效
cysoldierssortie_FxName = {
    pistol_shoot="pistol_shoot",--手枪射击音效
    rifle_shoot="rifle_shoot",--普通步枪射击音效	
    ak_shoot="ak_shoot",--AK步枪射击音效
    MachineGun_shoot="MachineGun_shoot",--机枪射击音效
    sniper_shoot="sniper_shoot",--狙击枪射击音效
    ArmoredShoot="ArmoredShoot",--装甲车发射音效
    fireFlame="fireFlame",--喷火兵发射音效
    gatling_shoot="gatling_shoot",--加特林射击音效
    missile="missile",--火箭/坦克/导弹兵发射导弹时音效
    missileBomb="missileBomb",--坦克/导弹/火箭砸落爆炸音效
    snowball_crush="snowball_crush",--雪球破碎音效
    balloon_crush="balloon_crush",--气球击碎音效
    down="down",--士兵被撞击倒下时
    boss23="boss23",--恶狗和恐龙出现时音效
    boss1="boss1",--领主出现时音效

    propsBuff="propsBuff",--穿过buff道具门反馈音效
    propsDeBuff="propsDeBuff",--穿过debuff道具门反馈音效
    gameWin="gamewin.mp3",--游戏胜利
    gameFail="gamefail.mp3",--游戏失败
    soldierDie="soldierDie",
    enemyDie="enemydie.wav",
    hit="hit.mp3",--木桶受击音效
    barrel_bomb="barrel_bomb.mp3",--木桶击败爆炸音效 --爆炸桶爆炸音效
    obtained="obtained.mp3",--获取兵种时
    upgrade="upgrade.mp3",--兵种升级时（普通士兵变成加特林士兵/提高攻击力/攻速等）
    
}
local fx = nil
function cysoldierssortie_OpenMusicController(bgFxName)
    if fx == nil or bc_IsNull(fx.luaMono) then
        fx = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.fx)
    end
    fx:OpenMusicController(bgFxName)
end

--播放技能发射音效
function cysoldierssortie_PlaySkillSpecial(skillID)
   -- log.Error(skillID)
    if not skillID then
        return
    end
    local skill_cfg = nil
    if cysoldierssortie_TroopClash then
        ---@type troopclash_mgr_level
        local lvMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        skill_cfg = lvMgr.resMgr:GetSkillConfigById(skillID)
    elseif cysoldierssortie_KingShot then
        ---@type kingshot_mgr_level
        local lvMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        skill_cfg = lvMgr.resMgr:GetSkillConfigById(skillID)
        if skill_cfg then
            --string转成int
            local fullScreenTime =  skill_cfg.FullscreenEffectsTime and tonumber(skill_cfg.FullscreenEffectsTime) or 0
            if fullScreenTime~=0 then
                local uiMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ui)
                uiMgr:ShowFullScreenEffect(fullScreenTime)
            end
        end
    else
        local game_scheme 	= require "game_scheme"
        skill_cfg = game_scheme:MiniSkill_0(skillID)
    end
    if not skill_cfg then
        return
    end
    local fxName=skill_cfg.SpecialSound
    if not fxName or fxName=="" then
        return
    end
    cysoldierssortie_PlaySfx(fxName,1,0.1)
end

--播放技能命中音效
function cysoldierssortie_PlaySkillDst(skillID)
    if not skillID then
        return
    end
    local skill_cfg = nil
    if cysoldierssortie_TroopClash then
        ---@type troopclash_mgr_level
        local lvMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        skill_cfg = lvMgr.resMgr:GetSkillConfigById(skillID)
    else
        local game_scheme 	= require "game_scheme"
        skill_cfg = game_scheme:MiniSkill_0(skillID)
    end
    if not skill_cfg then
        return
    end
    local fxName=skill_cfg.DstSound
    if not fxName or fxName=="" then
        return
    end


    cysoldierssortie_PlaySfx(fxName)
end

function cysoldierssortie_PlaySfx(fxName, duration, cd,fullAbName,randomVolume)
    if fx == nil or bc_IsNull(fx.luaMono) then
        fx = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.fx)
    end
    local ui_setting_data = require "ui_setting_data"
    local ui_setting_attr_enum = require "ui_setting_attr_enum"
    local EnSubModel = ui_setting_attr_enum.EnSubModel
    local EnVoiceAttrKey = ui_setting_attr_enum.EnVoiceAttrKey
    local value = ui_setting_data.GetAttrData(EnSubModel.En_Model_Voice, EnVoiceAttrKey.En_AttrKey_VoiceEffect)
    local volume = tonumber(value)
    --   
    local _duration = 1
    local _cd = 1

    if fxName == cysoldierssortie_FxName.gatling_shoot then
        return fx:PlaySfxLoop(fxName, volume)

    elseif fxName == cysoldierssortie_FxName.MachineGun_shoot then
        return fx:PlaySfxLoop(fxName, volume)
    elseif fxName == cysoldierssortie_FxName.fireFlame then
        return fx:PlaySfxLoop(fxName, volume)
    elseif fxName == cysoldierssortie_FxName.enemyDie then
        volume=volume*0.5
        return fx:PlaySfxOnNum(fxName, duration, 0.5, volume,nil,3)
    elseif fxName == cysoldierssortie_FxName.gameWin then
        duration=4
    elseif fxName == cysoldierssortie_FxName.gameFail then
        duration=4
    end

    if duration == nil then
        duration = _duration
    end

    if cd == nil then
        cd = _cd
    end
    if fx then
        return fx:PlaySfxCd(fxName, duration, cd, volume,nil,fullAbName,randomVolume)
    end
  
end

function cysoldierssortie_StopLoopSfx(obj)
    if fx == nil or bc_IsNull(fx.luaMono) then
        fx = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.fx)
    end

    fx:StopSfxLoop(obj)

end

function cysoldierssortie_NumTextScaleAni(NumTextObj)
    if bc_IsNotNull(NumTextObj) then
        if not CS.DG.Tweening.DOTween.IsTweening(NumTextObj.transform) then
            NumTextObj.transform:DOScale(1.5,0.15):SetLoops(2 , CS.DG.Tweening.LoopType.Yoyo)
        end
    end
end

--加载ab
function cysoldierssortie_LoadAB(gameAbName, callBack)
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:AddMainLoopCounter()
    end
    bc_LoadMgr.LoadPrefab(gameAbName, function(obj)
        totalMgr:ReduceMainLoopCounter()
        local minigame_mgr = require "minigame_mgr"
        if not minigame_mgr.getIsOpenMiniGame() then
            return
        end
       --print("加载完成" .. gameAbName)
        if bc_IsNull(obj) then
            print("Error!! load err", gameAbName)
            return
        end
        if callBack then
            callBack(obj)
        end
    end)
end

-- coroutine用
cysoldierssortie_coUtil = require 'xlua.util'
-- 
function cysoldierssortie_StartCoroutine(luaComp, func, ...)
    return luaComp.luaMono:StartCoroutine(require('xlua.util').cs_generator(func, ...))
end

function cysoldierssortie_StopCoroutine(luaComp, co)
    return luaComp.luaMono:StopCoroutine(co)
end

function cysoldierssortie_randomFloat(lower, greater)
    return lower + math.random()  * (greater - lower);
end

-- timer，替换coroutine用
local timerMgr = nil
function cysoldierssortie_DelayCallOnce(seconds,func)
    if timerMgr == nil then
        timerMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.TimerMgr)
    end
  return  timerMgr:SetTimerEx(func, seconds, 0, true, "")
end

function cysoldierssortie_DelayCall(seconds,func)
    if timerMgr == nil then
        timerMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.TimerMgr)
    end
    return timerMgr:SetTimerEx(func, seconds, 0, false, "")
end

function cysoldierssortie_StopDelayCall(timerHandle)
    if timerMgr == nil then
        timerMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.TimerMgr)
    end
    return timerMgr:KillTimer(timerHandle)
end

function cysoldierssortie_KillTimer(timer)
    if not timer then
        return
    end
    if timerMgr == nil then
        timerMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.TimerMgr)
    end
    timerMgr:KillTimer(timer)
    timer = nil
end

function cysoldierssortie_DisposeTimer()
    if timerMgr == nil then
        timerMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.TimerMgr)
    end
    timerMgr:Dispose()
end

function cysoldierssortie_GetMgr(name)
    return cysoldierssortie_mgrTable[name]
end

function cysoldierssortie_AddMgr(name, table)
    if cysoldierssortie_mgrTable[name] then
        print("已经有了mgr，可能存在逻辑错误: " .. name)
    end
    _G[name] = table
    cysoldierssortie_mgrTable[name] = table
end

function cysoldierssortie_CloseLight()
    local mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.scene)
    if mgr then
        mgr:ResetGlobalValue();
    end
end

function cysoldierssortie_OpenLight()
    local mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.scene)
    if mgr then
        mgr:OnReLoadSceneGlobalValue();
    end
end
local cysoldierssortie_entity_loader_batch = require("cysoldierssortie_entity_loader_batch")
function cysoldierssortie_RemoveAllMgr()
    if cysoldierssortie_mgrTable then
        cysoldierssortie_entity_loader_batch.__delete(cysoldierssortie_entity_loader_batch)
        for k, v in pairs(cysoldierssortie_mgrTable) do
            if cysoldierssortie_mgrTable[k] then
                if cysoldierssortie_mgrTable[k].OnDestroy then
                    cysoldierssortie_mgrTable[k]:OnDestroy()
                end
            end
        end

        for k, v in pairs(cysoldierssortie_mgrTable) do
            if cysoldierssortie_mgrTable[k] then
                cysoldierssortie_mgrTable[k] = nil
                _G[k] = nil
            end
        end
    end
end

function cysoldierssortie_RemoveMgr(name)
    _G[name] = nil
    cysoldierssortie_mgrTable[name] = nil
end

function cysoldierssortie_LoadPrefab(gameAbName, callBack)
    local asset_loader = require "asset_loader"
    local loader = asset_loader(gameAbName,"cysoldierssortie")
    loader:load(
            function(res)
                local minigame_mgr = require "minigame_mgr"
                if not minigame_mgr.getIsOpenMiniGame() then
                    return
                end
                if res and res.asset then
                    --log.Warning("cysoldierssortie_LoadPrefab complete", gameAbName)
                    if isAsync then
                        local AsyncOp = ApiHelper.InstantiateAsync(res.asset)
                        AsyncOp:completed("+", function()
                            local ngo = AsyncOp.Result[0]
                            if callBack then
                                callBack(ngo)
                            end
                        end)
                    else
                        local ngo = CS.UnityEngine.GameObject.Instantiate(res.asset)
                        if callBack then
                            callBack(ngo)
                        end
                    end
                else
                    log.Error("cysoldierssortie_LoadPrefab fail", gameAbName)
                end
            end
    )
    return loader
end

function cysoldierssortie_LoadBytes(gameAbName, callBack)
    local asset_loader = require "asset_loader"
    local loader = asset_loader(gameAbName, "cysoldierssortie")
    local log = require "log"
    loader:load(
        function(res)
            if res and res.asset then
                if callBack then
                    callBack(res.asset.text)
                end
            else
                log.Error("cysoldierssortie_LoadPrefab fail", gameAbName)
            end
        end
    )
    return loader
end

function cysoldierssortie_LoadAsset(gameAbName, callBack)
    local asset_loader = require "asset_loader"
    local loader = asset_loader(gameAbName,"cysoldierssortie")
    loader:load(
            function(res)
                local minigame_mgr = require "minigame_mgr"
                if not minigame_mgr.getIsOpenMiniGame() then
                    return
                end
                if res and res.asset then
                    if callBack then
                        callBack(res.asset)
                    end
                end
            end
    )
    return loader
end

function cysoldierssortie_IsNil(uobj)
    return uobj == nil or uobj:Equals(nil)
end
--获取物体身上的luaClass实例
local LuaCompCache = {}
local cysoldierssortie_entity_loader = require("cysoldierssortie_entity_loader")
function ClearLuaCompCache()
    LuaCompCache = {}
    cysoldierssortie_entity_loader.clear_cache()
    cysoldierssortie_entity_loader_batch.clear_cache()
end

function SetLuaCompCache(go,comp)
    rawset(LuaCompCache,go,comp)
end

local luaCompType = typeof(CS.CasualGame.lib_ChuagnYi.LuaMono)
function cysoldierssortie_GetLuaComp(comp)
    local res = rawget(LuaCompCache,comp)
    if res then
        return res
    end

    res = comp.luaComp
    if res then
        rawset(LuaCompCache,comp,res)
        return res
    else
        local temp = comp:GetComponent(luaCompType);
        if bc_IsNotNull(temp) then
            res = temp.luaComp        
            rawset(LuaCompCache,comp,res)
            return res
        end
    end
    return res
end

cysoldierssortie_CreateEntityRecord = {}
function WriteOneRecord(frameCount, newCreateCount, newCreateTime, oldReuseCount, oldReuseTime)
    table.insert(cysoldierssortie_CreateEntityRecord,frameCount)
    table.insert(cysoldierssortie_CreateEntityRecord,newCreateCount)
    table.insert(cysoldierssortie_CreateEntityRecord,newCreateTime)
    table.insert(cysoldierssortie_CreateEntityRecord,oldReuseCount)
    table.insert(cysoldierssortie_CreateEntityRecord,oldReuseTime)
end

function DumpEntityRecord()
    local groupCount = #cysoldierssortie_CreateEntityRecord / 5
    local totalNewCreateCount = 0
    local totalNewCreateTime = 0
    local totalOldReuseCount = 0
    local totalOldReuseTime = 0
    for i = 0,groupCount-1 do
        local frameCount = cysoldierssortie_CreateEntityRecord[i*5 + 1]
        local newCreateCount = cysoldierssortie_CreateEntityRecord[i*5 + 2]
        local newCreateTime = cysoldierssortie_CreateEntityRecord[i*5 + 3]
        local oldReuseCount = cysoldierssortie_CreateEntityRecord[i*5 + 4]
        local oldReuseTime = cysoldierssortie_CreateEntityRecord[i*5 + 5]
        totalNewCreateCount = totalNewCreateCount + newCreateCount
        totalNewCreateTime = totalNewCreateTime + newCreateTime
        totalOldReuseCount = totalOldReuseCount + oldReuseCount
        totalOldReuseTime = totalOldReuseTime + oldReuseTime
    end
    print("LD: check create entity: new create count:",totalNewCreateCount," avg:", totalNewCreateTime/totalNewCreateCount,
        ", old reuse count:",totalOldReuseCount," avg:",totalOldReuseTime/totalOldReuseCount)
    cysoldierssortie_CreateEntityRecord = {}
end

--- 简单的CSV解析
---@param data string 要解析的CSV数据
---@param headLine number 从第几行还是解析
---@param maxCol number 最大列数，如果为nil则读取所有列
function cysoldierssortie_LoadCSVByString(data, headLine, maxCol)
    local tmpMaxCol = maxCol or 9999
    -- 按行划分
    local lineStr = string.split(data, '\r\n')
    local allLine = {}
    local lineStrLength = #lineStr
    for i = headLine + 1, lineStrLength, 1 do
        if string.IsNullOrEmpty(string.trim(lineStr[i])) then
            break
        end
        -- 一行中，每一列的内容
        local content = string.split(lineStr[i], ",")
        local tmpRow = i - headLine
        if maxCol == nil then
            allLine[tmpRow] = content
        else
            local col = math.min(tmpMaxCol, #content)
            allLine[tmpRow] = {}
            for j = 1, col, 1 do
                allLine[tmpRow][j] = content[j]
            end
        end
    end
    return allLine
end