---@class kingshot_animator_gpu
local animatorGPU = bc_Class("kingshot_animator_gpu")
local aniName_Atk = "Attack"
local animNameMap = {
    ["Ability"] = aniName_Atk,
    ["ReSpawn"] = "Stand"
}

---@type kingshot_comp_character
animatorGPU.charactor = nil
animatorGPU.animator = nil
---@type number 当前动画速度
animatorGPU.currentSpeed = nil
---@type string 当前动画
animatorGPU.currentAnim = nil
---@type table<string,number> 状态映射表
animatorGPU.statesMap = nil
---@type number 攻击动画时长，单位S
animatorGPU.atkClipLength = nil

local animFrame = 30

function animatorGPU:__init(...)
    local modelGo = nil
    modelGo, self.charactor = ...
    self.animator = KingShot_Define.GetGpuAnimate(modelGo)
    self.currentSpeed = nil
    self.currentAnim = nil
    self.statesMap = {}
    self.playerCtrl = self.charactor._player
    local stateArr = self.animator.stateNames
    local animClipArray = self.animator.animations
    local stateCount = stateArr.Length - 1
    for i = 0, stateCount do
        local tmpName = stateArr[i]
        self.statesMap[tmpName] = i
        if tmpName == aniName_Atk then
            self.atkClipLength = animClipArray[i].nbrOfFramesPerSample / animFrame
        end
    end
end

---控制播放动画
---@param state string 动画状态
function animatorGPU:PlayAnimatorState(state)
    state = animNameMap[state] or state
    if self.currentAnim == state then
        return
    end
    self.currentAnim = state
    self.animator:SetAnimatorState(self.statesMap[state])
end

function animatorGPU:SetTrigger(state)
    self:PlayAnimatorState(state)
end

function animatorGPU:ResetTrigger(state)
    if state == cysoldierssortie_hero_anim_set.Ability then
        self:PlayAnimatorState(cysoldierssortie_hero_anim_set.Stand)
    end
end

function animatorGPU:SetAnimatorSpeed(speed)
    if self.currentSpeed == speed then
        return
    end
    self.currentSpeed = speed
    self.animator.speedFactor = speed
end

function animatorGPU:SetDissolve(dissolve)
    self.animator:SetDissolve(dissolve)
end

function animatorGPU:SetRenderPropertiesFloat(property, value)
    self.animator:SetRenderPropertiesFloat(property, value)
end

function animatorGPU:SetMaterialFloat(id, value)
    self.animator:SetMaterialFloat(id, value)
end

function animatorGPU:SetLayer(layer)
    self.animator:SetLayer(layer)
end

function animatorGPU:SetRenderPropertiesColor(property, value)
    self.animator:SetRenderPropertiesColor(property, value)
end

---@public 设置攻击循环
---@param useLoopInterval boolean 是否使用循环间隔
---@param attackInterval number 循环间隔
function animatorGPU:SetAttackLoopInterval(useLoopInterval, attackInterval)
    self.animator:SetAttackLoopInterval(useLoopInterval, attackInterval)
end

return animatorGPU
