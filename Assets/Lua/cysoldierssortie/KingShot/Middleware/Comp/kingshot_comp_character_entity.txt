-----@class kingshot_comp_character_entity : fusion_gopoolitem
--local entity = bc_Class("kingshot_comp_character_entity", require("fusion_gopoolitem"))
--local gw_hero_mgr = require "gw_hero_mgr"
--local game_scheme = require "game_scheme"
--local TypeOf_SkinnedMeshRenderer = typeof(CS.UnityEngine.SkinnedMeshRenderer)
--local TypeOf_MeshRenderer = typeof(CS.UnityEngine.MeshRenderer)
--local AnimatorCullingMode = CS.UnityEngine.AnimatorCullingMode
--local TypeOf_NavMeshAgent = typeof(CS.UnityEngine.AI.NavMeshAgent)
--local ObstacleAvoidanceType = CS.UnityEngine.AI.ObstacleAvoidanceType
--local kingshot_animator = require "kingshot_animator"
--local kingshot_animator_gpu = require "kingshot_animator_gpu"
--local ApiHelper = CS.XLuaUtil.LuaApiHelper
--local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
--local SetDestinationXYZ = ApiHelper.SetDestinationXYZ
--local IsActiveNavAgent = ApiHelper.IsActiveNavAgent
--local NewGpuAnimator = require("util").IsCSharpClass(CS.GPUAnimationBaker.Engine.ShaderIDs)
--local log = require("log")
--local ShadowCastingMode_Off = CS.UnityEngine.Rendering.ShadowCastingMode.Off
--local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
--local minigame_buff_mgr = require "minigame_buff_mgr"
--local minigame_mgr = require "minigame_mgr"
--
--entity.DataSrc = nil
-----@type kingshot_comp_character
--entity.character = nil
-----@type kingshot_mgr_actor
--entity.actorMgr = nil
-----@type boolean 是否存活状态
--entity.AliveFlag = nil
-----@type boolean 是否是玩家
--entity.isPlayer = nil
-----@type kingshot_player
--entity.playerCtrl = nil
--
--function entity:__init(...)
--    self:Ctor(...)
--    self.DataSrc = {}
--    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
--    neeRefer:Bind(self.DataSrc)
--    self._rfNumPos = self.DataSrc.rfNumPos
--    self._collider = self.DataSrc.Collider
--    self._releaseSkillPoint = self.DataSrc.ReleaseSkillPoint
--    self._hpPoint = self.DataSrc.HpPoint
--    self._modelRoot = self.DataSrc.ModelRoot
--    SetLuaCompCache(self.gameObject, self)
--end
--
--function entity:__delete()
--    if self._recycle_coroutine then
--        cysoldierssortie_KillTimer(self._recycle_coroutine)
--        self._recycle_coroutine = nil
--    end
--end
--
--function entity:Init(...)
--    self._animator = nil
--    self.modelGo = nil
--    self.character, self.actorMgr = ...
--    self.transform.localRotation = KingShot_Define.CacheQuaternion.Identity
--    self:InitLayer()
--    self._releaseSkillPoint.transform.localRotation = KingShot_Define.CacheQuaternion.Identity
--    self._pause_rotation = true
--    self._collider.enabled = true
--    self.gameObject:SetActive(true)
--    self.AliveFlag = true
--    self.playerCtrl = self.character._player
--    self.isPlayer = self.playerCtrl ~= nil
--end
--
--function entity:InitLayer()
--    self._layer = cysoldierssortie_unit_layer[self.character._unit_type]
--    self.gameObject.layer = self._layer
--    if not self.character._player then
--        self.gameObject.tag = cysoldierssortie_TagName.Enemy_zombie
--        self._cull = true
--    else
--        self.gameObject.tag = cysoldierssortie_TagName.Player
--        self._cull = false
--    end
--end
--
--function entity:PlayNewGetEffect()
--    if self.character._unit_type == cysoldierssortie_unit_type.Soldier then
--        local effect_path = cysoldierssortie_CommonEffect.UpgradeEffect
--        local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
--        local EffectParamCache = {}
--        EffectParamCache.auto_release = true
--        EffectParamCache.delay_release = 1
--        EffectParamCache.effect_path = effect_path
--        EffectParamCache.callBack = function(go)
--            go.transform.position = self.character.transform.position
--        end
--        EffectParamCache.maxWeightLimit = true
--        effect_mgr:CreateEffect(EffectParamCache)
--    end
--end
--
--function entity:CalBoundsSize(bounds)
--    local tmp_size = bounds.size
--    if tmp_size.x > tmp_size.z then
--        if tmp_size.x > tmp_size.y then
--            local temp = tmp_size.y
--            tmp_size.y = tmp_size.x
--            tmp_size.x = temp
--        end
--    else
--        if tmp_size.z > tmp_size.y then
--            local temp = tmp_size.y
--            tmp_size.y = tmp_size.z
--            tmp_size.z = temp
--        end
--    end
--
--    return tmp_size
--end
--
--function entity:InitBoundsData(renderers)
--    for i = 0, renderers.Length - 1 do
--        if i == 0 and not renderers[i]:IsNull() then
--            local bound_size = self:CalBoundsSize(renderers[i].bounds)
--            local maxHeight = bound_size.y
--            local hp_pos = self._hpPoint.transform.localPosition
--            if self.character._unit_type == cysoldierssortie_unit_type.Hero then
--                self._hpPoint.transform.localPosition = { x = hp_pos.x, y = 3, z = hp_pos.z }
--            else
--                self._hpPoint.transform.localPosition = { x = hp_pos.x, y = maxHeight + 0.5, z = hp_pos.z }
--            end
--            self._releaseSkillPoint.transform.localPosition = { x = 0, y = maxHeight / 3, z = 0 }
--            if self._collider then
--                self._collider.size = bound_size
--                self._collider.center = { x = 0, y = self._collider.size.y * 0.5, z = 0 }
--            end
--        end
--        renderers[i].shadowCastingMode = ShadowCastingMode_Off
--        if not NewGpuAnimator or not self._gpu_anim then
--            renderers[i].gameObject.layer = self._layer
--        end
--    end
--end
--
--function entity:SetRenderPropertiesFloat(property, value)
--    if self._animator and self._gpu_anim then
--        self._animator:SetRenderPropertiesFloat(property, value)
--    end
--end
--
--function entity:SetMaterialFloat(id, value)
--    if self._animator and self._gpu_anim then
--        self._animator:SetMaterialFloat(id, value)
--    end
--end
--
--function entity:SetRenderPropertiesColor(property, value)
--    if self._animator and self._gpu_anim then
--        self._animator:SetRenderPropertiesColor(property, value)
--    end
--end
--
--function entity:CreateModel()
--    if not self.AliveFlag then
--        return
--    end
--
--    self._modelRoot.transform.localRotation = KingShot_Define.CacheQuaternion.Identity
--    if self.character._isDrone then
--        return
--    end
--    if self.character._isBuilding then
--        return
--    end
--    local heroId = self.character._heroId
--    local star = self.character._star
--    local moudleId = gw_hero_mgr.ChangeHeroModel(heroId, star)
--    local moduleCfg = game_scheme:Modul_0(moudleId)
--    if not moduleCfg then
--        log.Error("[Mini Game SoldierSsortie] UniId = " ..
--            tostring(self.character._unitID) .. " model path not found!!!!!!")
--        return
--    end
--    local modelPath = moduleCfg.modelPath
--    modelPath = modelPath:gsub("%.prefab", "_simple.prefab")
--    local pool_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
--    --缓存当前的character，防止回调太慢导致character已经失效
--    local lastCharacter = self.character
--    local loadCallBack = function(gameObject)
--        self.modelGo = gameObject
--        if lastCharacter ~= self.character then
--            self:ReleaseModel()
--        else
--            self.modelGo.transform.localRotation = KingShot_Define.CacheQuaternion.Identity
--            self.modelGo.transform.localPosition = KingShot_Define.CacheVector3.Zero
--            self.modelGo.transform.localScale = {
--                x = self.character._scale,
--                y = self.character._scale,
--                z = self.character._scale
--            }
--            self._animator = gameObject:GetComponent(KingShot_Define.TypeOf.Animator)
--            if self._animator and not self._animator:IsNull() then
--                self._animator = kingshot_animator.New(self.modelGo, self._animator, self.character)
--                self._gpu_anim = false
--            else
--                self._animator = kingshot_animator_gpu.New(self.modelGo, self.character)
--                self._gpu_anim = true
--                if NewGpuAnimator then
--                    self._animator:SetLayer(self._layer)
--                end
--            end
--
--            local skinMeshRenderers = self.modelGo:GetComponentsInChildren(TypeOf_SkinnedMeshRenderer)
--            if skinMeshRenderers and skinMeshRenderers.Length > 0 then
--                self:InitBoundsData(skinMeshRenderers)
--            else
--                skinMeshRenderers = self.modelGo:GetComponentsInChildren(TypeOf_MeshRenderer)
--                if skinMeshRenderers and skinMeshRenderers.Length > 0 then
--                    self:InitBoundsData(skinMeshRenderers)
--                end
--            end
--            self._modelRoot.layer = self._layer
--            if not self.character._player then
--                if self._cull or not self.character._view_actor then
--                    self.modelGo:SetActive(false)
--                end
--                --self._cull = true
--            end
--            self.character:InitAnimState()
--            self.character:SpawnHp()
--        end
--    end
--
--    pool_mgr:CreateEntity(modelPath, self._modelRoot.transform, loadCallBack)
--end
--
--function entity:CreateNavMeshAgent(isBuffCreate)
--    if not isBuffCreate then
--        if minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.IgnoringTheTerrain, self.character) then
--            return
--        end
--    end
--
--    self._navMeshAgent = self.gameObject:GetComponent(TypeOf_NavMeshAgent)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        self._navMeshAgent = self.gameObject:AddComponent(TypeOf_NavMeshAgent)
--    else
--        self._navMeshAgent.enabled = true
--    end
--    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
--    self._navMeshAgent.acceleration = 1000
--    self._navMeshAgent.stoppingDistance = 0 --self.character._attackRange/2
--    self._navMeshAgent.angularSpeed = 360
--    self._crowed = false
--end
--
--function entity:SwitchObstacleAvoidState(Open)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--
--    if Open then
--        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
--    else
--        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
--    end
--end
--
--function entity:InitNavAgentData()
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--
--    if not self._navMeshAgent.isOnNavMesh or not self._navMeshAgent.isActiveAndEnabled then
--        return
--    end
--
--    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
--    if self._collider then
--        local minSize = self._collider.size.z
--        if minSize > self._collider.size.x then
--            minSize = self._collider.size.x
--        end
--        self._navMeshAgent.radius = minSize * 0.5
--        self._navMeshAgent.height = self._collider.size.y
--    else
--        self._navMeshAgent.radius = 0.5
--    end
--    self._crowed = true
--end
--
--function entity:RemoveNavMeshAgent()
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--    self._navMeshAgent.enabled = false
--end
--
--function entity:UpdateNavAgentProp(speed)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--
--    if not self._navMeshAgent.isOnNavMesh or not self._navMeshAgent.isActiveAndEnabled then
--        return
--    end
--    self._navMeshAgent.speed = speed
--end
--
--function entity:IsActiveNavAgent()
--    if not self._navMeshAgent then
--        return false
--    end
--    return IsActiveNavAgent(self._navMeshAgent)
--end
--
--function entity:SetDestination(position)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--    if not self._navMeshAgent.isOnNavMesh or not self._navMeshAgent.isActiveAndEnabled then
--        return
--    end
--    self._navMeshAgent:SetDestination(position)
--end
--
--function entity:SetDestinationXYZ(px, py, pz)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--    if not self._navMeshAgent.isOnNavMesh or not self._navMeshAgent.isActiveAndEnabled then
--        return
--    end
--    SetDestinationXYZ(self._navMeshAgent, px, py, pz)
--end
--
--function entity:StopNav(stop)
--    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
--        return
--    end
--
--    if not self._navMeshAgent.isOnNavMesh or not self._navMeshAgent.isActiveAndEnabled then
--        return
--    end
--    self._navMeshAgent.isStopped = stop
--
--    if self._crowed then
--        if not stop then
--            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
--        else
--            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
--        end
--    end
--end
--
--function entity:Dead(anim)
--    if not self.AliveFlag then
--        return
--    end
--    self.AliveFlag = false
--
--    anim = anim == nil and true or anim
--
--    if not anim then
--        self:Release()
--        return
--    end
--
--    if self.character._player then
--        ApiHelper.SetParent(self.transform, self.actorMgr.sceneMgr.LevelRoot)
--    end
--
--    if self._animator then
--        self._animator:SetTrigger(cysoldierssortie_hero_anim_set.Dead)
--    end
--    self._collider.enabled = false
--    self._recycle_coroutine = cysoldierssortie_DelayCallOnce(2, function()
--        self:Release()
--    end)
--end
--
--function entity:Release()
--    self:ReleaseModel()
--    self.actorMgr:PushOneEntity(self)
--end
--
--function entity:ReleaseModel()
--    if self.modelGo ~= nil then
--        NeeGame.ReturnObject(self.modelGo)
--        self.modelGo = nil
--    end
--end
--
----生命周期函数
--function entity:Update(deltaTime)
--    if not self.AliveFlag then
--        return
--    end
--    if self.character.troopClash_IgnoreBattleUpdate then
--        return
--    end
--    self.character:UpdateAI()
--    self:DistanceCull()
--    self:LookAtTarget(deltaTime)
--    self:DisToPlayer()
--    if not self._cull and self.character._view_actor then
--        self:UpdateHp()
--    end
--end
--
--function entity:UpdateHp()
--    self.character:UpdateHp()
--end
--
--function entity:GetDistance1D(z1, z2)
--    local dz = z1 - z2
--    return math.abs(dz)
--end
--
--function entity:DisToPlayer()
--    local playerZ = 0
--    local currentPositionX, currentPositionY, currentPositionZ = GetTransformPositionXYZ(self.transform)
--    self._currentPositionX = currentPositionX
--    if not self.character._player and self._isRunnerMode == nil then
--        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
--        local curLevel = levelMgr.curLevel
--        local isRunnerMode = curLevel:IsRunnerMode()
--        self._isRunnerMode = isRunnerMode
--        if self._isRunnerMode then
--            if not self._player then
--                self._player = curLevel.playerLua
--            end
--        end
--    end
--
--    if not self.character._player and self._isRunnerMode then
--        local x, y, z = self._player:GetPositionXYZ()
--        playerZ = z
--    end
--    self.character._disToPlayerZ = self:GetDistance1D(currentPositionZ, playerZ)
--    minigame_buff_mgr.CheckCondition(self.character, minigame_buff_mgr.ConditionType.Behind_Enemy,
--        self.character._disToPlayerZ)
--end
--
--function entity:LookAtTarget(deltaTime)
--    if not self.character._enemyRange then
--        return
--    end
--
--    local targetGo = self.character:GetTargetGo()
--
--    if self.character._freezeRotate then
--        return
--    end
--
--    local hasTarget = bc_IsNotNull(targetGo)
--    if hasTarget then
--        self._pause_rotation = false
--    end
--
--    if self._pause_rotation then
--        return
--    end
--
--    if self.character._unit_type ~= cysoldierssortie_unit_type.Hero and self.character._unit_type ~= cysoldierssortie_unit_type.Soldier then
--        return
--    end
--
--    if hasTarget then
--        self:SmoothRotateTo(targetGo.position, deltaTime)
--    else
--        self:AlignToForward(deltaTime)
--    end
--end
--
--function entity:DistanceCull()
--    if not minigame_mgr.GetIsStartGame() then
--        return
--    end
--    local cull_dis = 80
--    local horizontal_cull_dis = 60
--    local crowed_dis = 20
--
--    if self.character then
--        if self.character._player then
--            return
--        end
--    else
--        return
--    end
--
--    if not self._cam_mgr then
--        self._cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
--    end
--    local cam_to_ground_farthest_dis, cam_horizontal_max_dis = self._cam_mgr:GetFarthestDistance()
--    if cam_to_ground_farthest_dis then
--        cull_dis = cam_to_ground_farthest_dis
--    end
--    if cam_horizontal_max_dis then
--        horizontal_cull_dis = cam_horizontal_max_dis
--    end
--
--    if GCPerf then
--        --local character_posX, character_posY, character_posZ = GetTransformPositionXYZ(self.transform)
--        local disToPlayerZ = self.character._disToPlayerZ or 100
--        if self._cull then
--            if disToPlayerZ <= cull_dis and math.abs(self._currentPositionX or 100) <= horizontal_cull_dis then
--                if self.character._view_actor and bc_IsNotNull(self.modelGo) then
--                    self.modelGo:SetActive(true)
--                    minigame_buff_mgr.ShowBossTips(self.character)
--                end
--                self._cull = false
--                self.character:CreateNavAgent()
--            end
--        end
--
--        if not self._crowed and self._cull == false then
--            if disToPlayerZ <= crowed_dis then
--                self:InitNavAgentData()
--            end
--        end
--    end
--end
--
---- Aligns the character to face directly forward
--function entity:AlignToForward(deltaTime)
--    local lookDir = self.transform.forward
--    local forwardRotation = KingShot_Define.CS.Quaternion.LookRotation(lookDir)
--
--    if KingShot_Define.CS.Vector3.Angle(self._modelRoot.transform.forward, lookDir) <= 5 then
--        self._modelRoot.transform.rotation = forwardRotation
--        self._releaseSkillPoint.transform.rotation = forwardRotation
--        self._pause_rotation = true
--        return
--    end
--
--    --forwardRotation.y = 0 -- Only rotate on the horizontal plane
--    self:ApplyRotation(forwardRotation, deltaTime)
--end
--
---- Smoothly rotates the character to face a target position
--function entity:SmoothRotateTo(targetPosition, deltaTime)
--    local directionToTarget = targetPosition - self.transform.position
--    directionToTarget.y = 0 -- Only rotate on the horizontal plane
--    -- Create the desired rotation
--    if directionToTarget.magnitude < 0.8 then
--        return
--    end
--
--    local targetRotation = KingShot_Define.CS.Quaternion.LookRotation(directionToTarget)
--
--    if bc_CS_Vector3.Angle(self._modelRoot.transform.forward, directionToTarget) <= 5 then
--        self._modelRoot.transform.rotation = targetRotation
--        self._releaseSkillPoint.transform.rotation = targetRotation
--        return
--    end
--
--    -- Smoothly rotate toward the target
--    self:ApplyRotation(targetRotation, deltaTime)
--end
--
---- Applies a rotation to the model root and skill release point
--function entity:ApplyRotation(rotation, deltaTime)
--    self._modelRoot.transform.rotation = KingShot_Define.CS.Quaternion.Slerp(
--        self._modelRoot.transform.rotation,
--        rotation,
--        deltaTime * 10
--    )
--    self._releaseSkillPoint.transform.rotation = self._modelRoot.transform.rotation
--end
--
--return entity
