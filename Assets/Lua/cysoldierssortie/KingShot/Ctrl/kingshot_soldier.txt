---@class kingshot_soldier
local soldier = bc_Class("kingshot_soldier")
---@type kingshot_scene_mgr
soldier.sceneMgr = nil
---@type kingshot_res_mgr
soldier.resMgr = nil
---@type kingshot_ui_mgr
soldier.uiMgr = nil

---@type fusion_gopool
soldier.soldierPool = nil

---@type kingshot_soldierunit[] 记录存活的所有士兵
soldier.SoldierUnitList = nil

function soldier:__init(...)
    self.sceneMgr, self.resMgr = ...
    self.uiMgr = self.sceneMgr.uiMgr
    self.soldierPool = require("fusion_gopool").New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.SoldierUnit,
            "kingshot_soldierunit")
    self.soldierPool:Preload(30)
    self.LookAtTargetSystemInstance = KingShot_Define.LookAtTargetSystem.Instance
end

function soldier:Reset()
    self.SoldierUnitList = {}
end

function soldier:SpawnSoldier(skillConfig,skillPos)
    local soldierConfig = self.sceneMgr.resMgr:GetUnitConfigById(skillConfig.SoldierID)
    if not soldierConfig then
        Fusion.Error("无法找到小兵配置: " .. tostring(skillConfig.SoldierID))
        return
    end
    ---@type kingshot_soldierunit
    local soldierUnit = self.soldierPool:PopOne()
    soldierUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    soldierUnit:Init(self.sceneMgr, self, soldierConfig, skillPos)
    self.SoldierUnitList[#self.SoldierUnitList + 1] = soldierUnit
end

function soldier:SpawnSoldierByPos(unitId,skillPos)
    local soldierConfig = self.sceneMgr.resMgr:GetUnitConfigById(unitId)
    if not soldierConfig then
        Fusion.Error("无法找到小兵配置: " .. tostring(skillConfig.SoldierID))
        return
    end
    ---@type kingshot_soldierunit
    local soldierUnit = self.soldierPool:PopOne()
    soldierUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    soldierUnit:Init(self.sceneMgr, self, soldierConfig, skillPos)
    self.SoldierUnitList[#self.SoldierUnitList + 1] = soldierUnit
end

function soldier:SpawnTeamByPosCheck()
    local count = #self.sceneMgr.SoldierDatasByPos
    if count > 0 then
        local i = 1
        while i <= count do
            local data = self.sceneMgr.SoldierDatasByPos[i]
            local unit = self:SpawnSoldierByPos(data.SoldiderUnitID,data.Pos)
            --unit:ShowImmediately(data.Pos)
            table.remove(self.sceneMgr.SoldierDatasByPos, i)
            count = count - 1
        end
    end
end

function soldier:Update(deltaTime)
    --local pX, _, pZ = self.sceneMgr.playerCtrl:GetCenterXYZ()
    self:SpawnTeamByPosCheck()
    for _, v in ipairs(self.SoldierUnitList) do
        v:Update(deltaTime)
    end
end


---@param item kingshot_buildingitem
function soldier:PushOneBuildingItem(item)
    self.soldierPool:PushOne(item)
end

--- 角色开始射击
function soldier:CharacterFire(character)
    local unit = self.SoldierUnitList[character]
    unit:CharacterFire()
end

---玩家单位死亡,英雄和小兵都调用
function soldier:OnHeroDead(character)
    local unit = self.SoldierUnitList[character]
    unit:Die()
    self.SoldierUnitList[character] = nil
    --table.remove_value(self.SoldierUnitList, unit)

end

return soldier