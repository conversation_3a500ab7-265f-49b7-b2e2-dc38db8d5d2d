---@class kingshot_uibossguide_ctrl
local ctrl = bc_Class("kingshot_uibossguide_ctrl")
---@type kingshot_ui_mgr
ctrl.uiMgr = nil
ctrl.guideCon = nil
---@type fusion_gopool
ctrl.pool = nil
---@type table<kingshot_comp_character, kingshot_uibossguide>
ctrl.itemWithCharacter = nil
ctrl.itemCount = nil
ctrl.minPos = nil
ctrl.maxPos = nil
ctrl.halfCanvas = nil

local boundOffX = 80
function ctrl:__init(...)
    self.uiMgr = ...
    self.guideCon = self.uiMgr.DataSrc.GuideCon
    self.pool = require("fusion_gopool").new(self.uiMgr.dataSrc.guidePoolParent, self.uiMgr.dataSrc.BossGuidePrefab,
            "bcmanyknives_uibossguide")
    self.pool:preload(1)
    self.itemWithCharacter = {}
    self.itemCount = 0
    self.halfCanvas = self.uiMgr.realCanvaSize * 0.5
    self.halfCanvas.x = 720 * 0.5
    local boundOffTop = 80 + self.uiMgr.topOffY
    local boundOffBottom = 160
    self.minPos = { x = -self.halfCanvas.x + boundOffX, y = -self.halfCanvas.y + boundOffBottom }
    self.maxPos = { x = self.halfCanvas.x - boundOffX, y = self.halfCanvas.y - boundOffTop }
end

function ctrl:Update(deltaTime)
    if self.itemCount < 1 then
        return
    end
    local pX, pY, _ = self.uiMgr.sceneMgr.rolePlayer.center.position
    local tmpGuideArray = {}
    local tmpIndex = 0
    for k, v in pairs(self.itemWithCharacter) do
        local showFlag = v:RefreshInfo(pX, pY)
        if showFlag then
            tmpIndex = tmpIndex + 1
            tmpGuideArray[tmpIndex] = v
        end
    end
    if tmpIndex > 1 then
        table.sort(tmpGuideArray, self.CompareDistance)
        for i, v in ipairs(tmpGuideArray) do
            v.transform:SetAsLastSibling()
        end
    end
end

---@param a bcmanyknives_uibossguide
---@param b bcmanyknives_uibossguide
function ctrl.CompareDistance(a, b)
    return a.DistanceToPlayer > b.DistanceToPlayer
end

function ctrl:AddCharacter(character)
    ---@type bcmanyknives_uibossguide
    local item = self.pool:popOne()
    item.transform:SetParent(self.guideCon)
    item.transform.localScale = ManyKnivesDefine.CacheVector3.One
    item.transform.localRotation = ManyKnivesDefine.QuaternionIdentity
    item:Init(self, character)
    self.itemWithCharacter[character] = item
    self.itemCount = self.itemCount + 1
end

function ctrl:RemoveCharacter(character)
    local item = self.itemWithCharacter[character]
    if item then
        self.pool:pushOne(item)
        self.itemWithCharacter[character] = nil
        self.itemCount = self.itemCount - 1
    end
end

---判断位置是否在屏幕中
---@return boolean
function ctrl:CheckInView(canvasPos)
    return not (canvasPos.x < -self.halfCanvas.x or canvasPos.x > self.halfCanvas.x or canvasPos.y < -self.halfCanvas.y or
            canvasPos.y > self.halfCanvas.y)
end

function ctrl:dispose()
    if self.pool ~= nil then
        self.pool:clear()
        self.pool = nil
    end
end

return ctrl
