---@class troopclash_teamunit : fusion_gopoolitem
local unit = bc_Class("troopclash_teamunit", require("fusion_gopoolitem"))
---@type troopclash_scene_mgr
unit.sceneMgr = nil
---@type troopclash_team
unit.teamCtrl = nil
unit.DataSrc = nil
---@type troopclash_TeamConfig
unit.config = nil
---@type troopclash_enemyData
unit.teamData = nil
---@type number 记录队伍状态 0=集合状态，1=解散，2=成员死完
unit.ActionFlag = nil
---@type troopclash_comp_character[]
unit.characterArray = nil
---@type number 队伍存活数量
unit.characterCount = nil

unit.tween_TeamRot = nil

local wanderDurRandom = { 5, 8 }
local borderOff = 5

local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local TroopClash_Define = TroopClash_Define
local ipairs = ipairs
local math = math

function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(TroopClash_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
end

---@param sceneMgr troopclash_scene_mgr
---@param ctrl troopclash_team
---@param data troopclash_enemyData
---@param config troopclash_TeamConfig
function unit:Init(sceneMgr, ctrl, data, config)
    self.sceneMgr = sceneMgr
    self.teamCtrl = ctrl
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self.teamData = data
    self.config = config
    --直接参战解散
    local dissolutionFlag = self.config.Format == TroopClash_Define.TeamFormatType.Circle
    if dissolutionFlag then
        TroopClash_Define.SetTransformPositionXYZ(self.transform, 0, 0, 0)
        self.DataSrc.NavMeshAgent.enabled = false
        self.ActionFlag = 1
    else
        if self.teamData.Type == TroopClash_Define.TeamSpawnType.Pos then
            TroopClash_Define.SetTransformPositionXYZ(self.transform, self.teamData.Pos.x, self.teamData.Pos.y,
                self.teamData.Pos.z)
        else
            local newPos = self.sceneMgr:GetSpawnTeamPos(self.teamData.BossFlag)
            TroopClash_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
        end
        self.DataSrc.NavMeshAgent.enabled = true
        self.DataSrc.NavMeshAgent.speed = self.config.MoveSpeed
        self.ActionFlag = 0
    end
    self.gameObject:SetActive(true)

    self.characterArray = {}
    local pointArray = self:SpawnPointWithCount()
    local tmpIndex = 1
    local playerPos = nil
    if dissolutionFlag then
        playerPos = self.sceneMgr.playerCtrl:GetCenterVec3()
    end
    for _, data in ipairs(self.config.UnitDatas) do
        for i = 1, data.Count, 1 do
            local character = actorMgr:CreateCharacter(data.UnitId, pointArray[tmpIndex], self.transform)
            character.troopClash_IgnoreBattleUpdate = not dissolutionFlag
            self.characterArray[tmpIndex] = character
            self.teamCtrl.TeamUnitWithCharacter[character] = self
            self.teamCtrl.TeamUnitWithTransf[character.transform] = self
            tmpIndex = tmpIndex + 1
            if dissolutionFlag then
                local playerDir = playerPos - character.transform.position
                playerDir.y = 0
                character.transform.rotation = TroopClash_Define.CS.Quaternion.LookRotation(playerDir.normalized)
            end
        end
    end
    self.characterCount = tmpIndex - 1
    if not dissolutionFlag then
        self:Wandering(true)
    end
end

function unit:AddCharacter(character)
    self.characterCount = self.characterCount + 1
    character.troopClash_IgnoreBattleUpdate = false
    self.characterArray[self.characterCount] = character
    self.teamCtrl.TeamUnitWithCharacter[character] = self
    self.teamCtrl.TeamUnitWithTransf[character.transform] = self
end

function unit:SpawnPointWithCount()
    local count = self.config.UnitCount
    local pointArray = {}
    local max_radius = self.config.Radius
    if self.config.Format == TroopClash_Define.TeamFormatType.Circular then
        local golden_angle = 2.39996 -- 黄金角的弧度值，大约是2.39996
        for i = 0, count - 1, 1 do
            -- 基础参数计算
            local base_radius = math.sqrt(i / count) * max_radius
            local base_theta = i * golden_angle
            -- 添加半径扰动 (±5%)
            local radius_jitter = math.lerp(0.95, 1.05, math.random())
            local final_radius = math.min(base_radius * radius_jitter, max_radius)
            -- 添加角度扰动 (±15度)
            local angle_jitter = math.rad(math.lerp(-10, 10, math.random()))
            local final_theta = base_theta + angle_jitter
            -- 坐标计算
            local x = final_radius * math.cos(final_theta)
            local z = final_radius * math.sin(final_theta)
            pointArray[i + 1] = { x = x, y = 0, z = z }
        end
    else
        local pX, _, pZ = self.sceneMgr.playerCtrl:GetCenterXYZ()
        --整体旋转偏移
        local angleOff = math.random(0, 360)
        local perAngle = 360 / count
        --每个单位旋转偏移
        local rotOff = perAngle * 0.15
        local radiusOff = math.min(1, max_radius * 0.1)
        for i = 1, count, 1 do
            local deg = (i - 1) * perAngle + math.random(-rotOff, rotOff) + angleOff
            --半径加一点偏移
            local radius = max_radius + math.random(-radiusOff, radiusOff)
            local x = radius * math.cos(math.rad(deg))
            local z = radius * math.sin(math.rad(deg))
            local tmpPos = { x = x + pX, y = 0, z = z + pZ }
            _, tmpPos = self.sceneMgr:GetNavMeshPosition(tmpPos)
            tmpPos.y = 0
            pointArray[i] = tmpPos
        end
    end
    return pointArray
end

---新的游荡任务
function unit:Wandering(firstFlag)
    self:KillTween()
    local curPos = self.transform.position
    local moveLength = math.random(wanderDurRandom[1], wanderDurRandom[2]) * self.config.MoveSpeed
    local playerDir = self.sceneMgr.playerCtrl:GetCenterVec3() - curPos
    playerDir.y = 0
    local tmpRot = TroopClash_Define.CS.Quaternion.LookRotation(playerDir.normalized) *
        TroopClash_Define.CS.Quaternion.Euler(0, math.random(-70, 70), 0)
    local tarPos = curPos + (tmpRot * TroopClash_Define.CacheVector3.Forward).normalized * moveLength
    self.DataSrc.NavMeshAgent:SetDestination(tarPos)

    if not firstFlag then
        self.tween_TeamRot = TroopClash_Define.DOTween.Sequence()
    end
    for _, v in ipairs(self.characterArray) do
        if firstFlag then
            v.transform.rotation = tmpRot
        else
            self.tween_TeamRot:Insert(0, v.transform:DORotateQuaternion(tmpRot, 0.5))
        end
    end
end

function unit:Update(deltaTime, viewCenterPos, pX, pZ)
    if self.ActionFlag ~= 0 then
        return
    end
    --怪物太远，重置位置
    local resetWander = false
    local curX, curY, curZ = TroopClash_Define.GetTransformPositionXYZ(self.transform)
    local limitX = self.sceneMgr.cameraCtrl.camBorderSizeHalf.x
    local limitZ = self.sceneMgr.cameraCtrl.camBorderSizeHalf.y
    if self.teamData.BossFlag then
        limitX = limitX * TroopClash_Define.Params.BossWanderLimitScale
        limitZ = limitZ * TroopClash_Define.Params.BossWanderLimitScale
    else
        limitX = limitX * TroopClash_Define.Params.NormalWanderLimitScale
        limitZ = limitZ * TroopClash_Define.Params.NormalWanderLimitScale
    end
    if math.abs(curX - viewCenterPos.x) - self.config.Radius - borderOff > limitX
        or math.abs(curZ - viewCenterPos.z) - self.config.Radius - borderOff > limitZ then
        resetWander = true
        --boss不重置位置
        if not self.teamData.BossFlag then
            local newPos = self.sceneMgr:GetSpawnTeamPos(self.teamData.BossFlag)
            TroopClash_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
        end
    end
    if resetWander or
        (self.DataSrc.NavMeshAgent.hasPath and not self.DataSrc.NavMeshAgent.pathPending
            and self.DataSrc.NavMeshAgent.remainingDistance <= self.DataSrc.NavMeshAgent.stoppingDistance) then
        self:Wandering()
    end
    for _, v in ipairs(self.characterArray) do
        v:UpdateAI()
        -- 只刷新模型显示，不创建NavAgent
        v._character_entity:DistanceCull(true)
    end
    local disFromPlayer = TroopClash_Define.Func_GetDistance(pX, pZ, curX, curZ) -
        self.sceneMgr.playerCtrl.RadiusBind.value - self.config.Radius
    if disFromPlayer < self.config.SearchRange then
        self:Dissolution()
    end
end

--队伍解散
function unit:Dissolution()
    if self.ActionFlag == 0 then
        self:KillTween()
        self.ActionFlag = 1
        self.DataSrc.NavMeshAgent.enabled = false
        for _, v in ipairs(self.characterArray) do
            v.troopClash_IgnoreBattleUpdate = false
            --参战后，生成自己的NavAgent
            v:CreateNavAgent()
        end
    end
end

---队伍成员死亡
---@return boolean 是否全部死亡
function unit:CharacterDie(character)
    self.characterCount = self.characterCount - 1
    local allDieFlag = self.characterCount < 1
    if allDieFlag then
        --小队成员全部死亡，掉落道具
        if self.config.DropIds ~= nil then
            self.sceneMgr.propCtrl:DropProps(character.transform.position, self.config.DropIds)
        end
    end
    return allDieFlag
end

function unit:KillTween()
    if self.tween_TeamRot ~= nil then
        self.tween_TeamRot:Kill()
        self.tween_TeamRot = nil
    end
end

function unit:Recycle()
    self:KillTween()
end

function unit:__delete()
    self:KillTween()
    self:Dispose()
end

return unit
