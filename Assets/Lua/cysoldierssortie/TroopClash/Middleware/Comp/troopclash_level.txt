---@class troopclash_level
local level = bc_Class("troopclash_level")
---@type troopclash_mgr_level
level.levelMgr = nil
---@type troopclash_scene_mgr
level.sceneMgr = nil
---@type troopclash_ui_mgr
level.uiMgr = nil
---@type troopclash_res_mgr
level.resMgr = nil
---@type cysoldierssortie_mgr_cardBuff
level.cardBuffMgr = nil
level.MiniLevelCfg = nil
---@type troopclash_player
level.playerLua = nil

local cs_generator = require("xlua.util").cs_generator
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local TroopClash_Define = TroopClash_Define
local cysoldierssortie_FxName = cysoldierssortie_FxName
local cysoldierssortie_PlaySfx = cysoldierssortie_PlaySfx
local bc_IsNotNull = bc_IsNotNull
local bc_Time = bc_Time

function level:__init(...)
    self.levelMgr, self.MiniLevelCfg, self.uiMgr = ...
    self.sceneMgr = self.levelMgr.sceneMgr
    self.resMgr = self.levelMgr.resMgr
    self.cardBuffMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.CardBuffMgr)
end

function level:SetPlayerLua(player)
    self.playerLua = player
    self.playerLua.curLevel = self
    self._ai_nav = true
end

function level:GetUseSoldier(lv)
    return TroopClash_Define.SoldierUnitIDWithLevel[lv]
end

function level:AddEnemy(character)
end

function level:GetAllEnemy()
    return nil
end

--敌人死亡调用，敌人数量为0胜利
function level:RaduceEnemyCount(num, character, dontCheckBuff)
    local fxName = cysoldierssortie_FxName.enemyDie
    local cfg = self.resMgr:GetUnitConfigById(character._unitID)
    if cfg and cfg.DeadSound and cfg.DeadSound ~= "" then
        fxName = cfg.DeadSound
    end
    cysoldierssortie_PlaySfx(fxName, 1)
    if not dontCheckBuff then
        TroopClash_Define.minigame_buff_mgr.CheckCondition(character,
            TroopClash_Define.minigame_buff_mgr.ConditionType.Die)
    end
    return self.sceneMgr:EnemyDead(character)
end

function level:IsRunnerMode()
    return false
end

function level:GetMaxViewEnemyCount()
    return TroopClash_Define.Params.MaxViewActor
end

---获取当前敌人数量
function level:GetCurEnemyCount()
    return 60
end

function level:OnStartCoroutine(func, ...)
    return TroopClash_Define.CS.NeeGame.Instance:StartCoroutine(cs_generator(func, ...))
end

function level:PlaySfx(fxName, cd)
    cysoldierssortie_PlaySfx(fxName, 1)
end

function level:IsEnemyAutoMoveForward()
    return true
end

function level:GetPlayerZ()
    local _, _, z = self.playerLua:GetCenterXYZ()
    return z
end

function level:IsEnterAutoTowerDefence()
    return false
end

function level:PlaySkillEffectRes(effectParam)
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    effectParam.sid = effect_mgr:CreateEffect(effectParam)
end

function level:ReleaseEffect(effect_sid)
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    effect_mgr:ReleaseEffect(effect_sid)
end

function level:LoadPoolRes(resPath, cb)
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not bc_IsNotNull(poolMgr.transform) then
        return
    end
    poolMgr:CreateEntityAsync(resPath, poolMgr.transform, function(go)
        if not bc_IsNotNull(go) then
            return
        end
        if cb then
            cb(go)
        end
    end)
end

function level:AddSpawnEnemy(id, pos, transf, hp, att)
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    ---@type troopclash_teamunit
    local teamUnit = self.sceneMgr.teamCtrl:GetTeamUnitByTransf(transf)
    local newChar = actorMgr:CreateCharacter(id, pos, teamUnit.transform, true)
    teamUnit:AddCharacter(newChar)
end

function level:GetCurMapFogMaskData()
    local renderIndex = TroopClash_Define.SceneDataConfigs[self.resMgr.CurLvConfig.LightIndex]
    if renderIndex then
        return cysoldierssortie_scene_data[renderIndex].fogMask
    end
    return nil
end

function level:TriggerOpenPanelInGame()
    if self.lastBcTimeScale == nil then
        self.lastBcTimeScale = bc_Time.SetTimeScale(0)
    end
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:AddMainLoopCounter()
    end
end

function level:TriggerClosePanelInGame()
    if self.lastBcTimeScale then
        bc_Time.SetTimeScale(self.lastBcTimeScale)
        self.lastBcTimeScale = nil
    end
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:ReduceMainLoopCounter()
    end
end

return level
