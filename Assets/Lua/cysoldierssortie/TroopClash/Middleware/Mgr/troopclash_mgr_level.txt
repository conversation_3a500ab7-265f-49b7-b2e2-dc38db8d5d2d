---@class troopclash_mgr_level : fusion_mgrbase
---@field lifeScope troopclash_lifescope
local mgr = bc_Class("troopclash_mgr_level", Fusion.MgrBase)
---@type troopclash_scene_mgr
mgr.sceneMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type troopclash_ui_mgr
mgr.uiMgr = nil
---@type troopclash_res_mgr
mgr.resMgr = nil
---@type troopclash_mgr_actor
mgr.actorMgr = nil
---@type troopclash_level
mgr.curLevel = nil
mgr.miniLvConfig = nil

local cysoldierssortie_comp_level_dynamic_challenge = require("cysoldierssortie_comp_level_dynamic_challenge")
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName

function mgr:__init(...)
    self.miniLvConfig = ...
end

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.sceneMgr = self.lifeScope:GetMgr("troopclash_scene_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.uiMgr = self.lifeScope:GetMgr("troopclash_ui_mgr")
    self.resMgr = self.lifeScope:GetMgr("troopclash_res_mgr")
    self.actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
end

function mgr:Ready()
    self.curLevel = require("troopclash_level").New(self, self.miniLvConfig, self.uiMgr)
    self.curLevel:SetPlayerLua(self.sceneMgr.playerCtrl)
    TroopClash_Define.minigame_mgr.SetLevelScr(self.curLevel)
    local total = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    total.curLevel = self.curLevel
end

function mgr:GameStart()
    cysoldierssortie_OpenMusicController(self.miniLvConfig.Bgm)
end

--敌人受伤，队伍解散
function mgr:EnemyBeHit(character)
    self.sceneMgr.teamCtrl:EnemyBeHit(character)
end

function mgr:GetAttackLimit(attack)
    -- return attack
    return cysoldierssortie_comp_level_dynamic_challenge:GetAttackLimit(attack)
end

function mgr:GetHpLimit(attack)
    -- return attack
    return cysoldierssortie_comp_level_dynamic_challenge:GetHpLimit(attack)
end

function mgr:SetLevelChallengeDebuff(debuff)
    return cysoldierssortie_comp_level_dynamic_challenge:SetLevelChallengeDebuff(debuff)
end

function mgr:SetCurLevelFailFlag()
    return cysoldierssortie_comp_level_dynamic_challenge:SetCurLevelFailFlag()
end

return mgr
