local cysoldierssortie_mgr_pool = bc_Class("cysoldierssortie_mgr_pool") --类名用小游戏名加后缀保证全局唯一
cysoldierssortie_mgr_pool.dataSrc = nil --gameobject 上 gameluabehaviour组件数据
local bc_load_mgr = require "cysoldierssortie_load_mgr"
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local Time = CS.UnityEngine.Time
local bc_IsNotNull = bc_IsNotNull
local bc_ObjectPool = bc_ObjectPool
local log = log
local bc_Time = bc_Time
local cysoldierssortie_lua_queue = require("cysoldierssortie_lua_queue")
local CSUpdateEvent = cysoldierssortie_EventName.CSUpdate
local typeof = typeof
local BoxCollider = CS.UnityEngine.BoxCollider
local TextType = CS.UnityEngine.UI.Text
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
local Renderer = CS.UnityEngine.Renderer
local SkinnedMeshRenderer = CS.UnityEngine.SkinnedMeshRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer
local cysoldierssortie_entity_loader = require("cysoldierssortie_entity_loader")
local cysoldierssortie_ecs_batch_loader = cysoldierssortie_ecs_batch_loader
local cysoldierssortie_entity_loader_batch = require("cysoldierssortie_entity_loader_batch")
--在编辑器下只加载DefineList,不管其他部分
if ExecuteInEditorScript then
    return cysoldierssortie_mgr_pool
end

local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame

function cysoldierssortie_mgr_pool.__init(self, luaMono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self._waitLoadQueue = cysoldierssortie_lua_queue.New(true)
    self._waitLoadCBQueue = cysoldierssortie_lua_queue.New(true)
    self._waitLoadParentQueue = cysoldierssortie_lua_queue.New(true)
    self._waitLoadObjQueue = cysoldierssortie_lua_queue.New(true)
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_mgr_pool:OnEnable(data)
    if not self.eventMgr then
        return
    end
    self.eventMgr:RegisterEvt(self,CSUpdateEvent)
    
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform
end

function cysoldierssortie_mgr_pool:OnDisable()
    if not self.eventMgr then
        return
    end
    self.eventMgr:UnRegisterEvt(self,CSUpdateEvent)
end

function cysoldierssortie_mgr_pool:OnDestroy()
    self:DisposeObjectPool()
end

function cysoldierssortie_mgr_pool:WarmUpPool(resPath,cb,size)
    local warmup_size = size or 2
    local loadCallBack = function(gameObject)
        if cb then
            cb(gameObject)
        end
        NeeGame.AddNewObjectPool(resPath,gameObject,warmup_size)
    end
    bc_load_mgr.LoadRes(resPath,loadCallBack)
end


local preFrame = -1
local newCreateCount= 0
local newCreateTime = 0
local oldReuseCount= 0
local oldReuseTime = 0

local function reset()
    newCreateCount= 0
    newCreateTime = 0
    oldReuseCount= 0
    oldReuseTime = 0
end

function cysoldierssortie_mgr_pool:SetLayer(go,layer)
    go.gameObject.layer = layer
    local skinMeshRenderers = go:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
    if  skinMeshRenderers and  skinMeshRenderers.Length > 0 then
        for i=0,skinMeshRenderers.Length-1 do
            if  not skinMeshRenderers[i]:IsNull() then
                skinMeshRenderers[i].gameObject.layer =layer
            end
        end
    else
        skinMeshRenderers = go:GetComponentsInChildren(typeof(MeshRenderer))
        for i=0,skinMeshRenderers.Length-1 do
            if  not skinMeshRenderers[i]:IsNull() then
                skinMeshRenderers[i].gameObject.layer =layer
            end
        end
    end
end

local MAX_LOAD_COUNT = 1
function cysoldierssortie_mgr_pool:CSUpdate()
    local uiMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ui)
    if uiMgr then
        uiMgr:ClearCamZ()
    end
    
    if not self._waitLoadQueue then
        return
    end
    local queue_size = self._waitLoadQueue:Size()
    if queue_size <= 0 then
        return
    end
    queue_size = math.min(MAX_LOAD_COUNT,queue_size)
    for i=1,queue_size do
        local resPath =   self._waitLoadQueue:Dequeue()
        if  resPath then
            local cb = nil
            if self._waitLoadCBQueue then
                cb = self._waitLoadCBQueue:Dequeue()
            end

            local parent =nil
            if not self._waitLoadParentQueue then
                parent = self.transform
            else
                parent = self._waitLoadParentQueue:Dequeue()
            end
            local obj = nil
            if self._waitLoadObjQueue then
               obj = self._waitLoadObjQueue:Dequeue()
            end
            self:CreateEntityAsync(resPath,parent,cb,false,false,obj)
        end
    end
end

function cysoldierssortie_mgr_pool:CreateEntityAsync(resPath,parent, cb,normalize,splitFrame,obj,ecs,beforeCB,layer,dontEcsSplitFrame)
    if ecs  then
        if not layer then
            return
        end
        if cysoldierssortie_ecs_batch_loader then
            local ecsRequestID =  cysoldierssortie_entity_loader_batch.RequestInstantiate(resPath,beforeCB,cb,nil,false,layer,dontEcsSplitFrame) 
            return ecsRequestID
        else
            if beforeCB then
                cysoldierssortie_entity_loader.register_before_instantiate_callback(beforeCB)
            end
            cysoldierssortie_entity_loader.register_instantiate_callback(cb)
            cysoldierssortie_entity_loader.instantiate_async(resPath,layer)
        end
        return
    end
    if not normalize then
        normalize = false
    end
    if obj then
        if obj._invalid then
            return
        end
    end
    if splitFrame then
        if not self._waitLoadQueue then
            self._waitLoadQueue = cysoldierssortie_lua_queue.New(true)
        end
        if not self._waitLoadCBQueue then
            self._waitLoadCBQueue = cysoldierssortie_lua_queue.New(true)
        end
        if not self._waitLoadParentQueue then
            self._waitLoadParentQueue = cysoldierssortie_lua_queue.New(true)
        end
        if obj and not self._waitLoadObjQueue then
            self._waitLoadObjQueue = cysoldierssortie_lua_queue.New(true)
        end
        self._waitLoadQueue:Enqueue(resPath)
        self._waitLoadCBQueue:Enqueue(cb)
        self._waitLoadParentQueue:Enqueue(parent)
        if obj then
            self._waitLoadObjQueue:Enqueue(obj)
        end
        return
    end
    local pool_obj = nil
    if isAsync then
        if NeeGame.InAsyncPool(resPath) then
            NeeGame.PoolObjectAsync(resPath, function(pool_obj)
                if cb then
                    cb(pool_obj)
                end
            end, parent,normalize)
        else
            local log = require "log"
            local logName=resPath
            local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
            if totalMgr then
                totalMgr:AddMainLoopCounter()
            end
            local loadCallBack = function(gameObject)
                if not bc_IsNotNull(gameObject) then
                    return
                end
                NeeGame.AddNewObjectAsyncPool(resPath,gameObject)
                pool_obj = NeeGame.PoolObjectAsync(resPath,
                    function(pool_obj)
                        if cb then
                            cb(pool_obj)
                        end
                        if totalMgr then
                            totalMgr:ReduceMainLoopCounter()
                        end
                    end, parent,normalize)
            end
            bc_load_mgr.LoadRes(resPath,loadCallBack)
        end
    else
        if NeeGame.InPool(resPath) then
            pool_obj = NeeGame.PoolObject(resPath, parent,normalize)
        end
        local log = require "log"
        local logName=resPath
        if pool_obj  and cb then
            cb(pool_obj)
        else
            local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
            if totalMgr then
                totalMgr:AddMainLoopCounter()
            end
            local loadCallBack = function(gameObject)
                if not bc_IsNotNull(gameObject) then
                    return
                end
                NeeGame.AddNewObjectPool(resPath,gameObject)
                pool_obj = NeeGame.PoolObject(resPath, parent,normalize)
                if cb then
                    cb(pool_obj)
                end
                if totalMgr then
                    totalMgr:ReduceMainLoopCounter()
                end
            end
            bc_load_mgr.LoadRes(resPath,loadCallBack)
        end
    end
end

function cysoldierssortie_mgr_pool:CreateEntity(resPath,parent, cb,normalize,splitFrame,obj,ecs,beforeCB,layer,dontEcsSplitFrame)
    if ecs  then
        if not layer then
            return
        end
        if cysoldierssortie_ecs_batch_loader then
            local ecsRequestID =  cysoldierssortie_entity_loader_batch.RequestInstantiate(resPath,beforeCB,cb,nil,false,layer,dontEcsSplitFrame)
            return ecsRequestID
        else
            if beforeCB then
                cysoldierssortie_entity_loader.register_before_instantiate_callback(beforeCB)
            end
            cysoldierssortie_entity_loader.register_instantiate_callback(cb)
            cysoldierssortie_entity_loader.instantiate_async(resPath,layer)
        end

        return
    end
    if not normalize then
        normalize = false
    end
    if obj then
        if obj._invalid then
            return
        end
    end
    if splitFrame then
        if not self._waitLoadQueue then
            self._waitLoadQueue = cysoldierssortie_lua_queue.New(true)
        end
        if not self._waitLoadCBQueue then
            self._waitLoadCBQueue = cysoldierssortie_lua_queue.New(true)
        end
        if not self._waitLoadParentQueue then
            self._waitLoadParentQueue = cysoldierssortie_lua_queue.New(true)
        end
        if obj and not self._waitLoadObjQueue then
            self._waitLoadObjQueue = cysoldierssortie_lua_queue.New(true)
        end
        self._waitLoadQueue:Enqueue(resPath)
        self._waitLoadCBQueue:Enqueue(cb)
        self._waitLoadParentQueue:Enqueue(parent)
        if obj then
            self._waitLoadObjQueue:Enqueue(obj)
        end
        return
    end
    local pool_obj = nil
    if NeeGame.InPool(resPath) then
        pool_obj = NeeGame.PoolObject(resPath, parent,normalize)
    end
    local log = require "log"
    local logName=resPath
    if pool_obj  and cb then
        cb(pool_obj)
    else
        local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
        if totalMgr then
            totalMgr:AddMainLoopCounter()
        end
        local loadCallBack = function(gameObject)
            if not bc_IsNotNull(gameObject) then
                return
            end
            NeeGame.AddNewObjectPool(resPath,gameObject)
            pool_obj = NeeGame.PoolObject(resPath, parent,normalize)
            if cb then
                cb(pool_obj)
            end
            if totalMgr then
                totalMgr:ReduceMainLoopCounter()
            end
        end
        bc_load_mgr.LoadRes(resPath,loadCallBack)
    end
end


function cysoldierssortie_mgr_pool:DisposeEntity(instanceID)
    if instanceID~=0 then
        cysoldierssortie_entity_loader.Dispose(instanceID)
    end
end

function cysoldierssortie_mgr_pool:GetMainLoopState()
    return self._main_loop_state_changed or false
end

function cysoldierssortie_mgr_pool:Update()
    
end

function cysoldierssortie_mgr_pool:Init()
    
end

function cysoldierssortie_mgr_pool:WarmUp(strFileName,num,...)
    if  not self._object_pools then
        self._object_pools = {}
    end
    if not self._object_pools[strFileName] then
        local objectPool = bc_ObjectPool.New(strFileName)
        self._object_pools[strFileName] = objectPool
    end
    local objectPool = self._object_pools[strFileName]
    objectPool:WarmUp(num,...)
end

--Object Pool
function cysoldierssortie_mgr_pool:AcquireObj(strFileName,...)
    if  not self._object_pools then
        self._object_pools = {}
    end
    
    if not self._object_pools[strFileName] then
        local objectPool = bc_ObjectPool.New(strFileName)
        self._object_pools[strFileName] = objectPool
    end
    local objectPool = self._object_pools[strFileName]
    local object = objectPool:Acquire(...)
    return object
end

function cysoldierssortie_mgr_pool:AcquireObjAsync(strFileName, cb, ...)
    if  not self._object_pools then
        self._object_pools = {}
    end
    
    if not self._object_pools[strFileName] then
        local objectPool = bc_ObjectPool.New(strFileName)
        self._object_pools[strFileName] = objectPool
    end
    local objectPool = self._object_pools[strFileName]

    objectPool:AcquireAsync(cb,...)
end

function cysoldierssortie_mgr_pool:ReleaseObj(obj)
    local name = obj._class_type.__cname
    if name then
        if self._object_pools and self._object_pools[name] then
            local objectPool = self._object_pools[name]
            objectPool:Release(obj)
        else
            print("ld: ReleaseObj error, not found object pool",name)
        end
    else
        log.Error("[MiniGame]Error ReleaseObj obj.__cname == nil")
    end
end

function cysoldierssortie_mgr_pool:DisposeObjectPool()
    if self._object_pools then
        for k,v in pairs(self._object_pools) do
            local objectPool = self._object_pools[k]
            objectPool:Clean()
        end
        self._object_pools = nil
    end
end

function cysoldierssortie_mgr_pool:GetTransform(go)
    if not self._transformCache then
        self._transformCache = {}
    end
    local res = rawget(self._transformCache,go)
    if not res then
        res = go.transform
        rawset( self._transformCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetRenderer(go)
    if not self._rendererCache then
        self._rendererCache = {}
    end
    local res = rawget(self._rendererCache,go)
    if not res then
        res =  go:GetComponent(typeof(Renderer))
        rawset( self._rendererCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetModelRoot(go)
    if not self._modelRootCache then
        self._modelRootCache = {}
    end
    local res = rawget(self._modelRootCache,go)
    if not res then
        res = go.transform:Find("ModelRoot")
        rawset( self._modelRootCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetModelRootGo(go)
    if not self._modelRootGoCache then
        self._modelRootGoCache = {}
    end
    local res = rawget(self._modelRootGoCache,go)
    if not res then
        res = go.transform:Find("ModelRoot").gameObject
        rawset( self._modelRootGoCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetCollider(go)
    if not self._colliderCache then
        self._colliderCache = {}
    end
    
    local res = rawget(self._colliderCache,go)
    if not res then
        res = go:GetComponent(typeof(BoxCollider))
        rawset( self._colliderCache,go,res)
    end
    
    return res
end

local bc_CS_ColListener = bc_CS_ColListener
function cysoldierssortie_mgr_pool:ColliderListener(go)
    if not self._colliderListenerCache then
        self._colliderListenerCache = {}
    end

    local res = rawget(self._colliderListenerCache,go)
    if not res then
        res = go:GetComponent(typeof(bc_CS_ColListener))
        rawset( self._colliderListenerCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetRfNumPos(go)
    if not self._rfNumPosCache then
        self._rfNumPosCache = {}
    end
    local res = rawget(self._rfNumPosCache,go)
    if not res then
        res = go.transform:Find("rfNumPos").gameObject
        rawset( self._rfNumPosCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetReleaseSkillPoint(go)
    if not self._releaseSkillPointCache then
        self._releaseSkillPointCache = {}
    end
    local res = rawget(self._releaseSkillPointCache,go)
    if not res then
        res = go.transform:Find("ReleaseSkillPoint").gameObject
        rawset( self._releaseSkillPointCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetHpPoint(go)
    if not self._hpPointCache then
        self._hpPointCache = {}
    end
    local res = rawget(self._hpPointCache,go)
    if not res then
        res = go.transform:Find("HpPoint").gameObject
        rawset( self._hpPointCache,go,res)
    end

    return res
end

function cysoldierssortie_mgr_pool:GetHpAnimText(go)
    if not self._hpAnimTextCache then
        self._hpAnimTextCache = {}
    end
    local res = rawget(self._hpAnimTextCache,go)
    if not res then
        res = go:GetComponentInChildren(typeof(TextType))
        rawset(self._hpAnimTextCache,go,res)
    end
    return res
end

function cysoldierssortie_mgr_pool:GetHpAnimTextMesh(go)
    if not self._hpAnimTextCache then
        self._hpAnimTextCache = {}
    end
    local res = rawget(self._hpAnimTextCache,go)
    if not res then
        res = go:GetComponentInChildren(typeof(TextMeshProUGUI))
        rawset(self._hpAnimTextCache,go,res)
    end
    return res
end

return cysoldierssortie_mgr_pool