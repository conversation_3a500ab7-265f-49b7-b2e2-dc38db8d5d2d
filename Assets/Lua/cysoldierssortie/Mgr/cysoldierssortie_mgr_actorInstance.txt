local cysoldierssortie_mgr_actorInstance = bc_Class("cysoldierssortie_mgr_pool") --类名用小游戏名加后缀保证全局唯一
local game_scheme 	= require "game_scheme"
local cysoldierssortie_comp_character = require "cysoldierssortie_comp_character"
local table = table
local minigame_buff_mgr= require "minigame_buff_mgr"
local cysoldierssortie_lst_group_type = cysoldierssortie_lst_group_type
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem
local LookAtTargetSystemInstance
local log = log
local bc_Time = bc_Time
local cysoldierssortie_lua_queue = require("cysoldierssortie_lua_queue")
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local minigame_mgr = require "minigame_mgr"
local typeof = typeof
local SkinnedMeshRenderer = CS.UnityEngine.SkinnedMeshRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer
local bc_IsNotNull =bc_IsNotNull
local UnityEngine = CS.UnityEngine
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local xpcall = xpcall
local debug = debug
local cysoldierssortie_comp_name = cysoldierssortie_comp_name

function cysoldierssortie_mgr_actorInstance.__init(self, luaMono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
    local res = xpcall(function()
        LookAtTargetSystemInstance = LookAtTargetSystem.Instance
    end,debug.traceback)
    self._waitLoadLst = cysoldierssortie_lua_queue.New()
    self._waitLoadHeroLst = cysoldierssortie_lua_queue.New()
    self.cysoldierssortie_TroopClash = cysoldierssortie_TroopClash
    self.cysoldierssortie_KingShot = cysoldierssortie_KingShot or cysoldierssortie_KingShot_HOME
    self.TroopClash_Define = TroopClash_Define
    self.KingShot_Define = KingShot_Define
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_mgr_actorInstance:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform
end

function cysoldierssortie_mgr_actorInstance:InitData()
    --最大实体数量显示
    self._max_view_actor = self.cysoldierssortie_TroopClash and self.TroopClash_Define.Params.MaxViewActor or 60
    --当前实体显示的实体数量
    self._view_actor = 0
    --等待实体显示队列
    self._view_actor_queue = {}
    --临时记录下boss
    self._boss_lst = {}
    --拥有预警组件的actor
    self:ResetEarlyWarning()
    --生成唯一sid
    self._sid = 1
end

function cysoldierssortie_mgr_actorInstance:Start()
    self:InitData()
end

function cysoldierssortie_mgr_actorInstance:ResetEarlyWarning()
    --拥有预警组件的actor
    self._early_warning_actor = {}
end

function cysoldierssortie_mgr_actorInstance:AddEarlyWarningActor(actor,startPos)
    self._early_warning_actor[#self._early_warning_actor+1] = {actor = actor,startPos = startPos}
end

function cysoldierssortie_mgr_actorInstance:GenerateSid()
    self._sid = self._sid +1
    return self._sid
end

function cysoldierssortie_mgr_actorInstance:AddEarlyWarningComp()
    if not self._early_warning_actor and #self._early_warning_actor>0 then
        return
    end
    for i=#self._early_warning_actor,1,-1 do
        local actor =  self._early_warning_actor[i].actor
        local startPos =  self._early_warning_actor[i].startPos
        actor:AddComponent(cysoldierssortie_comp_name.EarlyWarning,{character = actor,startPos = startPos})
        table.remove(self._early_warning_actor,i)
    end
end


function cysoldierssortie_mgr_actorInstance:CreateCharacterData(unitId,localPos,heroID,_hp,_attack,_player)
    local curHeroId = 0
    local unit_type = cysoldierssortie_unit_type.NormalEnemy
    local hp = 100
    local atk = 2.5
    local atkRange = 1
    local moveSpeed = 10
    local enemyRange = 15
    local scale = 1
    local hitDeceleration = 0
    local bloodOffset = 0
    local lst_group_type = cysoldierssortie_lst_group_type.HeroLst
    local level = 1
    self.LevelMgr = self.LevelMgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local level_atk_increase_ratio =self.LevelMgr.curLevel.MiniLevelCfg.AtkIncreaseRatio
    local atkIncreaseRatioByLevel =level_atk_increase_ratio or  0        --管卡士兵攻击增幅表
    local weapons = {}
    local PassiveSkills = {}
    local sid = self:GenerateSid()
    local rewardCoin = 0
    local soldierDpsCoe = 0
    local hpScale = 1
    local patrol = false
    if unitId then
        local unit_cfg = nil
        if self.cysoldierssortie_TroopClash or self.cysoldierssortie_KingShot then
            unit_cfg = self.LevelMgr.resMgr:GetUnitConfigById(unitId)
        else
            unit_cfg = game_scheme:MiniUnit_0(unitId)
        end
        if unit_cfg then
            unit_type = unit_cfg.UnitType
            bloodOffset=unit_cfg.BloodOffset or 0
            atkRange = unit_cfg.AtkRange
            moveSpeed = unit_cfg.MoveSpeed
            enemyRange = unit_cfg.EnemyRange

            if self.cysoldierssortie_TroopClash then
                if self.cysoldierssortie_TroopClash then
                    atkRange = _player and self.TroopClash_Define.Params.PlayerAtkRange or unit_cfg.AtkRange
                    enemyRange = _player and self.TroopClash_Define.Params.PlayerSearchRange or self.TroopClash_Define.Params.EnemySearchRange
                else
                    
                end
            end
            if self.cysoldierssortie_KingShot then
                atkRange = _player and self.KingShot_Define.Params.PlayerAtkRange or unit_cfg.AtkRange
                enemyRange = _player and self.KingShot_Define.Params.PlayerSearchRange or self.KingShot_Define.Params.EnemySearchRange
            end
            
            scale = unit_cfg.Scale / 100
            hitDeceleration =  unit_cfg.HitDeceleration
            level=unit_cfg.UnitLevel
            curHeroId = heroID or unit_cfg.ModelID
            bloodOffset=unit_cfg.BloodOffset or 0
            if _hp and _hp> 0 then
                hp = _hp
            else
                hp = unit_cfg.HP 
            end
            
            if _attack and _attack > 0 then
                atk = _attack
            else
                atk =  unit_cfg.ATK
            end
            
            if unit_cfg.Patrol and unit_cfg.Patrol > 0 then
                patrol = true
            end
            
            lst_group_type = cysoldierssortie_lst_group_type.SoldierLst
            local skillID = unit_cfg.SkillID
            rewardCoin = unit_cfg.coin
            soldierDpsCoe = unit_cfg.SoldierDpsCoe
            if unit_cfg.HpScale and unit_cfg.HpScale > 1 then
                hpScale = unit_cfg.HpScale / 100
            end
            if skillID and skillID.data then
                local skill_data_array = skillID.data
                if skill_data_array[0] then
                    for k=0,#skill_data_array do
                        local weapon = {}
                        local skill_cfg = nil
                        if self.cysoldierssortie_TroopClash or self.cysoldierssortie_KingShot then
                            skill_cfg = self.LevelMgr.resMgr:GetSkillConfigById(skill_data_array[k])
                        else
                            skill_cfg = game_scheme:MiniSkill_0(skill_data_array[k])
                        end
                        weapon._attackType = skill_cfg.AttackType
                        weapon._damageCoefficient = skill_cfg.DamageCoefficient
                        weapon._attackSpeed = skill_cfg.AttackSpeed / 10000                 --冷却时间
                        weapon._skillLoop =   skill_cfg.SkillLoop and (skill_cfg.SkillLoop / 10000) or 1                     --技能生命周期
                        weapon._skillPriority = skill_cfg.SkillPriority and  skill_cfg.SkillPriority or 1
                        weapon._criticalHit = skill_cfg.CriticalHit
                        weapon._damageRange = skill_cfg.lockRange
                        weapon._ballisiticVelocity = skill_cfg.BallisticVelocity
                        weapon._ballisiticRange = skill_cfg.BallisticRange
                        weapon._pierce = skill_cfg.pierce
                        weapon._skillEffectPath = skill_cfg.SpecialEffectsPath
                        weapon._showEffectsPath = skill_cfg.ShowEffectsPath
                        if skill_cfg.DstEffectsPath ~= "" then
                            weapon._dstEffectsPath =  skill_cfg.DstEffectsPath
                        end
                        weapon._bulletScale = skill_cfg.SpecialEffectScaling > 0 and skill_cfg.SpecialEffectScaling / 100 or 1
                        weapon._skillID = skill_data_array[k]
                        weapon._startCD = skill_cfg.startCD
                        weapon._maxDamageNum = skill_cfg.MaxDamageNumber or 0
                        weapon._nBuffIDs = skill_cfg.nBuffIDs
                        weapon._nSkillRepeatCnt = skill_cfg.nSkillRepeatCnt > 0 and skill_cfg.nSkillRepeatCnt or 1
                        weapon._nTriggerProb = skill_cfg.nTriggerProb
                        weapon._repeatMinInterval = skill_cfg.RepeatMinInterval > 0 and skill_cfg.RepeatMinInterval/10000 or 0
                        weapon._overrideAtkRange = skill_cfg.OverrideAtkRange / 100
                        if  skill_cfg.strAttackAction and skill_cfg.strAttackAction > 0 then
                            weapon._strAttackAction = "Skill0"..skill_cfg.strAttackAction
                        end

                        if skill_cfg.IsUltra then
                            weapon._isUltra = skill_cfg.IsUltra > 0 and true or false
                        end
                        --星级增幅
                        if skill_cfg.StarRatingIncrease then
                            weapon._starRatingIncrease = skill_cfg.StarRatingIncrease
                        end
                        if skill_cfg.DamageType and skill_cfg.DamageType < 1 then
                            weapon._damageType = 1
                        else
                            weapon._damageType = skill_cfg.DamageType 
                        end

                        if skill_cfg.TypeParameter1 and skill_cfg.TypeParameter1 > 0 then
                            weapon._typeParameter1 = skill_cfg.TypeParameter1
                        end

                        if skill_cfg.TypeParameter2 and skill_cfg.TypeParameter2 > 0 then
                            weapon._typeParameter2 = skill_cfg.TypeParameter2
                        end

                        if skill_cfg.TypeParameter3 and skill_cfg.TypeParameter3 > 0 then
                            weapon._typeParameter3 = skill_cfg.TypeParameter3
                        end

                        if self.cysoldierssortie_TroopClash then
                            -- 地面预警，提前X秒
                            weapon._attent = skill_cfg.Attent / 1000
                        end

                        weapons[#weapons+1] = weapon
                    end
                end
            end

            --被动技能
            if self.cysoldierssortie_TroopClash or self.cysoldierssortie_KingShot then
                local pSkillID = unit_cfg.PassiveSkills
                if pSkillID and pSkillID.data then
                    local dataArray = pSkillID.data
                    for k = 0, #dataArray do
                        PassiveSkills[k + 1] = self.LevelMgr.resMgr:GetPassiveSkillConfigById(dataArray[k])
                    end
                end
            end
        end
    end
    
    local data = 
    {
        Level = level,   Attack = atk,  HP = hp,    HeroID = curHeroId,
        Star=1, player = _player,   UnitType = unit_type,   AttackRange = atkRange, 
        MoveSpeed = moveSpeed,  EnemyRange = enemyRange,    LocalPos = localPos,    UnitID = unitId, 
        Scale = scale,  HitDeceleration = hitDeceleration,  BloodOffset = bloodOffset,  Sid = sid,
        AtkIncreaseRatioByLevel = atkIncreaseRatioByLevel,  LstGroupType = lst_group_type, RewardCoin = rewardCoin,SoldierDpsCoe = soldierDpsCoe,
        HpScale = hpScale, Patrol = patrol,
        Weapons = weapons, PassiveSkills = PassiveSkills,
    }
    return data
end

--unitId 单位id， parent,forceView,heroID(英雄id),_hp,_attack,_player
function cysoldierssortie_mgr_actorInstance:EnqueueWaitLoadModel(character)
    if not character._player then
        self._waitLoadLst = self._waitLoadLst or {}
        if not self._waitLoadLst then
            self._waitLoadLst = cysoldierssortie_lua_queue.New()
        end
        self._waitLoadLst:Enqueue(character)
    else
        if not self._waitLoadHeroLst then
            self._waitLoadHeroLst = cysoldierssortie_lua_queue.New()
        end
        self._waitLoadHeroLst:Enqueue(character)
    end
end

local spawnInterval = 0.03333
function cysoldierssortie_mgr_actorInstance:Update()
    if cysoldierssortie_urp_ecs and entity_manager.URP22 then
        return
    end
    if not self._updateTimer or bc_Time.time > self._updateTimer then
        self._updateTimer = spawnInterval + bc_Time.time
        self:DequeueHeroWaitLoadModel()
        if not minigame_mgr.GetIsStartGame() then
            return
        end
        self:DequeueWaitLoadModel() 
    end
end

local boundSizeCache = {}
function cysoldierssortie_mgr_actorInstance:CalActorBoundSize(resPath,scale,go)
    self._boundSizeMap = self._boundSizeMap or {}
    cysoldierssortie_lua_util:Append(resPath)
    cysoldierssortie_lua_util:Append(tostring(scale))
    local key = cysoldierssortie_lua_util:ToString()
    if self._boundSizeMap[key] then
        if not cysoldierssortie_urp_ecs or not entity_manager.URP22 then
            local skinMeshRenderers = go:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
            if  skinMeshRenderers and  skinMeshRenderers.Length > 0 then
                    for i=0,skinMeshRenderers.Length-1 do
                        if bc_IsNotNull(skinMeshRenderers[i]) then
                            skinMeshRenderers[i].shadowCastingMode =  UnityEngine.Rendering.ShadowCastingMode.Off
                        end
                    end
            else
                skinMeshRenderers = go:GetComponentsInChildren(typeof(MeshRenderer))
                if skinMeshRenderers and  skinMeshRenderers.Length > 0 then
                        for i=0,skinMeshRenderers.Length-1 do
                            if bc_IsNotNull(skinMeshRenderers[i]) then
                                skinMeshRenderers[i].shadowCastingMode =  UnityEngine.Rendering.ShadowCastingMode.Off
                            end
                        end
                    end
                end
        end
        return self._boundSizeMap[key]
    end
    
    local CalBoundData = function(bounds)
        local tmp_size = bounds.size
        if tmp_size.x > tmp_size.z then
            if tmp_size.x > tmp_size.y then
                local temp = tmp_size.y
                tmp_size.y = tmp_size.x
                tmp_size.x = temp
            end
        else
            if tmp_size.z > tmp_size.y then
                local temp = tmp_size.y
                tmp_size.y = tmp_size.z
                tmp_size.z = temp
            end
        end

        return tmp_size
    end
    boundSizeCache.x = 1
    boundSizeCache.y = 1
    boundSizeCache.z = 1
    local skinMeshRenderers = go:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
    if  skinMeshRenderers and  skinMeshRenderers.Length > 0 then
        boundSizeCache = CalBoundData(skinMeshRenderers[0].bounds)
        if not cysoldierssortie_urp_ecs or not entity_manager.URP22 then
            for i=0,skinMeshRenderers.Length-1 do
                if bc_IsNotNull(skinMeshRenderers[i]) then
                    skinMeshRenderers[i].shadowCastingMode =  UnityEngine.Rendering.ShadowCastingMode.Off
                end
            end
        end
    else
        skinMeshRenderers = go:GetComponentsInChildren(typeof(MeshRenderer))
        if skinMeshRenderers and  skinMeshRenderers.Length > 0 then
            boundSizeCache = CalBoundData(skinMeshRenderers[0].bounds)
            if not cysoldierssortie_urp_ecs or not entity_manager.URP22 then
                for i=0,skinMeshRenderers.Length-1 do
                    if bc_IsNotNull(skinMeshRenderers[i]) then
                        skinMeshRenderers[i].shadowCastingMode =  UnityEngine.Rendering.ShadowCastingMode.Off
                    end
                end
            end
        end
    end
    boundSizeCache.x = boundSizeCache.x * scale
    boundSizeCache.y = boundSizeCache.y * scale
    boundSizeCache.z = boundSizeCache.z * scale
    self._boundSizeMap[key] = {x=boundSizeCache.x,y=boundSizeCache.y,z=boundSizeCache.z}
    return self._boundSizeMap[key]
end

function cysoldierssortie_mgr_actorInstance:DequeueWaitLoadModel()
    if not self._waitLoadLst then
        return 
    end
    
    local character = self._waitLoadLst:Dequeue()
    if character then
        character._character_entity:CreateModel()
    end
end

function cysoldierssortie_mgr_actorInstance:DequeueHeroWaitLoadModel()
    if not self._waitLoadHeroLst then
        return
    end

    local character = self._waitLoadHeroLst:Dequeue()
    if character then
        character._character_entity:CreateModel()
    end
end

---@param elRushFlag boolean 标记是无尽模式冲锋生成的敌人
function cysoldierssortie_mgr_actorInstance:CreateCharacter(unitId,localPos,parent,forceView,heroID,_hp,_attack,_player,playEffect,elRushFlag)
    local data = self:CreateCharacterData(unitId,localPos,heroID,_hp,_attack,_player)
    local character = cysoldierssortie_comp_character.New()
    character:CreateData(data)
    character:CreateEntity(parent,playEffect or false)
    if not elRushFlag and (not cysoldierssortie_urp_ecs or not entity_manager.URP22) then
        self:EnqueueWaitLoadModel(character)
    else
        if character._character_entity then
            character._character_entity:CreateModel()
        end
    end

    self.LevelMgr = self.LevelMgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel =  self.LevelMgr.curLevel
    if not character._player then
        --先处理敌人
        if not elRushFlag then
            curLevel:AddEnemy(character)
        end
        if self.cysoldierssortie_TroopClash or self.cysoldierssortie_KingShot then
            minigame_buff_mgr.AddBuffCfgByPassiveSkillConfig(unitId, character, character._passiveSkills)
        else
            minigame_buff_mgr.AddBuffCfg(unitId,character)
        end
        minigame_buff_mgr.CheckCondition(character,minigame_buff_mgr.ConditionType.Incubate)
        if not forceView and not elRushFlag then
            self:EnqueueActor(character)
        end
        if not elRushFlag then
            local res = xpcall(function()
                LookAtTargetSystemInstance:RegisterEnemy(character.transform, character._enemyRange)
            end, debug.traceback)
        end
    else
        if  character._isDrone then--神兽
            return character
        end
        local enemyRange = character._enemyRange
        if curLevel:IsEnterAutoTowerDefence() or curLevel._isStartedLevelAcceleration then
            enemyRange = 1000 
        end
        local res = xpcall(function()
            LookAtTargetSystemInstance:RegisterSoldier(character.transform,enemyRange)
        end,debug.traceback)
    end

    return character
end

function cysoldierssortie_mgr_actorInstance:EnqueueActor(character)
    local maxViewActor = 60
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    local curLevel = levelMgr.curLevel
    if curLevel then
        maxViewActor =  curLevel:GetMaxViewEnemyCount()
    end
    if self._view_actor > maxViewActor then
        character:SetViewActor(false)
        self._view_actor_queue[#self._view_actor_queue+1] = character
    else
        character:SetViewActor(true)
        self._view_actor = self._view_actor + 1
    end
end

function cysoldierssortie_mgr_actorInstance:DequeueActor(character)
    if character._view_actor == true then
        self._view_actor = self._view_actor - 1
        
        if #self._view_actor_queue <=0 then
            return
        end
        local actor = self._view_actor_queue[1]
        actor:SetViewActor(true)
        table.remove(self._view_actor_queue,1)
    else
        table.remove_value(self._view_actor_queue,character)
    end
end


return cysoldierssortie_mgr_actorInstance